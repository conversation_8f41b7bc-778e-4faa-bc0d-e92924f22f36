-- 戰鬥服務 - 處理戰鬥系統的網路通信
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local CombatService = Knit.CreateService({
    Name = "CombatService",
    Client = {
        -- 客戶端可調用的方法
        AttackMonster = Knit.CreateSignal(),
        StartCombat = Knit.CreateSignal(),
        EndCombat = Knit.CreateSignal(),
        GetCombatStatus = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnCombatStarted = Knit.CreateSignal(),
        OnCombatEnded = Knit.CreateSignal(),
        OnDamageDealt = Knit.CreateSignal(),
        OnDamageReceived = Knit.CreateSignal(),
        OnMonsterDefeated = Knit.CreateSignal(),
        OnPlayerDefeated = Knit.CreateSignal(),
        OnExperienceGained = Knit.CreateSignal(),
        OnRewardReceived = Knit.CreateSignal()
    }
})

function CombatService:KnitInit()
    self._worldManager = _G.WorldManager
    self._world = _G.ECSWorld
    print("⚔️ CombatService 初始化完成")
end

function CombatService:KnitStart()
    print("🎮 CombatService 啟動")
    
    -- 監聽戰鬥系統事件
    self:setupCombatEventListeners()
end

-- 設置戰鬥事件監聽器
function CombatService:setupCombatEventListeners()
    -- 這裡可以添加對 CombatSystem 事件的監聽
    -- 例如：當怪物被擊敗時自動通知客戶端
end

-- 客戶端方法：開始戰鬥
function CombatService.Client:StartCombat(player, monsterId)
    return self.Server:StartCombat(player, monsterId)
end

function CombatService:StartCombat(player, monsterId)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    -- 檢查怪物是否存在
    if not self._world:contains(monsterId) then
        return { success = false, error = "怪物不存在" }
    end
    
    local monster = self._world:get(monsterId, Components.MonsterComponent)
    if not monster then
        return { success = false, error = "無效的怪物" }
    end
    
    -- 檢查玩家是否已在戰鬥中
    local playerCombat = self._world:get(playerEntity, Components.CombatComponent)
    if playerCombat and playerCombat.isInCombat then
        return { success = false, error = "已在戰鬥中" }
    end
    
    -- 檢查怪物是否已在戰鬥中
    local monsterCombat = self._world:get(monsterId, Components.CombatComponent)
    if monsterCombat and monsterCombat.isInCombat then
        return { success = false, error = "怪物已在戰鬥中" }
    end
    
    -- 檢查距離 (可選)
    local playerTransform = self._world:get(playerEntity, Components.TransformComponent)
    local monsterTransform = self._world:get(monsterId, Components.TransformComponent)
    
    if playerTransform and monsterTransform then
        local distance = (playerTransform.position - monsterTransform.position).Magnitude
        if distance > 50 then -- 50 studs 攻擊範圍
            return { success = false, error = "距離太遠" }
        end
    end
    
    -- 使用 CombatSystem 開始戰鬥
    local combatSystem = self._worldManager:getSystem("CombatSystem")
    if not combatSystem then
        return { success = false, error = "戰鬥系統未找到" }
    end
    
    local success = combatSystem:startCombat(self._world, playerEntity, monsterId)
    if not success then
        return { success = false, error = "戰鬥開始失敗" }
    end
    
    -- 通知客戶端戰鬥開始
    self.Client.OnCombatStarted:Fire(player, {
        monsterId = monsterId,
        monsterSpecies = monster.speciesId,
        timestamp = tick()
    })
    
    print("⚔️ 戰鬥開始:", player.Name, "vs", monster.speciesId)
    
    return {
        success = true,
        monsterId = monsterId,
        combatStartTime = tick()
    }
end

-- 客戶端方法：攻擊怪物
function CombatService.Client:AttackMonster(player)
    return self.Server:AttackMonster(player)
end

function CombatService:AttackMonster(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    -- 檢查玩家是否在戰鬥中
    local combat = self._world:get(playerEntity, Components.CombatComponent)
    if not combat or not combat.isInCombat then
        return { success = false, error = "不在戰鬥中" }
    end
    
    -- 檢查攻擊冷卻
    local currentTime = tick()
    if currentTime - combat.lastAttackTime < GameConfig.COMBAT.TURN_DURATION then
        local remainingCooldown = GameConfig.COMBAT.TURN_DURATION - (currentTime - combat.lastAttackTime)
        return { 
            success = false, 
            error = "攻擊冷卻中",
            remainingCooldown = remainingCooldown
        }
    end
    
    -- 手動觸發一次攻擊 (CombatSystem 會在下一幀處理)
    self._world:insert(playerEntity, Components.CombatComponent({
        isInCombat = combat.isInCombat,
        target = combat.target,
        lastAttackTime = currentTime - GameConfig.COMBAT.TURN_DURATION, -- 立即允許攻擊
        combatStartTime = combat.combatStartTime
    }))
    
    return {
        success = true,
        message = "攻擊指令已發送"
    }
end

-- 客戶端方法：結束戰鬥
function CombatService.Client:EndCombat(player)
    return self.Server:EndCombat(player)
end

function CombatService:EndCombat(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    -- 檢查玩家是否在戰鬥中
    local combat = self._world:get(playerEntity, Components.CombatComponent)
    if not combat or not combat.isInCombat then
        return { success = false, error = "不在戰鬥中" }
    end
    
    -- 使用 CombatSystem 結束戰鬥
    local combatSystem = self._worldManager:getSystem("CombatSystem")
    if combatSystem then
        combatSystem:endCombat(self._world, playerEntity, "player_retreat")
    end
    
    -- 通知客戶端戰鬥結束
    self.Client.OnCombatEnded:Fire(player, {
        reason = "player_retreat",
        timestamp = tick()
    })
    
    print("🏃 玩家逃跑:", player.Name)
    
    return {
        success = true,
        reason = "player_retreat"
    }
end

-- 客戶端方法：獲取戰鬥狀態
function CombatService.Client:GetCombatStatus(player)
    return self.Server:GetCombatStatus(player)
end

function CombatService:GetCombatStatus(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local combat = self._world:get(playerEntity, Components.CombatComponent)
    local health = self._world:get(playerEntity, Components.HealthComponent)
    local hero = self._world:get(playerEntity, Components.HeroComponent)
    
    local status = {
        isInCombat = combat and combat.isInCombat or false,
        target = combat and combat.target or nil,
        lastAttackTime = combat and combat.lastAttackTime or 0,
        combatStartTime = combat and combat.combatStartTime or 0,
        currentHealth = health and health.current or 0,
        maxHealth = hero and hero.maxHealth or 0,
        attack = hero and hero.attack or 0,
        defense = hero and hero.defense or 0
    }
    
    -- 如果在戰鬥中，獲取目標信息
    if status.isInCombat and status.target then
        local targetHealth = self._world:get(status.target, Components.HealthComponent)
        local targetMonster = self._world:get(status.target, Components.MonsterComponent)
        
        if targetHealth and targetMonster then
            status.targetInfo = {
                speciesId = targetMonster.speciesId,
                currentHealth = targetHealth.current,
                maxHealth = targetHealth.maximum
            }
        end
    end
    
    return {
        success = true,
        status = status
    }
end

-- 通知客戶端傷害事件
function CombatService:notifyDamageDealt(player, damage, target)
    self.Client.OnDamageDealt:Fire(player, {
        damage = damage,
        target = target,
        timestamp = tick()
    })
end

function CombatService:notifyDamageReceived(player, damage, source)
    self.Client.OnDamageReceived:Fire(player, {
        damage = damage,
        source = source,
        timestamp = tick()
    })
end

-- 通知客戶端怪物被擊敗
function CombatService:notifyMonsterDefeated(player, monster, rewards)
    self.Client.OnMonsterDefeated:Fire(player, {
        monsterSpecies = monster.speciesId,
        rewards = rewards,
        timestamp = tick()
    })
    
    -- 同時觸發經驗值和獎勵事件
    if rewards.experience > 0 then
        self.Client.OnExperienceGained:Fire(player, {
            amount = rewards.experience,
            source = "combat",
            timestamp = tick()
        })
    end
    
    if rewards.coins > 0 then
        self.Client.OnRewardReceived:Fire(player, {
            type = "coins",
            amount = rewards.coins,
            source = "combat",
            timestamp = tick()
        })
    end
end

-- 通知客戶端玩家被擊敗
function CombatService:notifyPlayerDefeated(player)
    self.Client.OnPlayerDefeated:Fire(player, {
        timestamp = tick()
    })
end

-- 獲取附近的怪物
function CombatService:getNearbyMonsters(playerEntity, range)
    range = range or 100 -- 默認範圍 100 studs
    
    local playerTransform = self._world:get(playerEntity, Components.TransformComponent)
    if not playerTransform then
        return {}
    end
    
    local nearbyMonsters = {}
    
    for entityId, monster, transform in self._world:query(
        Components.MonsterComponent,
        Components.TransformComponent
    ) do
        local distance = (playerTransform.position - transform.position).Magnitude
        if distance <= range then
            table.insert(nearbyMonsters, {
                entityId = entityId,
                monster = monster,
                distance = distance,
                position = transform.position
            })
        end
    end
    
    -- 按距離排序
    table.sort(nearbyMonsters, function(a, b)
        return a.distance < b.distance
    end)
    
    return nearbyMonsters
end

-- 檢查玩家是否可以攻擊
function CombatService:canPlayerAttack(playerEntity)
    local combat = self._world:get(playerEntity, Components.CombatComponent)
    local health = self._world:get(playerEntity, Components.HealthComponent)
    
    -- 檢查是否在戰鬥中
    if not combat or not combat.isInCombat then
        return false, "不在戰鬥中"
    end
    
    -- 檢查血量
    if not health or health.current <= 0 then
        return false, "血量不足"
    end
    
    -- 檢查攻擊冷卻
    local currentTime = tick()
    if currentTime - combat.lastAttackTime < GameConfig.COMBAT.TURN_DURATION then
        return false, "攻擊冷卻中"
    end
    
    return true
end

-- 計算戰鬥獎勵
function CombatService:calculateCombatRewards(monster, playerLevel)
    local baseExp = 10
    local baseCoins = 50
    
    -- 根據怪物等級調整獎勵
    local monsterLevel = 1 -- 可以從怪物數據中獲取
    local levelDifference = monsterLevel - playerLevel
    
    -- 等級差異獎勵調整
    local expMultiplier = 1.0
    local coinMultiplier = 1.0
    
    if levelDifference > 0 then
        expMultiplier = 1.0 + (levelDifference * 0.1) -- 挑戰高等級怪物獲得更多經驗
        coinMultiplier = 1.0 + (levelDifference * 0.05)
    elseif levelDifference < -5 then
        expMultiplier = 0.5 -- 挑戰低等級怪物獲得較少經驗
        coinMultiplier = 0.5
    end
    
    return {
        experience = math.floor(baseExp * expMultiplier),
        coins = math.floor(baseCoins * coinMultiplier)
    }
end

return CombatService
