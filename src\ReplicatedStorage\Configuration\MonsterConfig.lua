-- 副本BOSS和寵物獎勵配置系統
-- 基於現有97個模型重新設計為副本BOSS獎勵

local MonsterConfig = {}

-- 寵物稀有度配置 (作為副本獎勵)
MonsterConfig.RARITY = {
    COMMON = {
        name = "普通",
        color = Color3.fromRGB(255, 255, 255), -- 白色
        weight = 70, -- 生成率70%
        targetCount = 60, -- 目標數量60隻
        minParts = 2,
        maxParts = 8,
        requiresSpecialEffects = false
    },
    UNCOMMON = {
        name = "稀有", 
        color = Color3.fromRGB(0, 255, 0), -- 綠色
        weight = 20, -- 生成率20%
        targetCount = 75, -- 目標數量75隻 (任務中的稀有)
        minParts = 6,
        maxParts = 15,
        requiresSpecialEffects = false
    },
    RARE = {
        name = "史詩",
        color = Color3.fromRGB(0, 100, 255), -- 藍色
        weight = 8, -- 生成率8%
        targetCount = 75, -- 目標數量75隻 (任務中的史詩)
        minParts = 12,
        maxParts = 25,
        requiresSpecialEffects = true
    },
    EPIC = {
        name = "神話",
        color = Color3.fromRGB(150, 0, 255), -- 紫色
        weight = 2, -- 生成率2%
        targetCount = 45, -- 目標數量45隻 (任務中的神話)
        minParts = 20,
        maxParts = 35,
        requiresSpecialEffects = true
    },
    LEGENDARY = {
        name = "傳說",
        color = Color3.fromRGB(255, 165, 0), -- 橙色
        weight = 1, -- 生成率<1%
        targetCount = 36, -- 目標數量36隻 (任務中的傳說)
        minParts = 30,
        maxParts = 100,
        requiresSpecialEffects = true
    }
}

-- 副本BOSS配置 (基於現有模型分配為BOSS)
MonsterConfig.DUNGEON_BOSSES = {
    FOREST = {
        name = "翡翠森林",
        description = "森林守護者的領域",
        bosses = {
            -- 小BOSS (來自森林動物模型)
            {
                name = "狼王",
                model = "Wolf", -- 基於現有模型
                type = "mini_boss",
                rarity = "UNCOMMON",
                level = 8,
                health = 2000
            },
            {
                name = "熊王",
                model = "Bear", -- 基於現有模型
                type = "mini_boss",
                rarity = "RARE",
                level = 12,
                health = 3500
            },
            -- 主BOSS
            {
                name = "森林守護者",
                model = "LuaBear", -- 基於現有特殊模型
                type = "main_boss",
                rarity = "EPIC",
                level = 15,
                health = 8000
            }
        }
    },
    ICE = {
        name = "極地冰窟",
        description = "冰霜巨龍的巢穴",
        bosses = {
            -- 小BOSS
            {
                name = "冰魔法師",
                model = "Nice cloud", -- 基於現有模型
                type = "mini_boss",
                rarity = "RARE",
                level = 20,
                health = 4500
            },
            {
                name = "雪怪王",
                model = "Snowman", -- 基於現有模型
                type = "mini_boss",
                rarity = "EPIC",
                level = 25,
                health = 6000
            },
            -- 主BOSS
            {
                name = "冰霜巨龍",
                model = "Ice golem", -- 基於現有模型
                type = "main_boss",
                rarity = "LEGENDARY",
                level = 30,
                health = 15000
            }
        }
    },
    LAVA = {
        name = "地獄熔爐",
        description = "惡魔王的王座",
        bosses = {
            -- 小BOSS
            {
                name = "火焰巨人",
                model = "Lava Beast", -- 基於現有模型
                type = "mini_boss",
                rarity = "EPIC",
                level = 35,
                health = 8000
            },
            {
                name = "岩漿元素",
                model = "Hellish Golem", -- 基於現有模型
                type = "mini_boss",
                rarity = "EPIC",
                level = 40,
                health = 10000
            },
            {
                name = "地獄犬王",
                model = "Demon bee", -- 基於現有模型
                type = "mini_boss",
                rarity = "LEGENDARY",
                level = 42,
                health = 12000
            },
            -- 主BOSS
            {
                name = "熔岩惡魔王",
                model = "Demonic Destroyer", -- 基於現有模型
                type = "main_boss",
                rarity = "LEGENDARY",
                level = 45,
                health = 25000
            }
        }
    }
}

-- 基於實際模型數據的怪物分配
MonsterConfig.MONSTER_DATA = {
    -- Forest 區域怪物 (基於現有模型重新分類)
    FOREST = {
        -- 普通 (20隻)
        common = {
            "Cat", "Fox", "Dog", "Bear", "Bunny", "Mouse", "Bee", "Cow", "Pig", "Duck",
            "Reindeer", "Cowboy", "Bull", "Burger", "Turkey", "Monkey", "Koala", "Panda", "Builder", "Angel"
        },
        -- 稀有 (25隻) 
        uncommon = {
            "Golden Cat", "LuaBear", "Skeleton deer", "Bat", "Shark", "Crab", "Robot", "Alien", "TV", "Starry",
            "Snowman", "Skeleton pup", "Shadow Dominus", "Reaper", "Psychic Scorpion", "Psychic", "Model", "Plasma Overlord", "Nice cloud", "Baby lightning",
            "Donut", "Pepper", "Fedora", "Gumdrop", "Headphones"
        },
        -- 史詩 (25隻)
        rare = {
            "Cyborg", "lil' creature", "Octopuslord", "Robot Overlord", "Bacterial Virus", "Angelfly", "Ninja", "Duo Fly", "Candy Stack", "Skeleton Fly",
            "Monster", "Electric spider", "OctoHex", "Spikey Balloon", "Ultimus", "lil' fly", "Deviloon", "Mad Scientist", "Infection", "Hallows spider",
            "Angeler fish", "Candy Mage", "Jellyfish", "Creature of the deap", "Dominus Ultimus"
        },
        -- 神話 (15隻)
        epic = {
            "Gigantic Lord", "Kraken", "Forgotton kraken", "Lava Lord", "Creature of light", "Aqua Dragon", "lil' Dragon", "lil' sea monster", "Demon", "lil' demon",
            "Mythical Demon", "Demon bee", "Lava Beast", "Light Demon", "Hellish Golem"
        },
        -- 傳說 (12隻)
        legendary = {
            "Demonic Destroyer", "Demonic Dominus", "Demonic Spider", "Ice golem", "Light Bat", "Robot Overlord", "Gigantic Lord", "Kraken", "Lava Lord", "Creature of light",
            "Aqua Dragon", "Ultimus"
        }
    }
}

-- 副本獎勵配置 (寵物作為副本獎勵獲得)
MonsterConfig.DUNGEON_REWARDS = {
    -- 森林副本獎勵
    FOREST = {
        miniBossRewards = {
            COMMON = 60,    -- 60%
            UNCOMMON = 30,  -- 30%
            RARE = 10,      -- 10%
            EPIC = 0,       -- 0%
            LEGENDARY = 0   -- 0%
        },
        mainBossRewards = {
            COMMON = 20,    -- 20%
            UNCOMMON = 40,  -- 40%
            RARE = 30,      -- 30%
            EPIC = 10,      -- 10%
            LEGENDARY = 0   -- 0%
        }
    },
    -- 冰雪副本獎勵
    ICE = {
        miniBossRewards = {
            COMMON = 30,    -- 30%
            UNCOMMON = 40,  -- 40%
            RARE = 25,      -- 25%
            EPIC = 5,       -- 5%
            LEGENDARY = 0   -- 0%
        },
        mainBossRewards = {
            COMMON = 10,    -- 10%
            UNCOMMON = 30,  -- 30%
            RARE = 40,      -- 40%
            EPIC = 18,      -- 18%
            LEGENDARY = 2   -- 2%
        }
    },
    -- 熔岩副本獎勵
    LAVA = {
        miniBossRewards = {
            COMMON = 15,    -- 15%
            UNCOMMON = 35,  -- 35%
            RARE = 35,      -- 35%
            EPIC = 13,      -- 13%
            LEGENDARY = 2   -- 2%
        },
        mainBossRewards = {
            COMMON = 5,     -- 5%
            UNCOMMON = 20,  -- 20%
            RARE = 35,      -- 35%
            EPIC = 30,      -- 30%
            LEGENDARY = 10  -- 10%
        }
    }
}

-- 視覺效果標準
MonsterConfig.VISUAL_EFFECTS = {
    -- 材質標準
    MATERIALS = {
        COMMON = {
            primary = Enum.Material.SmoothPlastic,
            secondary = Enum.Material.Plastic,
            allowNeon = false,
            allowGlass = false
        },
        UNCOMMON = {
            primary = Enum.Material.SmoothPlastic,
            secondary = Enum.Material.Metal,
            allowNeon = false,
            allowGlass = true
        },
        RARE = {
            primary = Enum.Material.SmoothPlastic,
            secondary = Enum.Material.Metal,
            allowNeon = true,
            allowGlass = true,
            minNeonParts = 2
        },
        EPIC = {
            primary = Enum.Material.Neon,
            secondary = Enum.Material.Glass,
            allowNeon = true,
            allowGlass = true,
            minNeonParts = 5,
            requiresParticles = true
        },
        LEGENDARY = {
            primary = Enum.Material.Neon,
            secondary = Enum.Material.Glass,
            allowNeon = true,
            allowGlass = true,
            minNeonParts = 10,
            requiresParticles = true,
            requiresSpecialEffects = true
        }
    },
    
    -- 顏色方案
    COLOR_SCHEMES = {
        COMMON = {
            primary = Color3.fromRGB(100, 100, 100),
            secondary = Color3.fromRGB(150, 150, 150),
            accent = Color3.fromRGB(200, 200, 200)
        },
        UNCOMMON = {
            primary = Color3.fromRGB(50, 150, 50),
            secondary = Color3.fromRGB(100, 200, 100),
            accent = Color3.fromRGB(150, 255, 150)
        },
        RARE = {
            primary = Color3.fromRGB(50, 100, 200),
            secondary = Color3.fromRGB(100, 150, 255),
            accent = Color3.fromRGB(150, 200, 255)
        },
        EPIC = {
            primary = Color3.fromRGB(150, 50, 200),
            secondary = Color3.fromRGB(200, 100, 255),
            accent = Color3.fromRGB(255, 150, 255)
        },
        LEGENDARY = {
            primary = Color3.fromRGB(255, 150, 0),
            secondary = Color3.fromRGB(255, 200, 50),
            accent = Color3.fromRGB(255, 255, 100)
        }
    },
    
    -- 特殊效果
    SPECIAL_EFFECTS = {
        RARE = {
            "Sparkles",
            "PointLight"
        },
        EPIC = {
            "Sparkles",
            "PointLight", 
            "Fire",
            "Smoke"
        },
        LEGENDARY = {
            "Sparkles",
            "PointLight",
            "Fire", 
            "Smoke",
            "Beam",
            "Trail",
            "ParticleEmitter"
        }
    }
}

-- 戰鬥屬性配置
MonsterConfig.COMBAT_STATS = {
    BASE_STATS = {
        COMMON = {
            health = 100,
            attack = 10,
            defense = 5,
            speed = 10,
            critRate = 0.05,
            critDamage = 1.5
        },
        UNCOMMON = {
            health = 150,
            attack = 15,
            defense = 8,
            speed = 12,
            critRate = 0.08,
            critDamage = 1.6
        },
        RARE = {
            health = 250,
            attack = 25,
            defense = 12,
            speed = 15,
            critRate = 0.12,
            critDamage = 1.8
        },
        EPIC = {
            health = 400,
            attack = 40,
            defense = 20,
            speed = 18,
            critRate = 0.18,
            critDamage = 2.0
        },
        LEGENDARY = {
            health = 650,
            attack = 65,
            defense = 32,
            speed = 22,
            critRate = 0.25,
            critDamage = 2.5
        }
    },
    
    -- 等級成長係數
    GROWTH_RATES = {
        health = 1.2,
        attack = 1.15,
        defense = 1.1,
        speed = 1.05,
        critRate = 1.02,
        critDamage = 1.01
    }
}

-- 生成配置
MonsterConfig.SPAWN = {
    -- 野生怪物生成率
    WILD_SPAWN_RATES = {
        COMMON = 0.7,    -- 70%
        UNCOMMON = 0.2,  -- 20%
        RARE = 0.08,     -- 8%
        EPIC = 0.02,     -- 2%
        LEGENDARY = 0.001 -- 0.1%
    },
    
    -- 生成間隔 (秒)
    SPAWN_INTERVALS = {
        COMMON = 5,
        UNCOMMON = 15,
        RARE = 60,
        EPIC = 300,
        LEGENDARY = 1800
    },
    
    -- 每個區域最大怪物數量
    MAX_MONSTERS_PER_ZONE = 50,
    
    -- 玩家附近生成距離
    SPAWN_DISTANCE = {
        min = 50,
        max = 200
    }
}

-- 副本BOSS配置 (基於現有模型分配為BOSS)
MonsterConfig.DUNGEON_BOSSES = {
    FOREST = {
        name = "翡翠森林",
        description = "森林守護者的領域",
        bosses = {
            {name = "狼王", model = "Wolf", type = "mini_boss", rarity = "UNCOMMON", level = 8, health = 2000},
            {name = "熊王", model = "Bear", type = "mini_boss", rarity = "RARE", level = 12, health = 3500},
            {name = "森林守護者", model = "LuaBear", type = "main_boss", rarity = "EPIC", level = 15, health = 8000}
        }
    },
    ICE = {
        name = "極地冰窟",
        description = "冰霜巨龍的巢穴",
        bosses = {
            {name = "冰魔法師", model = "Nice cloud", type = "mini_boss", rarity = "RARE", level = 20, health = 4500},
            {name = "雪怪王", model = "Snowman", type = "mini_boss", rarity = "EPIC", level = 25, health = 6000},
            {name = "冰霜巨龍", model = "Ice golem", type = "main_boss", rarity = "LEGENDARY", level = 30, health = 15000}
        }
    },
    LAVA = {
        name = "地獄熔爐",
        description = "惡魔王的王座",
        bosses = {
            {name = "火焰巨人", model = "Lava Beast", type = "mini_boss", rarity = "EPIC", level = 35, health = 8000},
            {name = "岩漿元素", model = "Hellish Golem", type = "mini_boss", rarity = "EPIC", level = 40, health = 10000},
            {name = "地獄犬王", model = "Demon bee", type = "mini_boss", rarity = "LEGENDARY", level = 42, health = 12000},
            {name = "熔岩惡魔王", model = "Demonic Destroyer", type = "main_boss", rarity = "LEGENDARY", level = 45, health = 25000}
        }
    }
}

-- 副本獎勵配置 (寵物作為副本獎勵獲得)
MonsterConfig.DUNGEON_REWARDS = {
    FOREST = {
        miniBossRewards = {COMMON = 60, UNCOMMON = 30, RARE = 10, EPIC = 0, LEGENDARY = 0},
        mainBossRewards = {COMMON = 20, UNCOMMON = 40, RARE = 30, EPIC = 10, LEGENDARY = 0}
    },
    ICE = {
        miniBossRewards = {COMMON = 30, UNCOMMON = 40, RARE = 25, EPIC = 5, LEGENDARY = 0},
        mainBossRewards = {COMMON = 10, UNCOMMON = 30, RARE = 40, EPIC = 18, LEGENDARY = 2}
    },
    LAVA = {
        miniBossRewards = {COMMON = 15, UNCOMMON = 35, RARE = 35, EPIC = 13, LEGENDARY = 2},
        mainBossRewards = {COMMON = 5, UNCOMMON = 20, RARE = 35, EPIC = 30, LEGENDARY = 10}
    }
}

return MonsterConfig
