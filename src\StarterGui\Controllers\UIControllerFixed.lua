-- UI 控制器 - 修復版 (適配 Fusion 0.3)
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

local UIController = Knit.CreateController({
    Name = "UIController"
})

-- 響應式狀態 (使用 Fusion 0.3 語法)
local playerData = scope:Value({})
local heroStats = scope:Value({
    level = 1,
    experience = 0,
    maxHealth = 150,
    currentHealth = 150,
    attack = 15,
    defense = 8
})
local currencies = scope:Value({
    coins = 1000,
    gems = 20,
    robux = 0
})
local uiState = scope:Value({
    currentScreen = "main",
    isLoading = false,
    notifications = {}
})

-- 計算屬性 (使用 Fusion 0.3 語法)
local healthPercentage = scope:Computed(function(use)
    local hero = use(heroStats)
    if hero.maxHealth and hero.maxHealth > 0 then
        return hero.currentHealth / hero.maxHealth
    end
    return 1
end)

local expPercentage = scope:Computed(function(use)
    local hero = use(heroStats)
    if hero.level >= 50 then -- 假設最大等級是50
        return 1
    end
    
    local requiredExp = hero.level * 100 -- 簡化的經驗計算
    return requiredExp > 0 and (hero.experience / requiredExp) or 0
end)

function UIController:KnitStart()
    print("🎨 UIController 啟動 (Fusion 0.3)")
    
    -- 監聽服務端事件
    self:_connectToServices()
    
    -- 存儲 scope 引用
    self._scope = scope
end

function UIController:_connectToServices()
    -- 簡化版本，避免依賴不存在的服務
    print("🔗 連接到服務 (簡化版)")
end

function UIController:InitializeUI()
    local Players = game:GetService("Players")
    local player = Players.LocalPlayer
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- 創建主 UI
    local mainUI = self:_createMainUI()
    mainUI.Parent = playerGui
    
    print("🖼️ 主 UI 已創建 (Fusion 0.3)")
end

function UIController:_createMainUI()
    return scope:New "ScreenGui" {
        Name = "PetSim99UI",
        ResetOnSpawn = false,
        
        [Fusion.Children] = {
            -- 頂部狀態欄
            self:_createStatusBar(),
            
            -- 底部導航欄
            self:_createNavigationBar(),
            
            -- 通知系統
            self:_createNotificationSystem()
        }
    }
end

function UIController:_createStatusBar()
    return scope:New "Frame" {
        Name = "StatusBar",
        Size = UDim2.new(1, 0, 0, 80),
        Position = UDim2.new(0, 0, 0, 0),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        
        [Fusion.Children] = {
            -- 血量條
            scope:New "Frame" {
                Name = "HealthBar",
                Size = UDim2.new(0.25, -10, 0.3, 0),
                Position = UDim2.new(0.05, 0, 0.1, 0),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                
                [Fusion.Children] = {
                    scope:New "Frame" {
                        Name = "HealthFill",
                        Size = scope:Computed(function(use)
                            local percentage = use(healthPercentage)
                            return UDim2.new(percentage, 0, 1, 0)
                        end),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = scope:Computed(function(use)
                            local percentage = use(healthPercentage)
                            if percentage > 0.6 then
                                return Color3.fromRGB(0, 255, 0) -- 綠色
                            elseif percentage > 0.3 then
                                return Color3.fromRGB(255, 255, 0) -- 黃色
                            else
                                return Color3.fromRGB(255, 0, 0) -- 紅色
                            end
                        end),
                        BorderSizePixel = 0
                    },
                    
                    scope:New "TextLabel" {
                        Name = "HealthText",
                        Size = UDim2.new(1, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function(use)
                            local hero = use(heroStats)
                            return string.format("%d/%d", hero.currentHealth or 0, hero.maxHealth or 0)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    }
                }
            },
            
            -- 經驗值條
            scope:New "Frame" {
                Name = "ExpBar",
                Size = UDim2.new(0.25, -10, 0.2, 0),
                Position = UDim2.new(0.05, 0, 0.5, 0),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                
                [Fusion.Children] = {
                    scope:New "Frame" {
                        Name = "ExpFill",
                        Size = scope:Computed(function(use)
                            local percentage = use(expPercentage)
                            return UDim2.new(percentage, 0, 1, 0)
                        end),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 150, 255),
                        BorderSizePixel = 0
                    },
                    
                    scope:New "TextLabel" {
                        Name = "ExpText",
                        Size = UDim2.new(1, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function(use)
                            local hero = use(heroStats)
                            return string.format("Lv.%d", hero.level or 1)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    }
                }
            },
            
            -- 貨幣顯示
            scope:New "Frame" {
                Name = "CurrencyDisplay",
                Size = UDim2.new(0.4, 0, 0.8, 0),
                Position = UDim2.new(0.35, 0, 0.1, 0),
                BackgroundTransparency = 1,
                
                [Fusion.Children] = {
                    -- 金幣
                    scope:New "Frame" {
                        Name = "Coins",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(255, 215, 0),
                        BackgroundTransparency = 0.8,
                        BorderSizePixel = 0,
                        
                        [Fusion.Children] = {
                            scope:New "TextLabel" {
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local curr = use(currencies)
                                    return "💰 " .. tostring(curr.coins or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }
                        }
                    },
                    
                    -- 寶石
                    scope:New "Frame" {
                        Name = "Gems",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0.33, 5, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 255, 255),
                        BackgroundTransparency = 0.8,
                        BorderSizePixel = 0,
                        
                        [Fusion.Children] = {
                            scope:New "TextLabel" {
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local curr = use(currencies)
                                    return "💎 " .. tostring(curr.gems or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }
                        }
                    },
                    
                    -- R幣
                    scope:New "Frame" {
                        Name = "Robux",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0.66, 10, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 255, 0),
                        BackgroundTransparency = 0.8,
                        BorderSizePixel = 0,
                        
                        [Fusion.Children] = {
                            scope:New "TextLabel" {
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local curr = use(currencies)
                                    return "R$ " .. tostring(curr.robux or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }
                        }
                    }
                }
            }
        }
    }
end

function UIController:_createNavigationBar()
    return scope:New "Frame" {
        Name = "NavigationBar",
        Size = UDim2.new(1, 0, 0, 60),
        Position = UDim2.new(0, 0, 1, -60),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        
        [Fusion.Children] = {
            scope:New "TextLabel" {
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundTransparency = 1,
                Text = "🎮 導航欄 - Fusion 0.3 版本",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.Gotham
            }
        }
    }
end

function UIController:_createNotificationSystem()
    return scope:New "Frame" {
        Name = "NotificationSystem",
        Size = UDim2.new(0.3, 0, 0.5, 0),
        Position = UDim2.new(0.7, 0, 0.1, 0),
        BackgroundTransparency = 1,
        
        [Fusion.Children] = {
            -- 通知將在這裡動態添加
        }
    }
end

-- 公共方法
function UIController:ShowNotification(message, type)
    print("📢 通知:", message, "類型:", type or "info")
end

-- 數據獲取方法 (使用 peek 而不是 get)
function UIController:GetPlayerData()
    return Fusion.peek(playerData)
end

function UIController:GetHeroStats()
    return Fusion.peek(heroStats)
end

function UIController:GetCurrencies()
    return Fusion.peek(currencies)
end

function UIController:GetUIState()
    return Fusion.peek(uiState)
end

-- 更新方法
function UIController:UpdateHeroStats(newStats)
    heroStats:set(newStats)
end

function UIController:UpdateCurrencies(newCurrencies)
    currencies:set(newCurrencies)
end

function UIController:UpdateUIState(newState)
    local current = Fusion.peek(uiState)
    local merged = {}
    for k, v in pairs(current) do
        merged[k] = v
    end
    for k, v in pairs(newState) do
        merged[k] = v
    end
    uiState:set(merged)
end

-- 清理方法
function UIController:Cleanup()
    if self._scope then
        self._scope:doCleanup()
    end
end

return UIController
