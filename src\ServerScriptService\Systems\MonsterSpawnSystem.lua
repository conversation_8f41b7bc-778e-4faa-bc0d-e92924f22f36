-- 怪物生成系統
-- 負責在地圖上生成野生怪物，管理怪物數量和分布

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

local Matter = require(ReplicatedStorage.Packages.Matter)
local MonsterComponents = require(ReplicatedStorage.Components.MonsterComponents)
local MonsterConfig = require(ReplicatedStorage.Configuration.MonsterConfig)
local ZoneConfig = require(ReplicatedStorage.Configuration.ZoneConfig)

local MonsterSpawnSystem = {}

-- 系統狀態
local spawnPoints = {}
local activeSpawners = {}
local lastCleanupTime = 0
local CLEANUP_INTERVAL = 60 -- 清理間隔(秒)

-- 初始化生成點
function MonsterSpawnSystem.initializeSpawnPoints()
    spawnPoints = {
        FOREST = {
            {position = Vector3.new(100, 5, 100), radius = 80},
            {position = Vector3.new(200, 5, 150), radius = 60},
            {position = Vector3.new(50, 5, 200), radius = 70},
            {position = Vector3.new(300, 5, 100), radius = 90}
        },
        ICE = {
            {position = Vector3.new(500, 5, 100), radius = 80},
            {position = Vector3.new(600, 5, 150), radius = 60},
            {position = Vector3.new(450, 5, 200), radius = 70}
        },
        LAVA = {
            {position = Vector3.new(800, 5, 100), radius = 80},
            {position = Vector3.new(900, 5, 150), radius = 60},
            {position = Vector3.new(750, 5, 200), radius = 70}
        }
    }
    
    print("✅ 怪物生成點初始化完成")
end

-- 創建生成器
function MonsterSpawnSystem.createSpawner(world, zone, position, radius)
    local spawner = world:spawn(
        MonsterComponents.MonsterSpawner({
            zone = zone,
            position = position,
            radius = radius,
            maxMonsters = MonsterConfig.SPAWN.MAX_MONSTERS_PER_ZONE,
            spawnInterval = 30,
            lastSpawnTime = 0,
            activeMonsters = {},
            spawnRates = MonsterConfig.SPAWN.WILD_SPAWN_RATES
        })
    )
    
    activeSpawners[spawner] = true
    return spawner
end

-- 獲取隨機稀有度
function MonsterSpawnSystem.getRandomRarity(spawnRates)
    local random = math.random()
    local cumulative = 0
    
    for rarity, rate in pairs(spawnRates) do
        cumulative = cumulative + rate
        if random <= cumulative then
            return rarity
        end
    end
    
    return "COMMON" -- 默認返回普通
end

-- 獲取區域怪物列表
function MonsterSpawnSystem.getZoneMonsters(zone, rarity)
    local monsters = MonsterConfig.MONSTER_DATA[zone]
    if not monsters then
        return {}
    end
    
    local rarityKey = string.lower(rarity)
    return monsters[rarityKey] or {}
end

-- 生成怪物
function MonsterSpawnSystem.spawnMonster(world, zone, position, rarity)
    local monsterList = MonsterSpawnSystem.getZoneMonsters(zone, rarity)
    if #monsterList == 0 then
        return nil
    end
    
    -- 隨機選擇怪物
    local speciesId = monsterList[math.random(#monsterList)]
    local config = MonsterConfig.RARITY[rarity]
    local baseStats = MonsterConfig.COMBAT_STATS.BASE_STATS[rarity]
    
    -- 隨機位置偏移
    local spawnPos = position + Vector3.new(
        math.random(-50, 50),
        0,
        math.random(-50, 50)
    )
    
    -- 創建怪物實體
    local monster = world:spawn(
        MonsterComponents.Monster({
            speciesId = speciesId,
            name = speciesId,
            rarity = rarity,
            level = math.random(1, 10),
            zone = zone,
            isWild = true,
            spawnTime = tick(),
            lastSeenTime = tick()
        }),
        
        MonsterComponents.MonsterStats({
            health = baseStats.health,
            maxHealth = baseStats.health,
            attack = baseStats.attack,
            defense = baseStats.defense,
            speed = baseStats.speed,
            critRate = baseStats.critRate,
            critDamage = baseStats.critDamage,
            experience = baseStats.health * 0.1
        }),
        
        MonsterComponents.MonsterPosition({
            x = spawnPos.X,
            y = spawnPos.Y,
            z = spawnPos.Z,
            rotation = math.random(0, 360),
            zone = zone,
            isMoving = false,
            targetPosition = nil
        }),
        
        MonsterComponents.MonsterVisual({
            modelName = speciesId,
            instance = nil,
            material = config.name,
            primaryColor = config.color,
            secondaryColor = config.color,
            hasNeonParts = rarity ~= "COMMON",
            hasParticles = rarity == "EPIC" or rarity == "LEGENDARY",
            scale = 1.0
        }),
        
        MonsterComponents.MonsterAI({
            state = "idle",
            detectionRange = 50,
            attackRange = 10,
            patrolRadius = 30,
            lastActionTime = tick(),
            target = nil,
            homePosition = spawnPos
        }),
        
        MonsterComponents.MonsterLoot({
            experienceReward = baseStats.health * 0.1,
            coinReward = math.random(5, 15),
            gemReward = rarity == "LEGENDARY" and 1 or 0,
            dropTable = {},
            guaranteedDrops = {"coins"},
            rareDrops = {},
            dropChance = 0.1
        })
    )
    
    print(string.format("🐾 生成怪物: %s (%s) 在 %s", speciesId, rarity, zone))
    return monster
end

-- 檢查是否需要生成怪物
function MonsterSpawnSystem.shouldSpawn(spawner, currentTime)
    return (currentTime - spawner.lastSpawnTime) >= spawner.spawnInterval and
           #spawner.activeMonsters < spawner.maxMonsters
end

-- 清理死亡或消失的怪物
function MonsterSpawnSystem.cleanupMonsters(world, spawner)
    local validMonsters = {}
    
    for _, monsterId in ipairs(spawner.activeMonsters) do
        if world:contains(monsterId) then
            table.insert(validMonsters, monsterId)
        end
    end
    
    spawner.activeMonsters = validMonsters
end

-- 主要生成系統
function MonsterSpawnSystem.step(world, state)
    local currentTime = tick()
    
    -- 定期清理
    if currentTime - lastCleanupTime > CLEANUP_INTERVAL then
        lastCleanupTime = currentTime
        
        for spawnerId in pairs(activeSpawners) do
            if world:contains(spawnerId) then
                local spawner = world:get(spawnerId, MonsterComponents.MonsterSpawner)
                if spawner then
                    MonsterSpawnSystem.cleanupMonsters(world, spawner)
                end
            else
                activeSpawners[spawnerId] = nil
            end
        end
    end
    
    -- 處理每個生成器
    for spawnerId in pairs(activeSpawners) do
        if world:contains(spawnerId) then
            local spawner = world:get(spawnerId, MonsterComponents.MonsterSpawner)
            
            if spawner and MonsterSpawnSystem.shouldSpawn(spawner, currentTime) then
                -- 獲取隨機稀有度
                local rarity = MonsterSpawnSystem.getRandomRarity(spawner.spawnRates)
                
                -- 生成怪物
                local monster = MonsterSpawnSystem.spawnMonster(
                    world, 
                    spawner.zone, 
                    spawner.position, 
                    rarity
                )
                
                if monster then
                    -- 更新生成器狀態
                    table.insert(spawner.activeMonsters, monster)
                    spawner.lastSpawnTime = currentTime
                    
                    -- 更新組件
                    world:insert(spawnerId, MonsterComponents.MonsterSpawner(spawner))
                end
            end
        end
    end
end

-- 初始化系統
function MonsterSpawnSystem.initialize(world)
    MonsterSpawnSystem.initializeSpawnPoints()
    
    -- 為每個區域創建生成器
    for zone, points in pairs(spawnPoints) do
        for _, point in ipairs(points) do
            MonsterSpawnSystem.createSpawner(world, zone, point.position, point.radius)
        end
    end
    
    print("✅ 怪物生成系統初始化完成")
end

return MonsterSpawnSystem
