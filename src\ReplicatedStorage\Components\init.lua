-- ECS 組件定義模組
-- 這個模組導出所有遊戲中使用的 Matter 組件

local Matter = require(script.Parent.Parent.Packages.Matter)

local Components = {}

-- 玩家相關組件
Components.PlayerComponent = Matter.component("Player", {
    userId = 0,
    displayName = "",
    joinTime = 0,
    isOnline = true
})

Components.HeroComponent = Matter.component("Hero", {
    level = 1,
    experience = 0,
    maxHealth = 150,
    currentHealth = 150,
    attack = 15,
    defense = 8,
    critRate = 0.05,
    critDamage = 1.5
})

-- 寵物相關組件
Components.PetComponent = Matter.component("Pet", {
    speciesId = "",
    name = "",
    rarity = "common", -- common, rare, epic, mythic, legendary
    level = 1,
    experience = 0,
    isShiny = false,
    obtainedAt = 0,
    ownerId = 0
})

Components.PetStatsComponent = Matter.component("PetStats", {
    health = 75,
    attack = 15,
    defense = 7,
    speed = 21,
    rarityMultiplier = 1.0
})

-- 戰鬥相關組件
Components.CombatComponent = Matter.component("Combat", {
    isInCombat = false,
    target = nil,
    lastAttackTime = 0,
    combatStartTime = 0
})

Components.HealthComponent = Matter.component("Health", {
    current = 100,
    maximum = 100,
    regeneration = 1,
    lastRegenTime = 0
})

-- 位置和區域組件
Components.TransformComponent = Matter.component("Transform", {
    position = Vector3.new(0, 0, 0),
    rotation = Vector3.new(0, 0, 0),
    scale = Vector3.new(1, 1, 1)
})

Components.ZoneComponent = Matter.component("Zone", {
    currentZone = "castle", -- castle, forest, ice, lava
    lastZoneChange = 0,
    allowedZones = {}
})

-- 怪物相關組件
Components.MonsterComponent = Matter.component("Monster", {
    speciesId = "",
    spawnZone = "",
    spawnTime = 0,
    respawnTime = 300, -- 5分鐘重生
    isAlive = true,
    encounterCount = 0
})

-- 裝備相關組件
Components.EquipmentComponent = Matter.component("Equipment", {
    itemId = "",
    equipType = "", -- weapon, armor, accessory
    rarity = "common",
    level = 1,
    stats = {},
    effects = {}
})

-- 經濟相關組件
Components.CurrencyComponent = Matter.component("Currency", {
    coins = 1000,
    gems = 20,
    robux = 0,
    experience = 0
})

-- 任務相關組件
Components.QuestComponent = Matter.component("Quest", {
    questId = "",
    questType = "", -- daily, weekly, monthly
    target = 0,
    progress = 0,
    completed = false,
    claimed = false,
    rewards = {}
})

-- VIP 相關組件
Components.VIPComponent = Matter.component("VIP", {
    level = 0,
    totalSpent = 0,
    premiumExpiry = 0,
    premiumActive = false,
    lastRewardClaim = 0
})

-- 圖鑑相關組件
Components.PokedexComponent = Matter.component("Pokedex", {
    encounteredSpecies = {},
    ownedSpecies = {},
    completionRate = 0
})

-- UI 相關組件
Components.UIComponent = Matter.component("UI", {
    isVisible = true,
    screenName = "",
    data = {}
})

-- 社交相關組件
Components.SocialComponent = Matter.component("Social", {
    friends = {},
    guildId = nil,
    lastLogin = 0,
    loginStreak = 0
})

-- 任務進度組件
Components.QuestProgressComponent = Matter.component("QuestProgress", {
    activeQuests = {},
    completedQuests = {},
    dailyProgress = {},
    weeklyProgress = {},
    monthlyProgress = {}
})

-- 登入獎勵組件
Components.LoginRewardComponent = Matter.component("LoginReward", {
    currentDay = 1,
    lastClaimTime = 0,
    claimedDays = {},
    missedDays = {},
    canClaim = false
})

-- 成就組件
Components.AchievementComponent = Matter.component("Achievement", {
    unlockedAchievements = {},
    progress = {},
    totalPoints = 0
})

-- 設置組件
Components.SettingsComponent = Matter.component("Settings", {
    soundEnabled = true,
    musicEnabled = true,
    notificationsEnabled = true,
    language = "zh-TW",
    graphics = "medium"
})

-- 狀態效果組件
Components.StatusEffectComponent = Matter.component("StatusEffect", {
    effects = {}, -- {type, duration, strength, startTime}
    immunities = {}
})

-- 移動組件
Components.MovementComponent = Matter.component("Movement", {
    velocity = Vector3.new(0, 0, 0),
    speed = 16,
    isMoving = false,
    destination = nil,
    path = {}
})

-- 動畫組件
Components.AnimationComponent = Matter.component("Animation", {
    currentAnimation = "idle",
    animationSpeed = 1,
    isLooping = true,
    animations = {}
})

-- 音效組件
Components.AudioComponent = Matter.component("Audio", {
    currentMusic = "",
    soundEffects = {},
    volume = 1.0,
    isMuted = false
})

-- 區域組件
Components.ZoneComponent = Matter.component("Zone", {
    currentZone = "",
    previousZone = "",
    enterTime = 0
})

-- 怪物組件
Components.MonsterComponent = Matter.component("Monster", {
    speciesId = "",
    level = 1,
    spawnZone = "",
    spawnTime = 0,
    lastAttackTime = 0
})

-- 野生寵物組件
Components.WildPetComponent = Matter.component("WildPet", {
    speciesId = "",
    rarity = "common",
    spawnZone = "",
    spawnTime = 0,
    catchable = true
})

-- 傳送點組件
Components.TeleporterComponent = Matter.component("Teleporter", {
    targetZone = "",
    cost = {},
    cooldown = 0,
    lastUsed = 0
})

return Components
