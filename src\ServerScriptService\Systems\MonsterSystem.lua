-- 怪物系統 - 管理怪物生成、行為和戰鬥
local Matter = require(game.ReplicatedStorage.Packages.Matter)
local Components = require(game.ReplicatedStorage.Components)
local ZoneConfig = require(game.ReplicatedStorage.Configuration.ZoneConfig)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local MonsterSystem = {}

function MonsterSystem.new()
    local self = {
        name = "MonsterSystem",
        priority = 3,
        
        -- 怪物生成狀態
        _zoneSpawning = {},
        _lastSpawnTime = {},
        _monsterCounts = {}
    }
    
    return setmetatable(self, { __index = MonsterSystem })
end

function MonsterSystem:step(world, state)
    -- 更新怪物 AI
    self:updateMonsterAI(world, state)
    
    -- 處理怪物生成
    self:handleMonsterSpawning(world, state)
    
    -- 清理死亡怪物
    self:cleanupDeadMonsters(world, state)
end

-- 更新怪物 AI
function MonsterSystem:updateMonsterAI(world, state)
    for entityId, monster, health, transform in world:query(
        Components.MonsterComponent,
        Components.HealthComponent,
        Components.TransformComponent
    ) do
        -- 檢查怪物是否死亡
        if health.current <= 0 then
            self:handleMonsterDeath(world, entityId, monster)
            continue
        end
        
        -- 尋找附近的玩家
        local nearestPlayer = self:findNearestPlayer(world, transform.position, 50)
        
        if nearestPlayer then
            -- 移動向玩家
            self:moveMonsterTowardsPlayer(world, entityId, transform, nearestPlayer)
            
            -- 檢查攻擊距離
            local distance = (transform.position - nearestPlayer.position).Magnitude
            if distance <= 10 then -- 攻擊範圍
                self:attemptMonsterAttack(world, entityId, monster, nearestPlayer.entityId)
            end
        else
            -- 隨機移動或待機
            self:handleMonsterIdle(world, entityId, monster, transform)
        end
    end
end

-- 尋找最近的玩家
function MonsterSystem:findNearestPlayer(world, position, maxDistance)
    local nearestPlayer = nil
    local nearestDistance = maxDistance
    
    for entityId, player, transform, health in world:query(
        Components.PlayerComponent,
        Components.TransformComponent,
        Components.HealthComponent
    ) do
        if health.current > 0 then
            local distance = (position - transform.position).Magnitude
            if distance < nearestDistance then
                nearestDistance = distance
                nearestPlayer = {
                    entityId = entityId,
                    position = transform.position,
                    userId = player.userId
                }
            end
        end
    end
    
    return nearestPlayer
end

-- 移動怪物向玩家
function MonsterSystem:moveMonsterTowardsPlayer(world, monsterId, monsterTransform, player)
    local direction = (player.position - monsterTransform.position).Unit
    local speed = 8 -- 怪物移動速度
    local newPosition = monsterTransform.position + direction * speed * (1/60) -- 假設 60 FPS
    
    world:insert(monsterId, Components.TransformComponent({
        position = newPosition,
        rotation = monsterTransform.rotation,
        scale = monsterTransform.scale
    }))
end

-- 嘗試怪物攻擊
function MonsterSystem:attemptMonsterAttack(world, monsterId, monster, targetId)
    local currentTime = tick()
    local attackCooldown = 2.0 -- 2秒攻擊冷卻
    
    if currentTime - monster.lastAttackTime >= attackCooldown then
        -- 執行攻擊
        local damage = self:calculateMonsterDamage(monster)
        
        -- 對目標造成傷害
        local targetHealth = world:get(targetId, Components.HealthComponent)
        if targetHealth then
            local newHealth = math.max(0, targetHealth.current - damage)
            
            world:insert(targetId, Components.HealthComponent({
                current = newHealth,
                maximum = targetHealth.maximum,
                regeneration = targetHealth.regeneration,
                lastRegenTime = targetHealth.lastRegenTime
            }))
            
            print("🗡️ 怪物攻擊造成傷害:", damage)
        end
        
        -- 更新怪物攻擊時間
        world:insert(monsterId, Components.MonsterComponent({
            speciesId = monster.speciesId,
            level = monster.level,
            spawnZone = monster.spawnZone,
            spawnTime = monster.spawnTime,
            lastAttackTime = currentTime
        }))
    end
end

-- 計算怪物傷害
function MonsterSystem:calculateMonsterDamage(monster)
    local baseDamage = 10
    local levelMultiplier = 1 + (monster.level - 1) * 0.2
    return math.floor(baseDamage * levelMultiplier)
end

-- 處理怪物待機
function MonsterSystem:handleMonsterIdle(world, monsterId, monster, transform)
    -- 簡單的隨機移動邏輯
    if math.random() < 0.01 then -- 1% 機率移動
        local randomDirection = Vector3.new(
            math.random(-1, 1),
            0,
            math.random(-1, 1)
        ).Unit
        
        local newPosition = transform.position + randomDirection * 5
        
        world:insert(monsterId, Components.TransformComponent({
            position = newPosition,
            rotation = transform.rotation,
            scale = transform.scale
        }))
    end
end

-- 處理怪物死亡
function MonsterSystem:handleMonsterDeath(world, monsterId, monster)
    print("💀 怪物死亡:", monster.speciesId)
    
    -- 給予玩家獎勵 (這裡需要找到擊殺者)
    self:giveMonsterKillRewards(world, monster)
    
    -- 移除怪物實體
    world:despawn(monsterId)
    
    -- 更新區域怪物計數
    local zoneId = monster.spawnZone
    if self._monsterCounts[zoneId] then
        self._monsterCounts[zoneId] = math.max(0, self._monsterCounts[zoneId] - 1)
    end
end

-- 給予怪物擊殺獎勵
function MonsterSystem:giveMonsterKillRewards(world, monster)
    -- 這裡應該找到實際的擊殺者，暫時給予附近的玩家
    local baseExp = 25
    local baseCoins = 50
    local levelMultiplier = 1 + (monster.level - 1) * 0.1
    
    local expReward = math.floor(baseExp * levelMultiplier)
    local coinReward = math.floor(baseCoins * levelMultiplier)
    
    print("🎁 怪物擊殺獎勵:", expReward, "經驗,", coinReward, "金幣")
end

-- 處理怪物生成
function MonsterSystem:handleMonsterSpawning(world, state)
    local currentTime = tick()
    
    for zoneId, isSpawning in pairs(self._zoneSpawning) do
        if isSpawning then
            local lastSpawn = self._lastSpawnTime[zoneId] or 0
            local zoneConfig = ZoneConfig:GetZone(zoneId)
            
            if zoneConfig and zoneConfig.monsters then
                local spawnRate = zoneConfig.monsters.spawnRate
                local maxCount = zoneConfig.monsters.maxCount
                local currentCount = self._monsterCounts[zoneId] or 0
                
                -- 檢查是否需要生成怪物
                if currentCount < maxCount and currentTime - lastSpawn >= (1 / spawnRate) then
                    self:spawnMonsterInZone(world, zoneId, zoneConfig)
                    self._lastSpawnTime[zoneId] = currentTime
                end
            end
        end
    end
end

-- 在區域中生成怪物
function MonsterSystem:spawnMonsterInZone(world, zoneId, zoneConfig)
    local monsterSpecies = zoneConfig.monsters.species
    if not monsterSpecies or #monsterSpecies == 0 then
        return
    end
    
    -- 根據權重選擇怪物種類
    local selectedSpecies = Utils.weightedRandom(monsterSpecies)
    if not selectedSpecies then
        return
    end
    
    -- 隨機等級
    local minLevel = selectedSpecies.level[1]
    local maxLevel = selectedSpecies.level[2]
    local level = math.random(minLevel, maxLevel)
    
    -- 尋找生成位置
    local spawnPosition = self:findSpawnPosition(zoneId)
    if not spawnPosition then
        return
    end
    
    -- 創建怪物實體
    local monsterId = world:spawn(
        Components.MonsterComponent({
            speciesId = selectedSpecies.id,
            level = level,
            spawnZone = zoneId,
            spawnTime = tick(),
            lastAttackTime = 0
        }),
        
        Components.HealthComponent({
            current = 100 + level * 20,
            maximum = 100 + level * 20,
            regeneration = 1,
            lastRegenTime = tick()
        }),
        
        Components.TransformComponent({
            position = spawnPosition,
            rotation = Vector3.new(0, math.random(0, 360), 0),
            scale = Vector3.new(1, 1, 1)
        })
    )
    
    -- 更新怪物計數
    self._monsterCounts[zoneId] = (self._monsterCounts[zoneId] or 0) + 1
    
    print("👹 生成怪物:", selectedSpecies.id, "等級", level, "在", zoneId)
    
    return monsterId
end

-- 尋找生成位置
function MonsterSystem:findSpawnPosition(zoneId)
    local workspace = game:GetService("Workspace")
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    
    if zoneConfig and zoneConfig.boundaries.partName then
        local zonePart = workspace:FindFirstChild(zoneConfig.boundaries.partName)
        if zonePart then
            -- 在區域範圍內隨機生成
            local size = zonePart.Size
            local position = zonePart.Position
            
            local randomX = position.X + math.random(-size.X/2, size.X/2)
            local randomZ = position.Z + math.random(-size.Z/2, size.Z/2)
            local y = position.Y + size.Y/2 + 5 -- 在地面上方
            
            return Vector3.new(randomX, y, randomZ)
        end
    end
    
    return nil
end

-- 清理死亡怪物
function MonsterSystem:cleanupDeadMonsters(world, state)
    -- 這個方法在 handleMonsterDeath 中已經處理了
    -- 這裡可以添加額外的清理邏輯
end

-- 開始在區域中生成怪物
function MonsterSystem:startSpawningInZone(world, zoneId)
    self._zoneSpawning[zoneId] = true
    self._monsterCounts[zoneId] = 0
    print("🎯 開始在區域生成怪物:", zoneId)
end

-- 停止在區域中生成怪物
function MonsterSystem:stopSpawningInZone(world, zoneId)
    self._zoneSpawning[zoneId] = false
    print("🛑 停止在區域生成怪物:", zoneId)
end

-- 維護區域怪物數量
function MonsterSystem:maintainZoneMonsters(world, zoneId)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig or not zoneConfig.monsters then
        return
    end
    
    local currentCount = self._monsterCounts[zoneId] or 0
    local maxCount = zoneConfig.monsters.maxCount
    
    -- 如果怪物數量不足，增加生成頻率
    if currentCount < maxCount * 0.5 then
        -- 可以在這裡調整生成邏輯
    end
end

-- 獲取區域怪物數量
function MonsterSystem:getZoneMonsterCount(zoneId)
    return self._monsterCounts[zoneId] or 0
end

-- 清除區域中的所有怪物
function MonsterSystem:clearZoneMonsters(world, zoneId)
    for entityId, monster in world:query(Components.MonsterComponent) do
        if monster.spawnZone == zoneId then
            world:despawn(entityId)
        end
    end
    
    self._monsterCounts[zoneId] = 0
    print("🧹 清除區域怪物:", zoneId)
end

return MonsterSystem
