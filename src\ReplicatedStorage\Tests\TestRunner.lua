-- 測試運行器 - 統一管理和執行所有測試
local TestEZ = require(game.ReplicatedStorage.Packages.TestEZ)
local Signal = require(game.ReplicatedStorage.Packages.Signal)

local TestRunner = {}

-- 測試結果事件
TestRunner.OnTestStarted = Signal.new()
TestRunner.OnTestCompleted = Signal.new()
TestRunner.OnTestFailed = Signal.new()
TestRunner.OnAllTestsCompleted = Signal.new()

-- 測試統計
TestRunner.Stats = {
    totalTests = 0,
    passedTests = 0,
    failedTests = 0,
    skippedTests = 0,
    startTime = 0,
    endTime = 0
}

-- 測試配置
TestRunner.Config = {
    verbose = true,
    stopOnFirstFailure = false,
    timeout = 30, -- 秒
    parallel = false
}

-- 測試套件註冊
TestRunner._testSuites = {}

-- 初始化測試運行器
function TestRunner:Initialize()
    print("🧪 TestRunner 已初始化")
    
    -- 重置統計
    self:ResetStats()
    
    -- 註冊測試套件
    self:RegisterTestSuites()
end

-- 註冊測試套件
function TestRunner:RegisterTestSuites()
    -- 安全載入測試模組的函數
    local function safeRequire(path)
        local success, module = pcall(function()
            return require(path)
        end)
        if success then
            return module
        else
            warn("⚠️ 無法載入測試模組:", path)
            return function()
                describe("Placeholder", function()
                    it("模組載入失敗", function()
                        expect(false).to.equal(true)
                    end)
                end)
            end
        end
    end

    -- ECS 系統測試
    self:RegisterTestSuite("ECS Systems", {
        safeRequire(script.Parent.ECS.HeroSystemTest),
        safeRequire(script.Parent.ECS.CombatSystemTest),
        safeRequire(script.Parent.ECS.MovementSystemTest),
        safeRequire(script.Parent.ECS.LevelingSystemTest)
    })

    -- Knit 服務測試
    self:RegisterTestSuite("Knit Services", {
        safeRequire(script.Parent.Services.PlayerServiceTest),
        safeRequire(script.Parent.Services.PetServiceTest),
        safeRequire(script.Parent.Services.CombatServiceTest),
        safeRequire(script.Parent.Services.ZoneServiceTest)
    })

    -- UI 組件測試
    self:RegisterTestSuite("UI Components", {
        safeRequire(script.Parent.UI.StateManagerTest),
        safeRequire(script.Parent.UI.StatusBarTest),
        safeRequire(script.Parent.UI.PetListTest),
        safeRequire(script.Parent.UI.NotificationSystemTest)
    })

    -- 配置和工具測試
    self:RegisterTestSuite("Configuration & Utils", {
        safeRequire(script.Parent.Config.GameConfigTest),
        safeRequire(script.Parent.Config.PetConfigTest),
        safeRequire(script.Parent.Config.ZoneConfigTest),
        safeRequire(script.Parent.Utils.UtilsTest)
    })

    -- 事件系統測試
    self:RegisterTestSuite("Event System", {
        safeRequire(script.Parent.Events.EventManagerTest)
    })
end

-- 註冊測試套件
function TestRunner:RegisterTestSuite(name, tests)
    self._testSuites[name] = tests
    print("📝 已註冊測試套件:", name, "包含", #tests, "個測試")
end

-- 運行所有測試
function TestRunner:RunAllTests()
    print("🚀 開始運行所有測試...")
    
    self:ResetStats()
    self.Stats.startTime = tick()
    
    local allPassed = true
    
    for suiteName, tests in pairs(self._testSuites) do
        print("\n📦 運行測試套件:", suiteName)
        
        local suiteResult = self:RunTestSuite(suiteName, tests)
        if not suiteResult then
            allPassed = false
            
            if self.Config.stopOnFirstFailure then
                break
            end
        end
    end
    
    self.Stats.endTime = tick()
    
    -- 觸發完成事件
    self.OnAllTestsCompleted:Fire({
        allPassed = allPassed,
        stats = self.Stats,
        duration = self.Stats.endTime - self.Stats.startTime
    })
    
    -- 顯示總結
    self:PrintSummary()
    
    return allPassed
end

-- 運行測試套件
function TestRunner:RunTestSuite(suiteName, tests)
    local suitePassed = true
    
    for i, testModule in ipairs(tests) do
        local testName = suiteName .. " #" .. i
        
        local success, result = self:RunSingleTest(testName, testModule)
        
        if not success then
            suitePassed = false
            
            if self.Config.stopOnFirstFailure then
                break
            end
        end
    end
    
    return suitePassed
end

-- 運行單個測試
function TestRunner:RunSingleTest(testName, testModule)
    self.Stats.totalTests = self.Stats.totalTests + 1
    
    -- 觸發測試開始事件
    self.OnTestStarted:Fire({
        testName = testName,
        timestamp = tick()
    })
    
    local success = false
    local errorMessage = nil
    
    -- 執行測試
    local startTime = tick()
    
    local ok, result = pcall(function()
        if type(testModule) == "function" then
            -- 直接執行測試函數
            testModule()
        else
            -- 使用 TestEZ 運行測試
            TestEZ.TestBootstrap:run({ testModule })
        end
    end)
    
    local duration = tick() - startTime
    
    if ok then
        success = true
        self.Stats.passedTests = self.Stats.passedTests + 1
        
        if self.Config.verbose then
            print("✅", testName, string.format("(%.2fs)", duration))
        end
        
        -- 觸發測試完成事件
        self.OnTestCompleted:Fire({
            testName = testName,
            success = true,
            duration = duration,
            timestamp = tick()
        })
    else
        success = false
        errorMessage = tostring(result)
        self.Stats.failedTests = self.Stats.failedTests + 1
        
        print("❌", testName, "失敗:", errorMessage)
        
        -- 觸發測試失敗事件
        self.OnTestFailed:Fire({
            testName = testName,
            error = errorMessage,
            duration = duration,
            timestamp = tick()
        })
    end
    
    return success, errorMessage
end

-- 運行特定測試套件
function TestRunner:RunTestSuite(suiteName)
    if not self._testSuites[suiteName] then
        warn("❌ 測試套件不存在:", suiteName)
        return false
    end
    
    print("🧪 運行測試套件:", suiteName)
    
    local tests = self._testSuites[suiteName]
    return self:RunTestSuite(suiteName, tests)
end

-- 重置統計
function TestRunner:ResetStats()
    self.Stats = {
        totalTests = 0,
        passedTests = 0,
        failedTests = 0,
        skippedTests = 0,
        startTime = 0,
        endTime = 0
    }
end

-- 打印測試總結
function TestRunner:PrintSummary()
    local duration = self.Stats.endTime - self.Stats.startTime
    
    print("\n" .. string.rep("=", 50))
    print("📊 測試總結")
    print(string.rep("=", 50))
    print("總測試數:", self.Stats.totalTests)
    print("通過:", self.Stats.passedTests)
    print("失敗:", self.Stats.failedTests)
    print("跳過:", self.Stats.skippedTests)
    print("總耗時:", string.format("%.2f秒", duration))
    
    local successRate = self.Stats.totalTests > 0 and 
        (self.Stats.passedTests / self.Stats.totalTests * 100) or 0
    print("成功率:", string.format("%.1f%%", successRate))
    
    if self.Stats.failedTests == 0 then
        print("🎉 所有測試通過！")
    else
        print("⚠️ 有", self.Stats.failedTests, "個測試失敗")
    end
    
    print(string.rep("=", 50))
end

-- 獲取測試統計
function TestRunner:GetStats()
    return self.Stats
end

-- 設置配置
function TestRunner:SetConfig(config)
    for key, value in pairs(config) do
        self.Config[key] = value
    end
end

-- 獲取配置
function TestRunner:GetConfig()
    return self.Config
end

-- 列出所有測試套件
function TestRunner:ListTestSuites()
    print("📋 已註冊的測試套件:")
    for suiteName, tests in pairs(self._testSuites) do
        print("  " .. suiteName .. " (" .. #tests .. " 個測試)")
    end
end

-- 檢查測試環境
function TestRunner:CheckEnvironment()
    print("🔍 檢查測試環境...")
    
    local checks = {
        { name = "TestEZ", check = function() return TestEZ ~= nil end },
        { name = "Signal", check = function() return Signal ~= nil end },
        { name = "Matter", check = function() 
            return pcall(function() 
                return require(game.ReplicatedStorage.Packages.Matter) 
            end) 
        end },
        { name = "Knit", check = function() 
            return pcall(function() 
                return require(game.ReplicatedStorage.Packages.Knit) 
            end) 
        end }
    }
    
    local allPassed = true
    
    for _, check in ipairs(checks) do
        local success = check.check()
        if success then
            print("✅", check.name, "可用")
        else
            print("❌", check.name, "不可用")
            allPassed = false
        end
    end
    
    if allPassed then
        print("✅ 測試環境檢查通過")
    else
        print("❌ 測試環境檢查失敗")
    end
    
    return allPassed
end

-- 清理測試運行器
function TestRunner:Cleanup()
    self.OnTestStarted:Destroy()
    self.OnTestCompleted:Destroy()
    self.OnTestFailed:Destroy()
    self.OnAllTestsCompleted:Destroy()
    
    self._testSuites = {}
    self:ResetStats()
    
    print("🧹 TestRunner 已清理")
end

return TestRunner
