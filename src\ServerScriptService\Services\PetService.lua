-- 寵物服務 - 處理寵物系統的所有邏輯
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Components = require(game.ReplicatedStorage.Components)
local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local PetService = Knit.CreateService({
    Name = "PetService",
    Client = {
        -- 客戶端可調用的方法
        RollEgg = Knit.CreateSignal(),
        GetPetData = Knit.CreateSignal(),
        GetActivePets = Knit.CreateSignal(),
        SetActivePets = Knit.CreateSignal(),
        ReleasePet = Knit.CreateSignal(),
        RenamePet = Knit.CreateSignal(),
        GetPokedex = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnPetObtained = Knit.CreateSignal(),
        OnPetLevelUp = Knit.CreateSignal(),
        OnPetReleased = Knit.CreateSignal(),
        OnPokedexUpdated = Knit.CreateSignal(),
        OnActivePetsChanged = Knit.CreateSignal()
    }
})

function PetService:KnitInit()
    self._worldManager = _G.WorldManager
    self._world = _G.ECSWorld
    print("🐾 PetService 初始化完成")
end

function PetService:KnitStart()
    print("🎮 PetService 啟動")
end

-- 客戶端方法：扭蛋系統
function PetService.Client:RollEgg(player, eggType)
    return self.Server:RollEgg(player, eggType)
end

function PetService:RollEgg(player, eggType)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    -- 檢查貨幣
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if not currency then
        return { success = false, error = "貨幣數據未找到" }
    end
    
    -- 獲取扭蛋價格
    local eggPrice = GameConfig.ECONOMY.EGG_PRICES[string.upper(eggType)]
    if not eggPrice then
        return { success = false, error = "無效的扭蛋類型" }
    end
    
    -- 檢查是否有足夠貨幣
    local currentAmount = currency[eggPrice.currency]
    if currentAmount < eggPrice.amount then
        return { 
            success = false, 
            error = "貨幣不足",
            required = eggPrice.amount,
            current = currentAmount
        }
    end
    
    -- 扣除貨幣
    local newCurrency = Utils.deepCopy(currency)
    newCurrency[eggPrice.currency] = currentAmount - eggPrice.amount
    
    self._world:insert(playerEntity, Components.CurrencyComponent(newCurrency))
    
    -- 計算扭蛋結果
    local petData = self:generateRandomPet(eggType)
    if not petData then
        return { success = false, error = "扭蛋生成失敗" }
    end
    
    -- 創建寵物實體
    local petEntity = self._worldManager:createPetEntity(petData, player.UserId)
    if not petEntity then
        return { success = false, error = "寵物實體創建失敗" }
    end
    
    -- 更新圖鑑
    self:updatePokedex(playerEntity, petData.speciesId)
    
    -- 通知客戶端
    self.Client.OnPetObtained:Fire(player, {
        petData = petData,
        petEntity = petEntity,
        eggType = eggType,
        timestamp = tick()
    })
    
    print("🎉 玩家", player.Name, "獲得寵物:", petData.name, "稀有度:", petData.rarity)
    
    return {
        success = true,
        petData = petData,
        petEntity = petEntity,
        remainingCurrency = newCurrency
    }
end

-- 生成隨機寵物
function PetService:generateRandomPet(eggType)
    local gachaRates = GameConfig.PET.GACHA_RATES[string.upper(eggType)]
    if not gachaRates then
        return nil
    end
    
    -- 根據機率選擇稀有度
    local rarity = Utils.weightedRandom(gachaRates)
    if not rarity then
        return nil
    end
    
    -- 獲取該稀有度的寵物列表
    local rarityPets = PetConfig:GetPetsByRarity(string.lower(rarity))
    if #rarityPets == 0 then
        return nil
    end
    
    -- 隨機選擇寵物
    local speciesId = Utils.randomChoice(rarityPets)
    local speciesConfig = PetConfig.Species[speciesId]
    
    if not speciesConfig then
        return nil
    end
    
    -- 檢查是否為閃光寵物 (1% 機率)
    local isShiny = math.random() < 0.01
    
    return {
        speciesId = speciesId,
        name = speciesConfig.name .. (isShiny and " ✨" or ""),
        rarity = string.lower(rarity),
        level = 1,
        experience = 0,
        isShiny = isShiny,
        obtainedAt = tick()
    }
end

-- 更新圖鑑
function PetService:updatePokedex(playerEntity, speciesId)
    local pokedex = self._world:get(playerEntity, Components.PokedexComponent)
    if not pokedex then
        return
    end
    
    local ownedSpecies = Utils.deepCopy(pokedex.ownedSpecies)
    ownedSpecies[speciesId] = (ownedSpecies[speciesId] or 0) + 1
    
    -- 計算完成度
    local totalSpecies = 0
    local ownedCount = 0
    
    for _ in pairs(PetConfig.Species) do
        totalSpecies = totalSpecies + 1
    end
    
    for _ in pairs(ownedSpecies) do
        ownedCount = ownedCount + 1
    end
    
    local completionRate = totalSpecies > 0 and (ownedCount / totalSpecies) or 0
    
    self._world:insert(playerEntity, Components.PokedexComponent({
        encounteredSpecies = pokedex.encounteredSpecies,
        ownedSpecies = ownedSpecies,
        completionRate = completionRate
    }))
    
    -- 通知客戶端圖鑑更新
    local player = game.Players:GetPlayerByUserId(
        self._world:get(playerEntity, Components.PlayerComponent).userId
    )
    
    if player then
        self.Client.OnPokedexUpdated:Fire(player, {
            speciesId = speciesId,
            newCount = ownedSpecies[speciesId],
            completionRate = completionRate,
            timestamp = tick()
        })
    end
end

-- 客戶端方法：獲取寵物數據
function PetService.Client:GetPetData(player, petEntityId)
    return self.Server:GetPetData(player, petEntityId)
end

function PetService:GetPetData(player, petEntityId)
    if not self._world:contains(petEntityId) then
        return { success = false, error = "寵物不存在" }
    end
    
    local pet = self._world:get(petEntityId, Components.PetComponent)
    local stats = self._world:get(petEntityId, Components.PetStatsComponent)
    
    if not pet or pet.ownerId ~= player.UserId then
        return { success = false, error = "無權訪問此寵物" }
    end
    
    return {
        success = true,
        pet = pet,
        stats = stats
    }
end

-- 客戶端方法：獲取活躍寵物
function PetService.Client:GetActivePets(player)
    return self.Server:GetActivePets(player)
end

function PetService:GetActivePets(player)
    local playerPets = self._worldManager:findPlayerPets(player.UserId)
    local activePets = {}
    
    -- 這裡可以添加邏輯來確定哪些寵物是活躍的
    -- 暫時返回前5隻寵物
    for i = 1, math.min(5, #playerPets) do
        table.insert(activePets, {
            entityId = playerPets[i].entityId,
            pet = playerPets[i].pet
        })
    end
    
    return {
        success = true,
        activePets = activePets
    }
end

-- 客戶端方法：釋放寵物
function PetService.Client:ReleasePet(player, petEntityId)
    return self.Server:ReleasePet(player, petEntityId)
end

function PetService:ReleasePet(player, petEntityId)
    if not self._world:contains(petEntityId) then
        return { success = false, error = "寵物不存在" }
    end
    
    local pet = self._world:get(petEntityId, Components.PetComponent)
    if not pet or pet.ownerId ~= player.UserId then
        return { success = false, error = "無權釋放此寵物" }
    end
    
    -- 計算釋放獎勵
    local coinReward = self:calculateReleaseReward(pet)
    
    -- 給予獎勵
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if playerEntity then
        local currency = self._world:get(playerEntity, Components.CurrencyComponent)
        if currency then
            self._world:insert(playerEntity, Components.CurrencyComponent({
                coins = currency.coins + coinReward,
                gems = currency.gems,
                robux = currency.robux,
                experience = currency.experience
            }))
        end
    end
    
    -- 移除寵物實體
    self._world:despawn(petEntityId)
    
    -- 通知客戶端
    self.Client.OnPetReleased:Fire(player, {
        petName = pet.name,
        coinReward = coinReward,
        timestamp = tick()
    })
    
    print("🗑️ 玩家", player.Name, "釋放寵物:", pet.name, "獲得金幣:", coinReward)
    
    return {
        success = true,
        coinReward = coinReward
    }
end

-- 計算釋放獎勵
function PetService:calculateReleaseReward(pet)
    local baseReward = 100
    local rarityMultiplier = GameConfig.PET.RARITY[string.upper(pet.rarity)].multiplier
    local levelBonus = pet.level * 10
    
    return math.floor(baseReward * rarityMultiplier + levelBonus)
end

-- 客戶端方法：重命名寵物
function PetService.Client:RenamePet(player, petEntityId, newName)
    return self.Server:RenamePet(player, petEntityId, newName)
end

function PetService:RenamePet(player, petEntityId, newName)
    if not self._world:contains(petEntityId) then
        return { success = false, error = "寵物不存在" }
    end
    
    local pet = self._world:get(petEntityId, Components.PetComponent)
    if not pet or pet.ownerId ~= player.UserId then
        return { success = false, error = "無權重命名此寵物" }
    end
    
    -- 驗證名稱
    local sanitizedName = Utils.sanitizeInput(newName, 20)
    if sanitizedName == "" then
        return { success = false, error = "無效的名稱" }
    end
    
    -- 更新寵物名稱
    self._world:insert(petEntityId, Components.PetComponent({
        speciesId = pet.speciesId,
        name = sanitizedName,
        rarity = pet.rarity,
        level = pet.level,
        experience = pet.experience,
        isShiny = pet.isShiny,
        obtainedAt = pet.obtainedAt,
        ownerId = pet.ownerId
    }))
    
    print("✏️ 玩家", player.Name, "重命名寵物:", pet.name, "->", sanitizedName)
    
    return {
        success = true,
        newName = sanitizedName
    }
end

-- 客戶端方法：獲取圖鑑
function PetService.Client:GetPokedex(player)
    return self.Server:GetPokedex(player)
end

function PetService:GetPokedex(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local pokedex = self._world:get(playerEntity, Components.PokedexComponent)
    if not pokedex then
        return { success = false, error = "圖鑑數據未找到" }
    end
    
    return {
        success = true,
        pokedex = pokedex
    }
end

-- 給寵物增加經驗值
function PetService:givePetExperience(petEntityId, amount)
    if not self._world:contains(petEntityId) then
        return false
    end
    
    local LevelingSystem = self._worldManager:getSystem("LevelingSystem")
    if LevelingSystem then
        return LevelingSystem:givePetExperience(self._world, petEntityId, amount)
    end
    
    return false
end

-- 獲取玩家所有寵物
function PetService:getPlayerPets(userId)
    return self._worldManager:findPlayerPets(userId)
end

return PetService
