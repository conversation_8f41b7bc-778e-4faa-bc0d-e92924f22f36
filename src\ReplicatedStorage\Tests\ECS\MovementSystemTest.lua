-- 移動系統測試
return function()
    local Matter = require(game.ReplicatedStorage.Packages.Matter)
    local Components = require(game.ReplicatedStorage.Components)
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
    
    describe("MovementSystem", function()
        local world
        local movementSystem
        
        beforeEach(function()
            -- 創建測試世界
            world = Matter.World.new()
            
            -- 創建移動系統實例
            local MovementSystem = require(game.ServerScriptService.Systems.MovementSystem)
            movementSystem = MovementSystem.new()
        end)
        
        afterEach(function()
            if world then
                world:clear()
            end
        end)
        
        it("應該正確處理基本移動", function()
            -- 創建可移動實體
            local entity = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(0, 0, 0),
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.MovementComponent({
                    velocity = Vector3.new(5, 0, 0), -- 向X軸移動
                    speed = 10,
                    direction = Vector3.new(1, 0, 0),
                    isMoving = true
                })
            )
            
            -- 模擬一幀的移動 (假設60FPS)
            local deltaTime = 1/60
            local movement = world:get(entity, Components.MovementComponent)
            local transform = world:get(entity, Components.TransformComponent)
            
            local newPosition = transform.position + movement.velocity * deltaTime
            
            world:insert(entity, Components.TransformComponent({
                position = newPosition,
                rotation = transform.rotation,
                scale = transform.scale
            }))
            
            -- 驗證位置更新
            local updatedTransform = world:get(entity, Components.TransformComponent)
            local expectedX = 5 * deltaTime -- 約 0.083
            expect(math.abs(updatedTransform.position.X - expectedX)).to.be.lessThan(0.001)
            expect(updatedTransform.position.Y).to.equal(0)
            expect(updatedTransform.position.Z).to.equal(0)
        end)
        
        it("應該正確處理速度限制", function()
            -- 創建實體
            local entity = world:spawn(
                Components.MovementComponent({
                    velocity = Vector3.new(0, 0, 0),
                    speed = 10,
                    direction = Vector3.new(1, 0, 0),
                    isMoving = false
                })
            )
            
            -- 設置超過最大速度的速度
            local maxSpeed = 10
            local desiredVelocity = Vector3.new(15, 0, 0) -- 超過最大速度
            
            -- 限制速度
            local velocityMagnitude = desiredVelocity.Magnitude
            local limitedVelocity = desiredVelocity
            
            if velocityMagnitude > maxSpeed then
                limitedVelocity = desiredVelocity.Unit * maxSpeed
            end
            
            world:insert(entity, Components.MovementComponent({
                velocity = limitedVelocity,
                speed = maxSpeed,
                direction = limitedVelocity.Unit,
                isMoving = true
            }))
            
            -- 驗證速度限制
            local movement = world:get(entity, Components.MovementComponent)
            expect(movement.velocity.Magnitude).to.be.lessThanOrEqualTo(maxSpeed)
            expect(movement.velocity.X).to.equal(maxSpeed)
        end)
        
        it("應該正確處理方向變更", function()
            -- 創建實體
            local entity = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(0, 0, 0),
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.MovementComponent({
                    velocity = Vector3.new(0, 0, 0),
                    speed = 10,
                    direction = Vector3.new(1, 0, 0),
                    isMoving = false
                })
            )
            
            -- 改變移動方向
            local newDirection = Vector3.new(0, 0, 1) -- 向Z軸移動
            local speed = 10
            local newVelocity = newDirection * speed
            
            world:insert(entity, Components.MovementComponent({
                velocity = newVelocity,
                speed = speed,
                direction = newDirection,
                isMoving = true
            }))
            
            -- 更新旋轉以面向移動方向
            local transform = world:get(entity, Components.TransformComponent)
            local lookDirection = newDirection
            local rotationY = math.atan2(lookDirection.X, lookDirection.Z)
            
            world:insert(entity, Components.TransformComponent({
                position = transform.position,
                rotation = Vector3.new(0, math.deg(rotationY), 0),
                scale = transform.scale
            }))
            
            -- 驗證方向和旋轉
            local movement = world:get(entity, Components.MovementComponent)
            local updatedTransform = world:get(entity, Components.TransformComponent)
            
            expect(movement.direction.Z).to.equal(1)
            expect(movement.direction.X).to.equal(0)
            expect(updatedTransform.rotation.Y).to.equal(0) -- 面向Z軸正方向
        end)
        
        it("應該正確處理停止移動", function()
            -- 創建移動中的實體
            local entity = world:spawn(
                Components.MovementComponent({
                    velocity = Vector3.new(5, 0, 0),
                    speed = 10,
                    direction = Vector3.new(1, 0, 0),
                    isMoving = true
                })
            )
            
            -- 停止移動
            world:insert(entity, Components.MovementComponent({
                velocity = Vector3.new(0, 0, 0),
                speed = 10,
                direction = Vector3.new(1, 0, 0), -- 保持方向
                isMoving = false
            }))
            
            -- 驗證停止狀態
            local movement = world:get(entity, Components.MovementComponent)
            expect(movement.isMoving).to.equal(false)
            expect(movement.velocity.Magnitude).to.equal(0)
        end)
        
        it("應該正確處理碰撞檢測", function()
            -- 創建兩個實體
            local entity1 = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(0, 0, 0),
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(2, 2, 2) -- 2x2x2 的碰撞盒
                })
            )
            
            local entity2 = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(1, 0, 0), -- 1單位距離
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(2, 2, 2) -- 2x2x2 的碰撞盒
                })
            )
            
            -- 檢查碰撞
            local transform1 = world:get(entity1, Components.TransformComponent)
            local transform2 = world:get(entity2, Components.TransformComponent)
            
            local distance = (transform1.position - transform2.position).Magnitude
            local collisionDistance = (transform1.scale.X + transform2.scale.X) / 2
            
            local isColliding = distance < collisionDistance
            expect(isColliding).to.equal(true) -- 應該發生碰撞
        end)
        
        it("應該正確處理邊界限制", function()
            -- 創建實體
            local entity = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(95, 0, 0), -- 接近邊界
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.MovementComponent({
                    velocity = Vector3.new(10, 0, 0), -- 向邊界移動
                    speed = 10,
                    direction = Vector3.new(1, 0, 0),
                    isMoving = true
                })
            )
            
            -- 設置世界邊界
            local worldBounds = {
                minX = -100, maxX = 100,
                minZ = -100, maxZ = 100
            }
            
            -- 模擬移動到邊界
            local transform = world:get(entity, Components.TransformComponent)
            local movement = world:get(entity, Components.MovementComponent)
            
            local deltaTime = 1 -- 1秒
            local newPosition = transform.position + movement.velocity * deltaTime
            
            -- 應用邊界限制
            newPosition = Vector3.new(
                math.clamp(newPosition.X, worldBounds.minX, worldBounds.maxX),
                newPosition.Y,
                math.clamp(newPosition.Z, worldBounds.minZ, worldBounds.maxZ)
            )
            
            world:insert(entity, Components.TransformComponent({
                position = newPosition,
                rotation = transform.rotation,
                scale = transform.scale
            }))
            
            -- 驗證邊界限制
            local updatedTransform = world:get(entity, Components.TransformComponent)
            expect(updatedTransform.position.X).to.equal(worldBounds.maxX)
        end)
        
        it("應該正確處理重力", function()
            -- 創建空中的實體
            local entity = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(0, 10, 0), -- 在空中
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.MovementComponent({
                    velocity = Vector3.new(0, 0, 0),
                    speed = 10,
                    direction = Vector3.new(0, 0, 0),
                    isMoving = false
                })
            )
            
            -- 應用重力
            local gravity = -50 -- 重力加速度
            local deltaTime = 1/60
            
            local movement = world:get(entity, Components.MovementComponent)
            local newVelocityY = movement.velocity.Y + gravity * deltaTime
            
            world:insert(entity, Components.MovementComponent({
                velocity = Vector3.new(movement.velocity.X, newVelocityY, movement.velocity.Z),
                speed = movement.speed,
                direction = movement.direction,
                isMoving = movement.isMoving
            }))
            
            -- 驗證重力效果
            local updatedMovement = world:get(entity, Components.MovementComponent)
            expect(updatedMovement.velocity.Y).to.be.lessThan(0) -- 向下的速度
        end)
        
        it("應該正確處理地面檢測", function()
            -- 創建接近地面的實體
            local entity = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(0, 1, 0), -- 距離地面1單位
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.MovementComponent({
                    velocity = Vector3.new(0, -5, 0), -- 向下移動
                    speed = 10,
                    direction = Vector3.new(0, -1, 0),
                    isMoving = true
                })
            )
            
            -- 檢查地面碰撞
            local transform = world:get(entity, Components.TransformComponent)
            local groundLevel = 0
            local entityBottom = transform.position.Y - transform.scale.Y / 2
            
            local isOnGround = entityBottom <= groundLevel
            
            if isOnGround then
                -- 停止下降，設置到地面
                world:insert(entity, Components.TransformComponent({
                    position = Vector3.new(transform.position.X, groundLevel + transform.scale.Y / 2, transform.position.Z),
                    rotation = transform.rotation,
                    scale = transform.scale
                }))
                
                world:insert(entity, Components.MovementComponent({
                    velocity = Vector3.new(0, 0, 0),
                    speed = 10,
                    direction = Vector3.new(0, 0, 0),
                    isMoving = false
                }))
            end
            
            -- 驗證地面檢測
            local updatedTransform = world:get(entity, Components.TransformComponent)
            local updatedMovement = world:get(entity, Components.MovementComponent)
            
            expect(updatedTransform.position.Y).to.equal(groundLevel + transform.scale.Y / 2)
            expect(updatedMovement.velocity.Y).to.equal(0)
        end)
    end)
end
