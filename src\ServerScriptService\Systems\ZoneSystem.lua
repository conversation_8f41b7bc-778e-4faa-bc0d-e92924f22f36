-- 區域系統 - 處理玩家區域切換和區域特定邏輯
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local ZoneSystem = {}

-- 區域邊界定義 (基於實際地圖位置)
local ZONE_BOUNDARIES = {
    castle = {
        center = Vector3.new(0, 0, 0),
        size = Vector3.new(100, 50, 100)
    },
    forest = {
        center = Vector3.new(200, 0, 0),
        size = Vector3.new(150, 50, 150)
    },
    ice = {
        center = Vector3.new(-200, 0, 200),
        size = Vector3.new(150, 50, 150)
    },
    lava = {
        center = Vector3.new(0, 0, 400),
        size = Vector3.new(200, 50, 200)
    }
}

function ZoneSystem:step(world, state)
    -- 檢查玩家區域變化
    self:checkPlayerZoneChanges(world, state)
    
    -- 處理區域特定邏輯
    self:processZoneLogic(world, state)
    
    -- 生成怪物
    self:spawnMonstersInZones(world, state)
end

function ZoneSystem:checkPlayerZoneChanges(world, state)
    for entityId, player, transform, zone in world:query(
        Components.PlayerComponent,
        Components.TransformComponent,
        Components.ZoneComponent
    ) do
        local currentZone = self:getZoneFromPosition(transform.position)
        
        if currentZone ~= zone.currentZone then
            -- 檢查玩家是否有權限進入新區域
            if self:canEnterZone(world, entityId, currentZone) then
                self:changePlayerZone(world, entityId, zone.currentZone, currentZone, player)
            else
                -- 將玩家傳送回允許的區域
                self:teleportToAllowedZone(world, entityId, zone)
            end
        end
    end
end

function ZoneSystem:processZoneLogic(world, state)
    for entityId, player, zone in world:query(
        Components.PlayerComponent,
        Components.ZoneComponent
    ) do
        local zoneConfig = GameConfig.ZONES[string.upper(zone.currentZone)]
        if not zoneConfig then continue end
        
        -- 安全區域邏輯
        if zoneConfig.isSafeZone then
            self:applySafeZoneEffects(world, entityId)
        end
        
        -- PvP 區域邏輯
        if zoneConfig.isPvPEnabled then
            self:applyPvPZoneEffects(world, entityId)
        end
    end
end

function ZoneSystem:spawnMonstersInZones(world, state)
    -- 每30秒檢查一次怪物生成
    if math.floor(state.currentTime) % 30 ~= 0 then
        return
    end
    
    for zoneName, zoneConfig in pairs(GameConfig.ZONES) do
        if zoneConfig.monsterSpawn then
            self:spawnMonstersInZone(world, string.lower(zoneName), zoneConfig)
        end
    end
end

function ZoneSystem:getZoneFromPosition(position)
    for zoneName, boundary in pairs(ZONE_BOUNDARIES) do
        local center = boundary.center
        local size = boundary.size
        
        local minX = center.X - size.X / 2
        local maxX = center.X + size.X / 2
        local minZ = center.Z - size.Z / 2
        local maxZ = center.Z + size.Z / 2
        
        if position.X >= minX and position.X <= maxX and
           position.Z >= minZ and position.Z <= maxZ then
            return zoneName
        end
    end
    
    return "castle" -- 默認區域
end

function ZoneSystem:canEnterZone(world, entityId, zoneName)
    local zone = world:get(entityId, Components.ZoneComponent)
    if not zone then return false end
    
    -- 檢查是否在允許的區域列表中
    for _, allowedZone in ipairs(zone.allowedZones) do
        if allowedZone == zoneName then
            return true
        end
    end
    
    return false
end

function ZoneSystem:changePlayerZone(world, entityId, oldZone, newZone, player)
    -- 更新區域組件
    local zone = world:get(entityId, Components.ZoneComponent)
    if zone then
        world:insert(entityId, Components.ZoneComponent({
            currentZone = newZone,
            lastZoneChange = tick(),
            allowedZones = zone.allowedZones
        }))
    end
    
    -- 觸發區域變化事件
    self:onZoneChanged(world, entityId, oldZone, newZone, player)
    
    print("🗺️ 玩家區域變化:", player.displayName, oldZone, "->", newZone)
end

function ZoneSystem:teleportToAllowedZone(world, entityId, zone)
    -- 傳送到最後一個允許的區域
    local allowedZone = zone.allowedZones[#zone.allowedZones] or "castle"
    local boundary = ZONE_BOUNDARIES[allowedZone]
    
    if boundary then
        local MovementSystem = require(script.Parent.MovementSystem)
        MovementSystem:teleportEntity(world, entityId, boundary.center)
        
        print("⚠️ 玩家被傳送回允許區域:", allowedZone)
    end
end

function ZoneSystem:applySafeZoneEffects(world, entityId)
    -- 在安全區域中，玩家血量緩慢回復
    local health = world:get(entityId, Components.HealthComponent)
    local hero = world:get(entityId, Components.HeroComponent)
    
    if health and hero and health.current < health.maximum then
        local regenAmount = 2 -- 每次回復2點血量
        local newHealth = math.min(health.maximum, health.current + regenAmount)
        
        world:insert(entityId, Components.HealthComponent({
            current = newHealth,
            maximum = health.maximum,
            regeneration = health.regeneration,
            lastRegenTime = tick()
        }))
    end
    
    -- 清除戰鬥狀態
    local combat = world:get(entityId, Components.CombatComponent)
    if combat and combat.isInCombat then
        world:insert(entityId, Components.CombatComponent({
            isInCombat = false,
            target = nil,
            lastAttackTime = 0,
            combatStartTime = 0
        }))
    end
end

function ZoneSystem:applyPvPZoneEffects(world, entityId)
    -- PvP 區域的特殊效果可以在這裡添加
    -- 例如：增加經驗值獲得、特殊 buff 等
end

function ZoneSystem:spawnMonstersInZone(world, zoneName, zoneConfig)
    -- 計算當前區域的怪物數量
    local monsterCount = 0
    for entityId, monster in world:query(Components.MonsterComponent) do
        if monster.spawnZone == zoneName then
            monsterCount = monsterCount + 1
        end
    end
    
    -- 最大怪物數量限制
    local maxMonsters = 10
    if monsterCount >= maxMonsters then
        return
    end
    
    -- 獲取區域的寵物列表
    local zonePets = PetConfig:GetZonePets(string.upper(zoneName))
    if #zonePets == 0 then
        return
    end
    
    -- 隨機選擇一個寵物作為怪物
    local randomPet = Utils.randomChoice(zonePets)
    local petConfig = PetConfig.Species[randomPet]
    
    if petConfig then
        self:spawnMonster(world, zoneName, randomPet, petConfig)
    end
end

function ZoneSystem:spawnMonster(world, zoneName, speciesId, petConfig)
    local boundary = ZONE_BOUNDARIES[zoneName]
    if not boundary then return end
    
    -- 隨機生成位置
    local spawnPos = Vector3.new(
        boundary.center.X + (math.random() - 0.5) * boundary.size.X * 0.8,
        boundary.center.Y,
        boundary.center.Z + (math.random() - 0.5) * boundary.size.Z * 0.8
    )
    
    -- 創建怪物實體
    local monsterEntity = world:spawn(
        Components.MonsterComponent({
            speciesId = speciesId,
            spawnZone = zoneName,
            spawnTime = tick(),
            respawnTime = 300, -- 5分鐘重生
            isAlive = true,
            encounterCount = 0
        }),
        Components.TransformComponent({
            position = spawnPos,
            rotation = Vector3.new(0, math.random() * 360, 0),
            scale = Vector3.new(1, 1, 1)
        }),
        Components.HealthComponent({
            current = petConfig.baseStats.health,
            maximum = petConfig.baseStats.health,
            regeneration = 1,
            lastRegenTime = tick()
        }),
        Components.MovementComponent({
            velocity = Vector3.new(0, 0, 0),
            speed = petConfig.baseStats.speed * 0.5, -- 怪物移動較慢
            isMoving = false,
            destination = nil,
            path = {}
        }),
        Components.CombatComponent({
            isInCombat = false,
            target = nil,
            lastAttackTime = 0,
            combatStartTime = 0
        })
    )
    
    print("👹 怪物生成:", petConfig.name, "在", zoneName, "位置:", spawnPos)
    return monsterEntity
end

function ZoneSystem:onZoneChanged(world, entityId, oldZone, newZone, player)
    -- 通知客戶端區域變化
    local Knit = require(game.ReplicatedStorage.Packages.Knit)
    local success, ZoneService = pcall(function()
        return Knit.GetService("ZoneService")
    end)
    
    if success and ZoneService then
        local robloxPlayer = game.Players:GetPlayerByUserId(player.userId)
        if robloxPlayer then
            ZoneService.Client.OnZoneChanged:Fire(robloxPlayer, {
                oldZone = oldZone,
                newZone = newZone,
                timestamp = tick()
            })
        end
    end
end

-- 公共方法：強制改變玩家區域
function ZoneSystem:forceChangePlayerZone(world, entityId, zoneName)
    local player = world:get(entityId, Components.PlayerComponent)
    local zone = world:get(entityId, Components.ZoneComponent)
    
    if not player or not zone then
        return false
    end
    
    -- 檢查區域是否存在
    local boundary = ZONE_BOUNDARIES[zoneName]
    if not boundary then
        return false
    end
    
    -- 傳送玩家到新區域
    local MovementSystem = require(script.Parent.MovementSystem)
    MovementSystem:teleportEntity(world, entityId, boundary.center)
    
    -- 更新區域組件
    world:insert(entityId, Components.ZoneComponent({
        currentZone = zoneName,
        lastZoneChange = tick(),
        allowedZones = zone.allowedZones
    }))
    
    -- 觸發區域變化事件
    self:onZoneChanged(world, entityId, zone.currentZone, zoneName, player)
    
    return true
end

-- 公共方法：獲取區域信息
function ZoneSystem:getZoneInfo(zoneName)
    local zoneConfig = GameConfig.ZONES[string.upper(zoneName)]
    local boundary = ZONE_BOUNDARIES[zoneName]
    
    return {
        config = zoneConfig,
        boundary = boundary,
        exists = zoneConfig ~= nil and boundary ~= nil
    }
end

-- 公共方法：獲取玩家當前區域
function ZoneSystem:getPlayerCurrentZone(world, entityId)
    local zone = world:get(entityId, Components.ZoneComponent)
    return zone and zone.currentZone or nil
end

-- 公共方法：檢查玩家是否可以進入指定區域
function ZoneSystem:checkZoneAccess(world, entityId, zoneName)
    return self:canEnterZone(world, entityId, zoneName)
end

return ZoneSystem
