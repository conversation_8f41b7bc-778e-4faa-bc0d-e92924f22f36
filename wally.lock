# This file is automatically @generated by <PERSON>.
# It is not intended for manual editing.
registry = "test"

[[package]]
name = "britton<PERSON>scher/profileservice"
version = "2.1.5"
dependencies = []

[[package]]
name = "elttob/fusion"
version = "0.3.0"
dependencies = []

[[package]]
name = "evaera/promise"
version = "4.0.0"
dependencies = []

[[package]]
name = "matter-ecs/matter"
version = "0.8.5"
dependencies = []

[[package]]
name = "mattschrubb/zoneplus"
version = "3.2.0"
dependencies = []

[[package]]
name = "petsim99lua/game"
version = "0.1.0"
dependencies = [["Fusion", "elttob/fusion@0.3.0"], ["Knit", "sleitnick/knit@1.7.0"], ["Matter", "matter-ecs/matter@0.8.5"], ["ProfileService", "brittonfischer/profileservice@2.1.5"], ["Promise", "evaera/promise@4.0.0"], ["Signal", "sleitnick/signal@1.5.0"], ["TableUtil", "sleitnick/table-util@1.2.1"], ["TestEZ", "roblox/testez@0.4.1"], ["ZonePlus", "mattschrubb/zoneplus@3.2.0"]]

[[package]]
name = "roblox/testez"
version = "0.4.1"
dependencies = []

[[package]]
name = "sleitnick/comm"
version = "1.0.1"
dependencies = [["Option", "sleitnick/option@1.0.5"], ["Promise", "evaera/promise@4.0.0"], ["Signal", "sleitnick/signal@2.0.3"]]

[[package]]
name = "sleitnick/knit"
version = "1.7.0"
dependencies = [["Comm", "sleitnick/comm@1.0.1"], ["Promise", "evaera/promise@4.0.0"]]

[[package]]
name = "sleitnick/option"
version = "1.0.5"
dependencies = []

[[package]]
name = "sleitnick/signal"
version = "1.5.0"
dependencies = []

[[package]]
name = "sleitnick/signal"
version = "2.0.3"
dependencies = []

[[package]]
name = "sleitnick/table-util"
version = "1.2.1"
dependencies = []
