-- 裝備管理系統
local EquipmentManager = {}

-- 依賴
local EquipmentConfig = require(game.ReplicatedStorage.Configuration.EquipmentConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 裝備數據存儲
EquipmentManager.playerEquipment = {} -- playerId -> {slot -> equipmentData}
EquipmentManager.playerInventory = {} -- playerId -> {equipmentId -> equipmentData}
EquipmentManager.isInitialized = false

-- 初始化裝備管理系統
function EquipmentManager:Initialize()
    if self.isInitialized then
        warn("⚠️ 裝備管理系統已經初始化")
        return true
    end
    
    print("⚔️ 初始化裝備管理系統...")
    
    -- 重置數據
    self.playerEquipment = {}
    self.playerInventory = {}
    
    -- 設置事件處理器
    self:SetupEventHandlers()
    
    self.isInitialized = true
    print("✅ 裝備管理系統初始化完成")
    
    return true
end

-- 設置事件處理器
function EquipmentManager:SetupEventHandlers()
    -- 監聽玩家加入事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_JOINED, function(data)
        self:OnPlayerJoined(data.player)
    end)
    
    -- 監聽玩家離開事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_LEFT, function(data)
        self:OnPlayerLeft(data.player)
    end)
end

-- 玩家加入事件處理
function EquipmentManager:OnPlayerJoined(player)
    -- 初始化玩家裝備數據
    self.playerEquipment[player.UserId] = {}
    self.playerInventory[player.UserId] = {}
    
    -- 給新玩家一些基礎裝備
    self:GiveStarterEquipment(player)
    
    print(string.format("⚔️ %s 的裝備系統已初始化", player.Name))
end

-- 玩家離開事件處理
function EquipmentManager:OnPlayerLeft(player)
    -- 保存裝備數據
    self:SavePlayerEquipment(player)
    
    -- 清理數據
    self.playerEquipment[player.UserId] = nil
    self.playerInventory[player.UserId] = nil
    
    print(string.format("💾 %s 的裝備數據已保存", player.Name))
end

-- 給新玩家基礎裝備
function EquipmentManager:GiveStarterEquipment(player)
    -- 根據職業給予不同的起始裝備
    local heroData = self:GetPlayerHeroData(player) -- 需要與英雄系統整合
    local heroClass = heroData and heroData.class or "WARRIOR"
    
    local starterEquipment = {}
    
    if heroClass == "WARRIOR" then
        starterEquipment = {"sword_basic", "chest_cloth"}
    elseif heroClass == "MAGE" then
        starterEquipment = {"staff_basic", "chest_cloth"}
    elseif heroClass == "ARCHER" then
        starterEquipment = {"bow_basic", "chest_cloth"}
    elseif heroClass == "CLERIC" then
        starterEquipment = {"wand_basic", "chest_cloth"}
    end
    
    -- 添加到背包
    for _, equipmentId in ipairs(starterEquipment) do
        self:AddEquipmentToInventory(player, equipmentId, "COMMON", 0)
    end
    
    print(string.format("🎁 給 %s 發放了 %s 起始裝備", player.Name, heroClass))
end

-- 創建裝備實例
function EquipmentManager:CreateEquipmentInstance(equipmentId, quality, enhanceLevel)
    -- 查找裝備配置
    local equipmentConfig = self:FindEquipmentConfig(equipmentId)
    if not equipmentConfig then
        warn("找不到裝備配置:", equipmentId)
        return nil
    end
    
    -- 計算最終屬性
    local finalStats = EquipmentConfig.STAT_FORMULAS.calculateFinalStats(
        equipmentConfig.baseStats, 
        quality, 
        enhanceLevel
    )
    
    -- 計算裝備評分
    local score = EquipmentConfig.STAT_FORMULAS.calculateEquipmentScore(
        finalStats,
        quality,
        enhanceLevel
    )
    
    -- 創建裝備實例
    local equipment = {
        id = equipmentId,
        instanceId = self:GenerateInstanceId(),
        name = equipmentConfig.name,
        description = equipmentConfig.description,
        slot = equipmentConfig.slot,
        requiredLevel = equipmentConfig.requiredLevel,
        suitableClasses = equipmentConfig.suitableClasses,
        icon = equipmentConfig.icon,
        
        -- 品質和強化
        quality = quality,
        enhanceLevel = enhanceLevel,
        
        -- 屬性
        baseStats = equipmentConfig.baseStats,
        finalStats = finalStats,
        
        -- 評分
        score = score,
        
        -- 時間戳
        createdTime = tick(),
        lastModified = tick()
    }
    
    return equipment
end

-- 查找裝備配置
function EquipmentManager:FindEquipmentConfig(equipmentId)
    -- 在武器中查找
    for _, weapon in ipairs(EquipmentConfig.WEAPONS) do
        if weapon.id == equipmentId then
            return weapon
        end
    end
    
    -- 在護甲中查找
    for _, armor in ipairs(EquipmentConfig.ARMOR) do
        if armor.id == equipmentId then
            return armor
        end
    end
    
    -- 在飾品中查找
    for _, accessory in ipairs(EquipmentConfig.ACCESSORIES) do
        if accessory.id == equipmentId then
            return accessory
        end
    end
    
    return nil
end

-- 添加裝備到背包
function EquipmentManager:AddEquipmentToInventory(player, equipmentId, quality, enhanceLevel)
    local equipment = self:CreateEquipmentInstance(equipmentId, quality, enhanceLevel)
    if not equipment then
        return false
    end

    -- 確保玩家背包存在
    if not self.playerInventory[player.UserId] then
        self.playerInventory[player.UserId] = {}
    end

    -- 添加到玩家背包
    local inventory = self.playerInventory[player.UserId]
    inventory[equipment.instanceId] = equipment
    
    -- 觸發裝備獲得事件
    EventManager:Fire(EventManager.EventTypes.EQUIPMENT_OBTAINED, {
        player = player,
        equipment = equipment
    })
    
    print(string.format("📦 %s 獲得了 %s (%s)", player.Name, equipment.name, quality))
    
    return true
end

-- 裝備物品
function EquipmentManager:EquipItem(player, instanceId)
    local inventory = self.playerInventory[player.UserId]
    local equipped = self.playerEquipment[player.UserId]
    
    if not inventory or not equipped then
        return false, "玩家數據不存在"
    end
    
    local equipment = inventory[instanceId]
    if not equipment then
        return false, "裝備不存在"
    end
    
    -- 檢查等級要求
    local playerLevel = self:GetPlayerLevel(player)
    if playerLevel < equipment.requiredLevel then
        return false, string.format("需要等級 %d", equipment.requiredLevel)
    end
    
    -- 檢查職業要求
    if equipment.suitableClasses then
        local heroClass = self:GetPlayerHeroClass(player)
        local classMatch = false
        for _, suitableClass in ipairs(equipment.suitableClasses) do
            if heroClass == suitableClass then
                classMatch = true
                break
            end
        end
        
        if not classMatch then
            return false, "職業不符合要求"
        end
    end
    
    local slot = equipment.slot
    
    -- 如果該部位已有裝備，先卸下
    if equipped[slot] then
        self:UnequipItem(player, slot)
    end
    
    -- 裝備新物品
    equipped[slot] = equipment
    inventory[instanceId] = nil
    
    -- 觸發裝備事件
    EventManager:Fire(EventManager.EventTypes.EQUIPMENT_EQUIPPED, {
        player = player,
        equipment = equipment,
        slot = slot
    })
    
    print(string.format("⚔️ %s 裝備了 %s", player.Name, equipment.name))
    
    return true, nil
end

-- 卸下裝備
function EquipmentManager:UnequipItem(player, slot)
    local equipped = self.playerEquipment[player.UserId]
    local inventory = self.playerInventory[player.UserId]
    
    if not equipped or not inventory then
        return false, "玩家數據不存在"
    end
    
    local equipment = equipped[slot]
    if not equipment then
        return false, "該部位沒有裝備"
    end
    
    -- 移動到背包
    equipped[slot] = nil
    inventory[equipment.instanceId] = equipment
    
    -- 觸發卸下事件
    EventManager:Fire(EventManager.EventTypes.EQUIPMENT_UNEQUIPPED, {
        player = player,
        equipment = equipment,
        slot = slot
    })
    
    print(string.format("📦 %s 卸下了 %s", player.Name, equipment.name))
    
    return true, nil
end

-- 強化裝備
function EquipmentManager:EnhanceEquipment(player, instanceId)
    local inventory = self.playerInventory[player.UserId]
    local equipped = self.playerEquipment[player.UserId]
    
    -- 在背包和裝備中查找
    local equipment = nil
    local isEquipped = false
    
    if inventory and inventory[instanceId] then
        equipment = inventory[instanceId]
    else
        -- 在已裝備中查找
        for slot, equippedItem in pairs(equipped or {}) do
            if equippedItem.instanceId == instanceId then
                equipment = equippedItem
                isEquipped = true
                break
            end
        end
    end
    
    if not equipment then
        return false, "裝備不存在"
    end
    
    local currentLevel = equipment.enhanceLevel
    if currentLevel >= EquipmentConfig.ENHANCEMENT.maxLevel then
        return false, "已達到最大強化等級"
    end
    
    local nextLevel = currentLevel + 1
    local successRate = EquipmentConfig.ENHANCEMENT.successRates[nextLevel]
    local materialCost = EquipmentConfig.ENHANCEMENT.materialCosts[nextLevel]
    
    -- 檢查材料
    if not self:CheckMaterials(player, materialCost) then
        return false, "材料不足"
    end
    
    -- 消耗材料
    self:ConsumeMaterials(player, materialCost)
    
    -- 強化嘗試
    local success = math.random(100) <= successRate
    
    if success then
        -- 強化成功
        equipment.enhanceLevel = nextLevel
        equipment.lastModified = tick()
        
        -- 重新計算屬性
        equipment.finalStats = EquipmentConfig.STAT_FORMULAS.calculateFinalStats(
            equipment.baseStats,
            equipment.quality,
            equipment.enhanceLevel
        )
        
        equipment.score = EquipmentConfig.STAT_FORMULAS.calculateEquipmentScore(
            equipment.finalStats,
            equipment.quality,
            equipment.enhanceLevel
        )
        
        -- 觸發強化成功事件
        EventManager:Fire(EventManager.EventTypes.EQUIPMENT_ENHANCED, {
            player = player,
            equipment = equipment,
            newLevel = nextLevel,
            success = true
        })
        
        print(string.format("✨ %s 的 %s 強化到 +%d", player.Name, equipment.name, nextLevel))
        
        return true, "強化成功"
    else
        -- 強化失敗
        local penalty = EquipmentConfig.ENHANCEMENT.failurePenalty[nextLevel]
        
        if penalty == "level_decrease" and equipment.enhanceLevel > 0 then
            equipment.enhanceLevel = equipment.enhanceLevel - 1
            print(string.format("💥 %s 的 %s 強化失敗，等級降低到 +%d", player.Name, equipment.name, equipment.enhanceLevel))
        elseif penalty == "equipment_destroy" then
            -- 銷毀裝備
            if isEquipped then
                for slot, equippedItem in pairs(equipped) do
                    if equippedItem.instanceId == instanceId then
                        equipped[slot] = nil
                        break
                    end
                end
            else
                inventory[instanceId] = nil
            end
            print(string.format("💥 %s 的 %s 強化失敗，裝備被銷毀", player.Name, equipment.name))
        else
            print(string.format("💥 %s 的 %s 強化失敗", player.Name, equipment.name))
        end
        
        -- 觸發強化失敗事件
        EventManager:Fire(EventManager.EventTypes.EQUIPMENT_ENHANCED, {
            player = player,
            equipment = equipment,
            newLevel = nextLevel,
            success = false,
            penalty = penalty
        })
        
        return false, "強化失敗"
    end
end

-- 獲取玩家裝備
function EquipmentManager:GetPlayerEquipment(player)
    return self.playerEquipment[player.UserId] or {}
end

-- 獲取玩家背包
function EquipmentManager:GetPlayerInventory(player)
    return self.playerInventory[player.UserId] or {}
end

-- 計算玩家總屬性加成
function EquipmentManager:GetPlayerEquipmentStats(player)
    local equipped = self.playerEquipment[player.UserId] or {}
    local totalStats = {}
    
    -- 累加所有裝備的屬性
    for slot, equipment in pairs(equipped) do
        for statName, value in pairs(equipment.finalStats) do
            totalStats[statName] = (totalStats[statName] or 0) + value
        end
    end
    
    -- 檢查套裝加成
    local setBonuses = self:CalculateSetBonuses(player)
    for statName, value in pairs(setBonuses) do
        totalStats[statName] = (totalStats[statName] or 0) + value
    end
    
    return totalStats
end

-- 計算套裝加成
function EquipmentManager:CalculateSetBonuses(player)
    local equipped = self.playerEquipment[player.UserId] or {}
    local setBonuses = {}
    
    -- 檢查每個套裝
    for setName, setConfig in pairs(EquipmentConfig.EQUIPMENT_SETS) do
        local equippedPieces = 0
        
        -- 計算已裝備的套裝件數
        for _, pieceId in ipairs(setConfig.pieces) do
            for slot, equipment in pairs(equipped) do
                if equipment.id == pieceId then
                    equippedPieces = equippedPieces + 1
                    break
                end
            end
        end
        
        -- 應用套裝加成
        for pieceCount, bonuses in pairs(setConfig.setBonuses) do
            if equippedPieces >= pieceCount then
                for statName, value in pairs(bonuses) do
                    setBonuses[statName] = (setBonuses[statName] or 0) + value
                end
            end
        end
    end
    
    return setBonuses
end

-- 生成實例ID
function EquipmentManager:GenerateInstanceId()
    return "eq_" .. tick() .. "_" .. math.random(1000, 9999)
end

-- 輔助函數 (需要與其他系統整合)
function EquipmentManager:GetPlayerHeroData(player)
    -- 這裡應該調用英雄系統獲取數據
    return {class = "WARRIOR", level = 1}
end

function EquipmentManager:GetPlayerLevel(player)
    -- 這裡應該調用英雄系統獲取等級
    return 1
end

function EquipmentManager:GetPlayerHeroClass(player)
    -- 這裡應該調用英雄系統獲取職業
    return "WARRIOR"
end

function EquipmentManager:CheckMaterials(player, materialCost)
    -- 這裡應該檢查玩家材料
    return true -- 暫時返回true
end

function EquipmentManager:ConsumeMaterials(player, materialCost)
    -- 這裡應該消耗玩家材料
    print("消耗材料:", materialCost)
end

function EquipmentManager:SavePlayerEquipment(player)
    -- 這裡應該保存到數據庫
    print("保存裝備數據")
end

-- 調試信息
function EquipmentManager:Debug()
    print("⚔️ 裝備管理系統調試信息:")
    print("  初始化狀態:", self.isInitialized)
    
    local playerCount = 0
    for _ in pairs(self.playerEquipment) do
        playerCount = playerCount + 1
    end
    print("  活躍玩家數:", playerCount)
end

return EquipmentManager
