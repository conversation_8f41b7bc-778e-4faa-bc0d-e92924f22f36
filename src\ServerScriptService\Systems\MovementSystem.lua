-- 移動系統 - 處理玩家和寵物的移動邏輯
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local MovementSystem = {}

function MovementSystem:step(world, state)
    -- 處理玩家移動
    self:processPlayerMovement(world, state)
    
    -- 處理寵物跟隨
    self:processPetFollowing(world, state)
    
    -- 處理怪物移動
    self:processMonsterMovement(world, state)
end

function MovementSystem:processPlayerMovement(world, state)
    for entityId, movement, transform, player in world:query(
        Components.MovementComponent,
        Components.TransformComponent,
        Components.PlayerComponent
    ) do
        if movement.isMoving and movement.destination then
            self:moveTowardsDestination(world, entityId, movement, transform, state.deltaTime)
        end
        
        -- 更新動畫狀態
        self:updateMovementAnimation(world, entityId, movement)
    end
end

function MovementSystem:processPetFollowing(world, state)
    for entityId, pet, movement, transform in world:query(
        Components.PetComponent,
        Components.MovementComponent,
        Components.TransformComponent
    ) do
        -- 找到寵物主人
        local ownerId = self:findOwnerEntity(world, pet.ownerId)
        if ownerId then
            local ownerTransform = world:get(ownerId, Components.TransformComponent)
            if ownerTransform then
                self:followOwner(world, entityId, movement, transform, ownerTransform, state.deltaTime)
            end
        end
    end
end

function MovementSystem:processMonsterMovement(world, state)
    for entityId, monster, movement, transform in world:query(
        Components.MonsterComponent,
        Components.MovementComponent,
        Components.TransformComponent
    ) do
        -- 簡單的隨機移動邏輯
        if not movement.isMoving and math.random() < 0.01 then -- 1% 機率開始移動
            self:startRandomMovement(world, entityId, movement, transform)
        elseif movement.isMoving and movement.destination then
            self:moveTowardsDestination(world, entityId, movement, transform, state.deltaTime)
        end
    end
end

function MovementSystem:moveTowardsDestination(world, entityId, movement, transform, deltaTime)
    local currentPos = transform.position
    local destination = movement.destination
    
    -- 計算方向和距離
    local direction = (destination - currentPos)
    local distance = direction.Magnitude
    
    -- 如果已經到達目的地
    if distance < 1 then
        self:stopMovement(world, entityId, movement, destination)
        return
    end
    
    -- 計算移動
    local normalizedDirection = direction.Unit
    local moveDistance = movement.speed * deltaTime
    local newPosition = currentPos + (normalizedDirection * moveDistance)
    
    -- 更新位置
    world:insert(entityId, Components.TransformComponent({
        position = newPosition,
        rotation = transform.rotation,
        scale = transform.scale
    }))
    
    -- 更新速度
    world:insert(entityId, Components.MovementComponent({
        velocity = normalizedDirection * movement.speed,
        speed = movement.speed,
        isMoving = true,
        destination = destination,
        path = movement.path
    }))
end

function MovementSystem:followOwner(world, petEntityId, movement, transform, ownerTransform, deltaTime)
    local petPos = transform.position
    local ownerPos = ownerTransform.position
    
    -- 計算距離
    local distance = (ownerPos - petPos).Magnitude
    
    -- 如果距離太遠，開始跟隨
    local followDistance = 10 -- 10 studs
    local stopDistance = 5   -- 5 studs
    
    if distance > followDistance then
        -- 開始跟隨主人
        world:insert(petEntityId, Components.MovementComponent({
            velocity = movement.velocity,
            speed = movement.speed * 1.2, -- 寵物移動稍快以追上主人
            isMoving = true,
            destination = ownerPos,
            path = movement.path
        }))
    elseif distance < stopDistance and movement.isMoving then
        -- 停止移動
        self:stopMovement(world, petEntityId, movement, petPos)
    end
end

function MovementSystem:startRandomMovement(world, entityId, movement, transform)
    -- 生成隨機目的地 (在當前位置周圍20 studs範圍內)
    local currentPos = transform.position
    local randomOffset = Vector3.new(
        (math.random() - 0.5) * 40, -- -20 到 20
        0,
        (math.random() - 0.5) * 40  -- -20 到 20
    )
    
    local destination = currentPos + randomOffset
    
    world:insert(entityId, Components.MovementComponent({
        velocity = Vector3.new(0, 0, 0),
        speed = movement.speed * 0.5, -- 怪物移動較慢
        isMoving = true,
        destination = destination,
        path = movement.path
    }))
end

function MovementSystem:stopMovement(world, entityId, movement, finalPosition)
    world:insert(entityId, Components.MovementComponent({
        velocity = Vector3.new(0, 0, 0),
        speed = movement.speed,
        isMoving = false,
        destination = nil,
        path = {}
    }))
    
    -- 更新最終位置
    local transform = world:get(entityId, Components.TransformComponent)
    if transform then
        world:insert(entityId, Components.TransformComponent({
            position = finalPosition,
            rotation = transform.rotation,
            scale = transform.scale
        }))
    end
end

function MovementSystem:updateMovementAnimation(world, entityId, movement)
    local animation = world:get(entityId, Components.AnimationComponent)
    if not animation then return end
    
    local newAnimation = movement.isMoving and "walking" or "idle"
    
    if animation.currentAnimation ~= newAnimation then
        world:insert(entityId, Components.AnimationComponent({
            currentAnimation = newAnimation,
            animationSpeed = movement.isMoving and 1.0 or 0.5,
            isLooping = true,
            animations = animation.animations
        }))
    end
end

function MovementSystem:findOwnerEntity(world, ownerId)
    for entityId, player in world:query(Components.PlayerComponent) do
        if player.userId == ownerId then
            return entityId
        end
    end
    return nil
end

-- 公共方法：設置移動目標
function MovementSystem:setMovementTarget(world, entityId, destination)
    local movement = world:get(entityId, Components.MovementComponent)
    if not movement then
        -- 創建移動組件
        world:insert(entityId, Components.MovementComponent({
            velocity = Vector3.new(0, 0, 0),
            speed = 16, -- 默認速度
            isMoving = true,
            destination = destination,
            path = {}
        }))
    else
        -- 更新移動目標
        world:insert(entityId, Components.MovementComponent({
            velocity = movement.velocity,
            speed = movement.speed,
            isMoving = true,
            destination = destination,
            path = movement.path
        }))
    end
    
    return true
end

-- 公共方法：停止移動
function MovementSystem:stopEntityMovement(world, entityId)
    local movement = world:get(entityId, Components.MovementComponent)
    if movement then
        world:insert(entityId, Components.MovementComponent({
            velocity = Vector3.new(0, 0, 0),
            speed = movement.speed,
            isMoving = false,
            destination = nil,
            path = {}
        }))
    end
    
    return true
end

-- 公共方法：設置移動速度
function MovementSystem:setMovementSpeed(world, entityId, speed)
    local movement = world:get(entityId, Components.MovementComponent)
    if movement then
        world:insert(entityId, Components.MovementComponent({
            velocity = movement.velocity,
            speed = speed,
            isMoving = movement.isMoving,
            destination = movement.destination,
            path = movement.path
        }))
        return true
    end
    return false
end

-- 公共方法：瞬移到指定位置
function MovementSystem:teleportEntity(world, entityId, position)
    local transform = world:get(entityId, Components.TransformComponent)
    if transform then
        world:insert(entityId, Components.TransformComponent({
            position = position,
            rotation = transform.rotation,
            scale = transform.scale
        }))
        
        -- 停止當前移動
        self:stopEntityMovement(world, entityId)
        
        return true
    end
    return false
end

-- 公共方法：獲取實體位置
function MovementSystem:getEntityPosition(world, entityId)
    local transform = world:get(entityId, Components.TransformComponent)
    return transform and transform.position or nil
end

-- 公共方法：計算兩個實體之間的距離
function MovementSystem:getDistanceBetweenEntities(world, entityId1, entityId2)
    local pos1 = self:getEntityPosition(world, entityId1)
    local pos2 = self:getEntityPosition(world, entityId2)
    
    if pos1 and pos2 then
        return (pos1 - pos2).Magnitude
    end
    
    return nil
end

-- 公共方法：檢查實體是否在移動
function MovementSystem:isEntityMoving(world, entityId)
    local movement = world:get(entityId, Components.MovementComponent)
    return movement and movement.isMoving or false
end

return MovementSystem
