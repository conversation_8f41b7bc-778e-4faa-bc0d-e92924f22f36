-- 事件管理器測試
return function()
    local EventManager = require(game.ReplicatedStorage.Shared.EventManager)
    
    describe("EventManager", function()
        beforeEach(function()
            -- 重新初始化事件管理器
            EventManager:Initialize()
        end)
        
        afterEach(function()
            -- 清理事件管理器
            EventManager:Cleanup()
        end)
        
        it("應該正確初始化", function()
            -- 驗證初始化
            expect(EventManager).to.be.ok()
            expect(EventManager.EventTypes).to.be.ok()
            expect(EventManager._signals).to.be.ok()
            expect(EventManager._listeners).to.be.ok()
            expect(EventManager._stats).to.be.ok()
            
            -- 驗證事件類型數量
            local eventTypeCount = EventManager:GetEventTypeCount()
            expect(eventTypeCount).to.be.greaterThan(0)
        end)
        
        it("應該正確觸發和監聽事件", function()
            local eventFired = false
            local receivedData = nil
            
            -- 監聽事件
            local connection = EventManager:Connect(EventManager.EventTypes.PLAYER_LEVEL_UP, function(data)
                eventFired = true
                receivedData = data
            end)
            
            -- 觸發事件
            local testData = { level = 5, player = "TestPlayer" }
            local success = EventManager:Fire(EventManager.EventTypes.PLAYER_LEVEL_UP, testData)
            
            -- 等待事件處理
            wait(0.1)
            
            -- 驗證結果
            expect(success).to.equal(true)
            expect(eventFired).to.equal(true)
            expect(receivedData).to.be.ok()
            expect(receivedData.level).to.equal(5)
            expect(receivedData.player).to.equal("TestPlayer")
            expect(receivedData.timestamp).to.be.ok()
            expect(receivedData.eventType).to.equal(EventManager.EventTypes.PLAYER_LEVEL_UP)
            
            -- 清理
            connection:Disconnect()
        end)
        
        it("應該正確處理多個監聽器", function()
            local listener1Called = false
            local listener2Called = false
            local listener3Called = false
            
            -- 添加多個監聽器
            local connection1 = EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
                listener1Called = true
            end)
            
            local connection2 = EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
                listener2Called = true
            end)
            
            local connection3 = EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
                listener3Called = true
            end)
            
            -- 觸發事件
            EventManager:Fire(EventManager.EventTypes.COMBAT_STARTED, { attacker = "Player1", target = "Monster1" })
            
            -- 等待事件處理
            wait(0.1)
            
            -- 驗證所有監聽器都被調用
            expect(listener1Called).to.equal(true)
            expect(listener2Called).to.equal(true)
            expect(listener3Called).to.equal(true)
            
            -- 清理
            connection1:Disconnect()
            connection2:Disconnect()
            connection3:Disconnect()
        end)
        
        it("應該正確處理一次性監聽", function()
            local callCount = 0
            
            -- 添加一次性監聽器
            EventManager:Once(EventManager.EventTypes.PET_OBTAINED, function(data)
                callCount = callCount + 1
            end)
            
            -- 多次觸發事件
            EventManager:Fire(EventManager.EventTypes.PET_OBTAINED, { petId = "pet1" })
            EventManager:Fire(EventManager.EventTypes.PET_OBTAINED, { petId = "pet2" })
            EventManager:Fire(EventManager.EventTypes.PET_OBTAINED, { petId = "pet3" })
            
            -- 等待事件處理
            wait(0.1)
            
            -- 驗證只被調用一次
            expect(callCount).to.equal(1)
        end)
        
        it("應該正確處理事件統計", function()
            -- 獲取初始統計
            local initialStats = EventManager:GetStats()
            expect(initialStats.totalEvents).to.equal(0)
            
            -- 觸發一些事件
            EventManager:Fire(EventManager.EventTypes.PLAYER_JOINED, { player = "Player1" })
            EventManager:Fire(EventManager.EventTypes.PLAYER_LEVEL_UP, { player = "Player1", level = 2 })
            EventManager:Fire(EventManager.EventTypes.PET_OBTAINED, { player = "Player1", petId = "pet1" })
            
            -- 獲取更新後的統計
            local updatedStats = EventManager:GetStats()
            expect(updatedStats.totalEvents).to.equal(3)
            expect(updatedStats.eventCounts[EventManager.EventTypes.PLAYER_JOINED]).to.equal(1)
            expect(updatedStats.eventCounts[EventManager.EventTypes.PLAYER_LEVEL_UP]).to.equal(1)
            expect(updatedStats.eventCounts[EventManager.EventTypes.PET_OBTAINED]).to.equal(1)
        end)
        
        it("應該正確處理未知事件類型", function()
            -- 嘗試觸發未知事件
            local success = EventManager:Fire("UNKNOWN_EVENT", { data = "test" })
            expect(success).to.equal(false)
            
            -- 嘗試監聽未知事件
            local connection = EventManager:Connect("UNKNOWN_EVENT", function() end)
            expect(connection).to.equal(nil)
        end)
        
        it("應該正確處理事件等待", function()
            local testData = { message = "test message" }
            
            -- 在另一個線程中延遲觸發事件
            task.spawn(function()
                wait(0.2)
                EventManager:Fire(EventManager.EventTypes.ZONE_ENTERED, testData)
            end)
            
            -- 等待事件
            local receivedData = EventManager:Wait(EventManager.EventTypes.ZONE_ENTERED, 1)
            
            -- 驗證接收到的數據
            expect(receivedData).to.be.ok()
            expect(receivedData.message).to.equal("test message")
        end)
        
        it("應該正確處理事件等待超時", function()
            -- 等待一個不會觸發的事件，設置短超時
            local receivedData = EventManager:Wait(EventManager.EventTypes.SERVER_SHUTDOWN, 0.1)
            
            -- 應該返回 nil（超時）
            expect(receivedData).to.equal(nil)
        end)
        
        it("應該正確處理批量事件", function()
            local receivedEvents = {}
            
            -- 監聽多種事件
            local connection1 = EventManager:Connect(EventManager.EventTypes.PLAYER_JOINED, function(data)
                table.insert(receivedEvents, data)
            end)
            
            local connection2 = EventManager:Connect(EventManager.EventTypes.PLAYER_LEVEL_UP, function(data)
                table.insert(receivedEvents, data)
            end)
            
            local connection3 = EventManager:Connect(EventManager.EventTypes.PET_OBTAINED, function(data)
                table.insert(receivedEvents, data)
            end)
            
            -- 批量觸發事件
            local events = {
                { type = EventManager.EventTypes.PLAYER_JOINED, data = { player = "Player1" } },
                { type = EventManager.EventTypes.PLAYER_LEVEL_UP, data = { player = "Player1", level = 2 } },
                { type = EventManager.EventTypes.PET_OBTAINED, data = { player = "Player1", petId = "pet1" } }
            }
            
            EventManager:FireBatch(events)
            
            -- 等待事件處理
            wait(0.1)
            
            -- 驗證所有事件都被接收
            expect(#receivedEvents).to.equal(3)
            
            -- 清理
            connection1:Disconnect()
            connection2:Disconnect()
            connection3:Disconnect()
        end)
        
        it("應該正確處理延遲事件", function()
            local eventReceived = false
            local startTime = tick()
            local receiveTime = 0
            
            -- 監聽事件
            local connection = EventManager:Connect(EventManager.EventTypes.QUEST_COMPLETED, function(data)
                eventReceived = true
                receiveTime = tick()
            end)
            
            -- 延遲觸發事件
            local delay = 0.3
            EventManager:FireDelayed(EventManager.EventTypes.QUEST_COMPLETED, { questId = "quest1" }, delay)
            
            -- 等待事件
            wait(delay + 0.1)
            
            -- 驗證事件被延遲觸發
            expect(eventReceived).to.equal(true)
            local actualDelay = receiveTime - startTime
            expect(actualDelay).to.be.greaterThan(delay - 0.1)
            expect(actualDelay).to.be.lessThan(delay + 0.2)
            
            -- 清理
            connection:Disconnect()
        end)
        
        it("應該正確處理條件事件", function()
            local eventReceived = false
            
            -- 監聽事件
            local connection = EventManager:Connect(EventManager.EventTypes.ACHIEVEMENT_UNLOCKED, function(data)
                eventReceived = true
            end)
            
            -- 條件為真時觸發事件
            local success1 = EventManager:FireIf(true, EventManager.EventTypes.ACHIEVEMENT_UNLOCKED, { achievementId = "test1" })
            expect(success1).to.equal(true)
            
            -- 條件為假時不觸發事件
            local success2 = EventManager:FireIf(false, EventManager.EventTypes.ACHIEVEMENT_UNLOCKED, { achievementId = "test2" })
            expect(success2).to.equal(false)
            
            -- 等待事件處理
            wait(0.1)
            
            -- 驗證只有條件為真的事件被觸發
            expect(eventReceived).to.equal(true)
            
            -- 清理
            connection:Disconnect()
        end)
        
        it("應該正確處理便捷方法", function()
            local playerEventReceived = false
            local combatEventReceived = false
            local petEventReceived = false
            
            -- 監聽事件
            local connection1 = EventManager:Connect(EventManager.EventTypes.PLAYER_JOINED, function(data)
                playerEventReceived = true
                expect(data.player).to.be.ok()
                expect(data.userId).to.be.ok()
                expect(data.playerName).to.be.ok()
            end)
            
            local connection2 = EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
                combatEventReceived = true
                expect(data.attacker).to.be.ok()
                expect(data.target).to.be.ok()
            end)
            
            local connection3 = EventManager:Connect(EventManager.EventTypes.PET_OBTAINED, function(data)
                petEventReceived = true
                expect(data.player).to.be.ok()
                expect(data.petData).to.be.ok()
            end)
            
            -- 使用便捷方法觸發事件
            local mockPlayer = { UserId = 123, Name = "TestPlayer" }
            EventManager:FirePlayerEvent(EventManager.EventTypes.PLAYER_JOINED, mockPlayer, {})
            EventManager:FireCombatEvent(EventManager.EventTypes.COMBAT_STARTED, "attacker", "target", {})
            EventManager:FirePetEvent(EventManager.EventTypes.PET_OBTAINED, mockPlayer, { petId = "pet1" }, {})
            
            -- 等待事件處理
            wait(0.1)
            
            -- 驗證事件被正確觸發
            expect(playerEventReceived).to.equal(true)
            expect(combatEventReceived).to.equal(true)
            expect(petEventReceived).to.equal(true)
            
            -- 清理
            connection1:Disconnect()
            connection2:Disconnect()
            connection3:Disconnect()
        end)
    end)
end
