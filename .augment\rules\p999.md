---
type: "always_apply"
---

## 🔧 Rojo 使用說明

### 啟動 Rojo 服務器
```bash
rojo serve
```
- 服務器會在 `http://localhost:34872/` 啟動
- 保持終端開啟，不要關閉

### 在 Roblox Studio 中連接
1. 安裝 Rojo 插件（從 Roblox 插件商店）
2. 點擊 Rojo 插件中的 "Connect" 按鈕
3. 輸入服務器地址：`localhost:34872`
4. 點擊 "Connect" 連接

### 同步注意事項
- ⚠️ **首次同步會覆蓋 Studio 中的現有內容**
- 建議在空白 Place 中進行首次同步
- 同步後，代碼更改會自動反映到 Studio 中

## 🛠️ 開發工具介紹

### 1. Matter - ECS 框架
**用途：** 實體組件系統 (Entity Component System)，提供高性能的遊戲邏輯組織

**引用方式：**
```lua
local Matter = require(game.ReplicatedStorage.Packages.Matter)
```

**基本用法：**
```lua
-- 定義組件
local HealthComponent = Matter.component("Health", {
    current = 100,
    maximum = 100
})

local PositionComponent = Matter.component("Position", {
    x = 0,
    y = 0,
    z = 0
})

-- 創建世界
local world = Matter.World.new()

-- 創建實體
local entity = world:spawn(
    HealthComponent({ current = 150, maximum = 150 }),
    PositionComponent({ x = 10, y = 0, z = 5 })
)

-- 創建系統
local function healthSystem(world, state)
    for entityId, health in world:query(HealthComponent) do
        if health.current <= 0 then
            world:despawn(entityId)
        end
    end
end

-- 運行系統
world:addSystem(healthSystem)
```

**注意事項：**
- 組件只應包含數據，不包含邏輯
- 系統包含邏輯，操作組件數據
- 使用 `world:query()` 高效查詢實體
- 避免在系統中直接修改其他系統的組件

### 2. Knit - 網路通信框架
**用途：** 伺服器-客戶端通信框架，自動化網路同步

**引用方式：**
```lua
local Knit = require(game.ReplicatedStorage.Packages.Knit)
```

**服務端服務：**
```lua
-- 創建服務
local MyService = Knit.CreateService({
    Name = "MyService",
    Client = {
        -- 客戶端可調用的方法
        GetData = Knit.CreateSignal(),

        -- 客戶端事件
        OnDataChanged = Knit.CreateSignal()
    }
})

function MyService:KnitStart()
    print("服務啟動")
end

-- 客戶端可調用的方法
function MyService.Client:GetData(player)
    return self.Server:GetPlayerData(player)
end

-- 服務端方法
function MyService:GetPlayerData(player)
    return { level = 10, coins = 1000 }
end

-- 啟動 Knit
Knit.Start():andThen(function()
    print("Knit 服務端啟動完成")
end)
```

**客戶端控制器：**
```lua
-- 創建控制器
local MyController = Knit.CreateController({
    Name = "MyController"
})

function MyController:KnitStart()
    -- 獲取服務
    local MyService = Knit.GetService("MyService")

    -- 調用服務方法
    MyService:GetData():andThen(function(data)
        print("收到數據：", data)
    end)

    -- 監聽服務事件
    MyService.OnDataChanged:Connect(function(newData)
        print("數據更新：", newData)
    end)
end

-- 啟動 Knit
Knit.Start():andThen(function()
    print("Knit 客戶端啟動完成")
end)
```

### 3. Fusion - UI 框架
**用途：** 現代化 UI 框架，響應式組件和狀態管理

**引用方式：**
```lua
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local New = Fusion.New
local State = Fusion.State
local Computed = Fusion.Computed
```

**基本用法：**
```lua
-- 創建響應式狀態
local playerLevel = State(1)
local playerHealth = State(100)
local playerMaxHealth = State(100)

-- 計算屬性
local healthPercentage = Computed(function()
    return playerHealth:get() / playerMaxHealth:get()
end)

-- 創建 UI 組件
local healthBar = New "Frame" {
    Name = "HealthBar",
    Size = UDim2.new(0.3, 0, 0.05, 0),
    Position = UDim2.new(0.1, 0, 0.1, 0),
    BackgroundColor3 = Color3.fromRGB(200, 50, 50),

    -- 響應式大小
    Size = Computed(function()
        local percentage = healthPercentage:get()
        return UDim2.new(0.3 * percentage, 0, 0.05, 0)
    end),

    [Fusion.Children] = {
        New "TextLabel" {
            Size = UDim2.new(1, 0, 1, 0),
            BackgroundTransparency = 1,
            TextColor3 = Color3.fromRGB(255, 255, 255),
            TextScaled = true,

            -- 響應式文字
            Text = Computed(function()
                return playerHealth:get() .. "/" .. playerMaxHealth:get()
            end)
        }
    }
}

-- 更新狀態
playerHealth:set(80) -- UI 會自動更新
```

### 4. ZonePlus - 區域管理
**用途：** 動態區域檢測和管理，處理玩家進入/離開區域事件

**引用方式：**
```lua
local ZonePlus = require(game.ReplicatedStorage.Packages.ZonePlus)
```

**基本用法：**
```lua
-- 創建區域 (使用 Part 或 Region3)
local zone = ZonePlus.new(workspace.SafeZone)

-- 監聽玩家進入區域
zone.playerEntered:Connect(function(player)
    print(player.Name .. " 進入安全區域")
    -- 停止 PvP、恢復血量等
end)

-- 監聽玩家離開區域
zone.playerExited:Connect(function(player)
    print(player.Name .. " 離開安全區域")
    -- 啟用 PvP、開始怪物生成等
end)

-- 檢查玩家是否在區域內
if zone:findPlayer(player) then
    print("玩家在區域內")
end

-- 獲取區域內所有玩家
local playersInZone = zone:getPlayers()
```

### 5. Signal - 事件處理
**用途：** 高性能的事件處理系統，用於 PvP 和寵物召喚等事件

**引用方式：**
```lua
local Signal = require(game.ReplicatedStorage.Packages.Signal)
```

**基本用法：**
```lua
-- 創建信號
local onPetSummoned = Signal.new()
local onPvPStarted = Signal.new()

-- 連接事件處理器
local connection1 = onPetSummoned:Connect(function(player, petData)
    print(player.Name .. " 召喚了寵物：" .. petData.name)
end)

local connection2 = onPvPStarted:Connect(function(player1, player2)
    print("PvP 開始：" .. player1.Name .. " vs " .. player2.Name)
end)

-- 觸發事件
onPetSummoned:Fire(player, { name = "火龍", level = 10 })
onPvPStarted:Fire(player1, player2)

-- 清理連接
connection1:Disconnect()
connection2:Disconnect()
```

### 6. TestEZ - 測試框架
**用途：** 單元測試框架，配合 Matter 除錯視圖進行測試

**引用方式：**
```lua
local TestEZ = require(game.ReplicatedStorage.Packages.TestEZ)
```

**基本用法：**
```lua
-- 測試套件
return function()
    describe("寵物系統", function()
        it("應該正確創建寵物實體", function()
            local world = Matter.World.new()
            local pet = world:spawn(
                PetComponent({ name = "測試寵物", level = 1 }),
                StatsComponent({ health = 100, attack = 20 })
            )

            expect(world:get(pet, PetComponent).name).to.equal("測試寵物")
            expect(world:get(pet, StatsComponent).health).to.equal(100)
        end)

        it("應該正確處理寵物升級", function()
            local world = Matter.World.new()
            local petSystem = PetSystem.new()

            local pet = world:spawn(
                PetComponent({ level = 1, experience = 100 }),
                StatsComponent({ health = 100 })
            )

            petSystem:step(world, {})

            local petData = world:get(pet, PetComponent)
            expect(petData.level).to.equal(2)
        end)
    end)
end

-- 運行測試
TestEZ.TestBootstrap:run({
    game.ReplicatedStorage.Tests
})
```

### 7. ProfileService - 數據持久化
**用途：** 安全的玩家數據儲存，防止數據丟失

**引用方式：**
```lua
local ProfileService = require(game.ReplicatedStorage.Packages.ProfileService)
```

**與 ECS 整合：**
```lua
-- 創建 ProfileStore
local ProfileStore = ProfileService.GetProfileStore("PlayerData", {
    entities = {}, -- 存儲玩家的 ECS 實體數據
    hero = { level = 1, experience = 0 },
    pets = {},
    currencies = { coins = 1000, gems = 20 }
})

-- 載入玩家數據並創建 ECS 實體
local function LoadPlayerData(player, world)
    local profile = ProfileStore:LoadProfileAsync("Player_" .. player.UserId)

    if profile then
        profile:Reconcile()

        -- 從數據創建英雄實體
        local heroEntity = world:spawn(
            HeroComponent(profile.Data.hero),
            PlayerComponent({ userId = player.UserId })
        )

        -- 從數據創建寵物實體
        for _, petData in ipairs(profile.Data.pets) do
            world:spawn(
                PetComponent(petData),
                OwnerComponent({ playerId = player.UserId })
            )
        end

        return profile
    end
end
```

### 8. TableUtil - 表格工具
**用途：** 高效處理表格操作，在 ECS 系統中處理組件數據

**引用方式：**
```lua
local TableUtil = require(game.ReplicatedStorage.Packages.TableUtil)
```

**在 ECS 中的用法：**
```lua
-- 深拷貝組件數據
local function CloneComponent(componentData)
    return TableUtil.Copy(componentData, true)
end

-- 合併組件屬性
local function MergeStats(baseStats, bonusStats)
    return TableUtil.Assign({}, baseStats, bonusStats)
end

-- 過濾寵物列表
local function FilterPetsByRarity(pets, rarity)
    return TableUtil.Filter(pets, function(pet)
        return pet.rarity == rarity
    end)
end
```

## ⚠️ 注意事項

### Rojo 相關
1. **不要在 Studio 中直接編輯同步的腳本**
   - 所有更改都應該在外部編輯器中進行
   - Studio 中的更改會被 Rojo 覆蓋

2. **文件結構**
   - 保持 `src/` 目錄結構整潔
   - 不要移動 `Packages/` 目錄
   - 遵循 ECS 和 Knit 的目錄結構規範

3. **同步問題**
   - 如果同步出現問題，重啟 Rojo 服務器
   - 檢查文件路徑是否正確
   - 確保沒有文件被其他程序鎖定

### ECS (Matter) 相關
1. **組件設計**
   - 組件只包含數據，不包含邏輯
   - 避免在組件中存儲函數或複雜對象
   - 使用簡單的數據類型 (number, string, boolean, table)

2. **系統設計**
   - 系統應該是無狀態的，所有狀態存儲在組件中
   - 避免系統間的直接依賴，使用事件通信
   - 系統的 `step` 函數應該盡可能高效

3. **實體管理**
   - 及時清理不需要的實體，避免內存洩漏
   - 使用 `world:despawn()` 正確銷毀實體
   - 避免在查詢過程中修改實體結構

### Knit 相關
1. **服務設計**
   - 服務應該專注於單一職責
   - 避免服務間的循環依賴
   - 使用 `KnitStart()` 進行初始化，避免在 `KnitInit()` 中調用其他服務

2. **網路通信**
   - 客戶端方法名稱要清晰明確
   - 避免頻繁的網路調用，考慮批量處理
   - 總是驗證客戶端傳入的數據

3. **錯誤處理**
   - 使用 Promise 處理異步操作
   - 在服務方法中添加適當的錯誤處理
   - 記錄重要的錯誤信息

### Fusion 相關
1. **狀態管理**
   - 避免創建過多的 State 對象
   - 使用 Computed 進行衍生狀態計算
   - 及時清理不需要的狀態和連接

2. **UI 性能**
   - 避免在 Computed 中進行耗時操作
   - 使用 `Fusion.cleanup()` 正確清理 UI 組件
   - 避免創建過深的組件嵌套

3. **響應式設計**
   - 保持數據流的單向性
   - 避免在 UI 組件中直接修改狀態
   - 使用事件系統進行 UI 與邏輯的通信

### ZonePlus 相關
1. **區域設計**
   - 避免創建過多重疊的區域
   - 合理設置區域大小，避免性能問題
   - 及時清理不需要的區域監聽器

2. **事件處理**
   - 在區域事件中避免耗時操作
   - 使用防抖機制避免頻繁觸發
   - 正確處理玩家離開遊戲的情況

### 測試相關
1. **TestEZ**
   - 為核心系統編寫單元測試
   - 使用模擬數據進行測試，避免依賴真實環境
   - 定期運行測試，確保代碼質量

2. **Matter 除錯**
   - 使用 Matter 的除錯視圖監控系統性能
   - 定期檢查實體和組件的數量
   - 監控系統執行時間，優化性能瓶頸

## 🎯 最佳實踐

### 代碼組織 (ECS + Knit 架構)
```
src/
├── ReplicatedStorage/
│   ├── Components/       # ECS 組件定義
│   ├── Shared/          # 共享模組和工具
│   ├── Configuration/   # 遊戲配置
│   └── Packages/        # 第三方依賴
├── ServerScriptService/
│   ├── Services/        # Knit 服務
│   ├── Systems/         # ECS 系統
│   └── init.server.lua  # 服務端啟動腳本
├── StarterGui/
│   ├── Controllers/     # Knit 客戶端控制器
│   ├── UI/             # Fusion UI 組件
│   └── init.client.lua  # 客戶端啟動腳本
└── StarterPlayer/
    └── PlayerScripts/   # 玩家腳本
```

### ECS 最佳實踐
```lua
-- 好的組件設計
local HealthComponent = Matter.component("Health", {
    current = 100,
    maximum = 100,
    regeneration = 1
})

-- 避免的組件設計
local BadComponent = Matter.component("Bad", {
    player = nil, -- 不要存儲複雜對象
    updateHealth = function() end, -- 不要存儲函數
    data = { deeply = { nested = { object = true } } } -- 避免過深嵌套
})

-- 好的系統設計
local function HealthSystem(world, state)
    for entityId, health in world:query(HealthComponent) do
        if health.current < health.maximum then
            world:insert(entityId, HealthComponent({
                current = math.min(health.current + health.regeneration, health.maximum),
                maximum = health.maximum,
                regeneration = health.regeneration
            }))
        end
    end
end
```

### Knit 最佳實踐
```lua
-- 服務端服務設計
local PetService = Knit.CreateService({
    Name = "PetService",
    Client = {
        -- 清晰的客戶端接口
        RollEgg = Knit.CreateSignal(),
        GetPetData = Knit.CreateSignal(),

        -- 客戶端事件
        OnPetObtained = Knit.CreateSignal(),
        OnPetLevelUp = Knit.CreateSignal()
    }
})

function PetService:KnitInit()
    -- 初始化，不調用其他服務
    self._world = Matter.World.new()
end

function PetService:KnitStart()
    -- 啟動後可以調用其他服務
    local PlayerService = Knit.GetService("PlayerService")
    self:_setupSystems()
end

-- 客戶端控制器設計
local UIController = Knit.CreateController({
    Name = "UIController"
})

function UIController:KnitStart()
    local PetService = Knit.GetService("PetService")

    -- 監聽服務事件
    PetService.OnPetObtained:Connect(function(petData)
        self:ShowPetObtainedUI(petData)
    end)
end
```

### Fusion UI 最佳實踐
```lua
-- 響應式狀態管理
local StateManager = {}

-- 全局狀態
StateManager.playerLevel = Fusion.State(1)
StateManager.playerHealth = Fusion.State(100)

-- 計算屬性
StateManager.healthPercentage = Fusion.Computed(function()
    return StateManager.playerHealth:get() / 100
end)

-- UI 組件設計
local function CreateHealthBar()
    return New "Frame" {
        Name = "HealthBar",
        Size = UDim2.new(0.3, 0, 0.05, 0),

        -- 響應式屬性
        BackgroundColor3 = Fusion.Computed(function()
            local percentage = StateManager.healthPercentage:get()
            if percentage > 0.5 then
                return Color3.fromRGB(0, 255, 0) -- 綠色
            elseif percentage > 0.2 then
                return Color3.fromRGB(255, 255, 0) -- 黃色
            else
                return Color3.fromRGB(255, 0, 0) -- 紅色
            end
        end),

        Size = Fusion.Computed(function()
            local percentage = StateManager.healthPercentage:get()
            return UDim2.new(0.3 * percentage, 0, 0.05, 0)
        end)
    }
end
```

### 錯誤處理和除錯
```lua
-- Knit 服務中的錯誤處理
function PetService.Client:RollEgg(player, eggType)
    return self.Server:RollEgg(player, eggType):catch(function(error)
        warn("扭蛋失敗：", error)
        self.Client.OnError:Fire(player, "扭蛋系統暫時不可用")
    end)
end

-- ECS 系統中的錯誤處理
local function SafeSystem(world, state)
    local success, error = pcall(function()
        -- 系統邏輯
        for entityId, component in world:query(SomeComponent) do
            -- 處理邏輯
        end
    end)

    if not success then
        warn("系統錯誤：", error)
        -- 記錄錯誤或恢復狀態
    end
end

-- Matter 除錯視圖使用
local function SetupDebugView(world)
    if game:GetService("RunService"):IsStudio() then
        local MatterDebugger = require(game.ReplicatedStorage.Packages.MatterDebugger)
        MatterDebugger.new(world)
    end
end
```

## ❓ 常見問題

### Q: Rojo 連接失敗怎麼辦？
A:
1. 檢查服務器是否正在運行
2. 確認端口號正確（通常是 34872）
3. 重啟 Rojo 服務器和 Studio

### Q: 為什麼我的更改沒有同步到 Studio？
A:
1. 檢查文件是否保存
2. 確認文件在正確的目錄中
3. 查看 Rojo 終端是否有錯誤信息

### Q: Matter ECS 系統運行緩慢怎麼辦？
A:
1. 檢查系統中是否有耗時操作
2. 使用 Matter 除錯視圖分析性能
3. 優化查詢條件，避免不必要的實體遍歷
4. 考慮將複雜邏輯分解為多個小系統

### Q: Knit 服務無法啟動怎麼辦？
A:
1. 檢查服務名稱是否重複
2. 確認 `KnitInit()` 中沒有調用其他服務
3. 查看 Studio 輸出的錯誤信息
4. 確保所有依賴的 Packages 都已正確安裝

### Q: Fusion UI 不更新怎麼辦？
A:
1. 檢查 State 是否正確設置
2. 確認 Computed 函數中使用了 `:get()` 方法
3. 避免在 Computed 中進行副作用操作
4. 檢查是否正確清理了 UI 組件

### Q: ZonePlus 區域檢測不準確怎麼辦？
A:
1. 檢查區域 Part 的大小和位置
2. 確認區域沒有重疊導致衝突
3. 調整區域檢測的精度設置
4. 使用 Region3 而不是 Part 進行更精確的檢測

### Q: ProfileService 數據丟失怎麼辦？
A:
1. 檢查是否正確調用了 `profile:Release()`
2. 確認網絡連接穩定
3. 查看 Studio 輸出是否有錯誤信息
4. 檢查 ProfileStore 名稱是否正確

### Q: 如何調試 ECS 系統？
A:
```lua
-- 使用 Matter 除錯視圖
local MatterDebugger = require(game.ReplicatedStorage.Packages.MatterDebugger)
local debugger = MatterDebugger.new(world)

-- 在系統中添加日誌
local function MySystem(world, state)
    local entityCount = 0
    for entityId, component in world:query(MyComponent) do
        entityCount = entityCount + 1
        -- 處理邏輯
    end
    print("MySystem 處理了", entityCount, "個實體")
end
```

### Q: 如何調試 Knit 網路通信？
A:
```lua
-- 在服務中添加日誌
function MyService.Client:MyMethod(player, data)
    print("客戶端調用：", player.Name, data)
    return self.Server:HandleRequest(player, data):andThen(function(result)
        print("服務端回應：", result)
        return result
    end):catch(function(error)
        warn("網路錯誤：", error)
        warn(debug.traceback())
    end)
end
```

### Q: 如何優化 Fusion UI 性能？
A:
1. 避免創建過多的 State 對象
2. 使用 `Fusion.cleanup()` 正確清理組件
3. 避免在 Computed 中進行複雜計算
4. 使用 `Fusion.Observer` 進行性能監控