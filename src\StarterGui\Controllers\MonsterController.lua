-- 怪物控制器
-- 客戶端怪物系統控制器，處理扭蛋、寵物管理、圖鑑等

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local Knit = require(ReplicatedStorage.Packages.Knit)
local Fusion = require(ReplicatedStorage.Packages.Fusion)

-- Fusion 0.2 組件
local State = Fusion.State
local Computed = Fusion.Computed

local MonsterController = Knit.CreateController({
    Name = "MonsterController"
})

-- 本地狀態
local monsterService = nil
local playerPets = State({
    active = {},
    stored = {},
    following = nil
})

local pokedexStats = State({
    discovered = 0,
    owned = 0,
    totalEncounters = 0,
    totalDefeats = 0,
    completionRate = 0
})

local gachaInfo = State({
    COIN = nil,
    GEM = nil,
    ROBUX = nil
})

local recentEncounters = State({})
local notifications = State({})

function MonsterController:KnitStart()
    -- 獲取服務
    monsterService = Knit.GetService("MonsterService")
    
    -- 連接服務事件
    self:_connectServiceEvents()
    
    -- 初始化數據
    self:_initializeData()
    
    print("✅ MonsterController 啟動完成")
end

-- 連接服務事件
function MonsterController:_connectServiceEvents()
    -- 寵物獲得事件
    monsterService.OnPetObtained:Connect(function(petData)
        self:_handlePetObtained(petData)
    end)
    
    -- 首次遭遇事件
    monsterService.OnFirstEncounter:Connect(function(encounterData)
        self:_handleFirstEncounter(encounterData)
    end)
    
    -- 怪物被擊敗事件
    monsterService.OnMonsterDefeated:Connect(function(defeatData)
        self:_handleMonsterDefeated(defeatData)
    end)
    
    -- 寵物裝備事件
    monsterService.OnPetEquipped:Connect(function(equipData)
        self:_handlePetEquipped(equipData)
    end)
end

-- 初始化數據
function MonsterController:_initializeData()
    -- 獲取扭蛋信息
    monsterService:GetGachaInfo():andThen(function(info)
        local gachaData = {}
        for _, gachaType in ipairs(info) do
            gachaData[gachaType.type] = gachaType
        end
        gachaInfo:set(gachaData)
    end):catch(function(err)
        warn("獲取扭蛋信息失敗:", err)
    end)
    
    -- 獲取玩家寵物
    self:refreshPlayerPets()
    
    -- 獲取圖鑑統計
    self:refreshPokedexStats()
end

-- 處理寵物獲得
function MonsterController:_handlePetObtained(petData)
    print(string.format("🎁 獲得新寵物: %s (%s)", petData.speciesId, petData.rarity))
    
    -- 添加通知
    local currentNotifications = notifications:get()
    table.insert(currentNotifications, {
        type = "pet_obtained",
        message = string.format("獲得新寵物: %s", petData.speciesId),
        rarity = petData.rarity,
        timestamp = tick()
    })
    notifications:set(currentNotifications)
    
    -- 刷新數據
    self:refreshPlayerPets()
    self:refreshPokedexStats()
end

-- 處理首次遭遇
function MonsterController:_handleFirstEncounter(encounterData)
    print(string.format("📖 首次發現: %s", encounterData.speciesId))
    
    -- 添加到最近遭遇
    local current = recentEncounters:get()
    table.insert(current, 1, {
        speciesId = encounterData.speciesId,
        rarity = encounterData.rarity,
        zone = encounterData.zone,
        isFirst = true,
        timestamp = tick()
    })
    
    -- 只保留最近10次遭遇
    if #current > 10 then
        table.remove(current, 11)
    end
    
    recentEncounters:set(current)
    
    -- 添加通知
    local currentNotifications = notifications:get()
    table.insert(currentNotifications, {
        type = "first_encounter",
        message = string.format("首次發現: %s", encounterData.speciesId),
        rarity = encounterData.rarity,
        timestamp = tick()
    })
    notifications:set(currentNotifications)
    
    -- 刷新圖鑑統計
    self:refreshPokedexStats()
end

-- 處理怪物被擊敗
function MonsterController:_handleMonsterDefeated(defeatData)
    print(string.format("⚔️ 擊敗怪物: %s (第%d次)", defeatData.speciesId, defeatData.defeatCount))
    
    -- 刷新圖鑑統計
    self:refreshPokedexStats()
end

-- 處理寵物裝備
function MonsterController:_handlePetEquipped(equipData)
    print(string.format("⚔️ 寵物已裝備到槽位 %d", equipData.slot))
    
    -- 刷新寵物數據
    self:refreshPlayerPets()
end

-- 刷新玩家寵物數據
function MonsterController:refreshPlayerPets()
    monsterService:GetPlayerPets():andThen(function(pets)
        playerPets:set(pets)
    end):catch(function(err)
        warn("獲取玩家寵物失敗:", err)
    end)
end

-- 刷新圖鑑統計
function MonsterController:refreshPokedexStats()
    monsterService:GetPokedexStats():andThen(function(stats)
        pokedexStats:set(stats)
    end):catch(function(err)
        warn("獲取圖鑑統計失敗:", err)
    end)
end

-- 扭蛋方法
function MonsterController:rollGacha(gachaType)
    return monsterService:RollGacha(gachaType):andThen(function(result)
        print(string.format("🎁 扭蛋成功: %s (%s)", result.speciesId, result.rarity))
        return result
    end):catch(function(err)
        warn("扭蛋失敗:", err)
        error(err)
    end)
end

-- 批量扭蛋方法
function MonsterController:rollMultipleGacha(gachaType, count)
    return monsterService:RollMultipleGacha(gachaType, count):andThen(function(data)
        print(string.format("🎁 批量扭蛋完成: %d 次成功", #data.results))
        return data
    end):catch(function(err)
        warn("批量扭蛋失敗:", err)
        error(err)
    end)
end

-- 裝備寵物方法
function MonsterController:equipPet(petId, slot)
    return monsterService:EquipPet(petId, slot):andThen(function(success)
        if success then
            print(string.format("⚔️ 寵物 %d 已裝備到槽位 %d", petId, slot))
        end
        return success
    end):catch(function(err)
        warn("裝備寵物失敗:", err)
        error(err)
    end)
end

-- 卸下寵物方法
function MonsterController:unequipPet(slot)
    return monsterService:UnequipPet(slot):andThen(function(success)
        if success then
            print(string.format("📦 槽位 %d 的寵物已卸下", slot))
        end
        return success
    end):catch(function(err)
        warn("卸下寵物失敗:", err)
        error(err)
    end)
end

-- 設置跟隨寵物方法
function MonsterController:setFollowingPet(petId)
    return monsterService:SetFollowingPet(petId):andThen(function(success)
        if success then
            print(string.format("🐕 寵物 %d 開始跟隨", petId))
        end
        return success
    end):catch(function(err)
        warn("設置跟隨寵物失敗:", err)
        error(err)
    end)
end

-- 停止跟隨方法
function MonsterController:stopFollowing()
    return monsterService:StopFollowing():andThen(function(success)
        if success then
            print("🚫 停止寵物跟隨")
        end
        return success
    end):catch(function(err)
        warn("停止跟隨失敗:", err)
        error(err)
    end)
end

-- 獲取遭遇歷史
function MonsterController:getEncounterHistory(speciesId)
    return monsterService:GetEncounterHistory(speciesId):andThen(function(history)
        return history
    end):catch(function(err)
        warn("獲取遭遇歷史失敗:", err)
        return nil
    end)
end

-- 清除通知
function MonsterController:clearNotifications()
    notifications:set({})
end

-- 清除最近遭遇
function MonsterController:clearRecentEncounters()
    recentEncounters:set({})
end

-- 獲取狀態（供UI使用）
function MonsterController:getPlayerPetsState()
    return playerPets
end

function MonsterController:getPokedexStatsState()
    return pokedexStats
end

function MonsterController:getGachaInfoState()
    return gachaInfo
end

function MonsterController:getRecentEncountersState()
    return recentEncounters
end

function MonsterController:getNotificationsState()
    return notifications
end

-- 計算屬性
function MonsterController:getActivePetCount()
    return Computed(function()
        local pets = playerPets:get()
        local count = 0
        for _, petId in pairs(pets.active) do
            if petId then
                count = count + 1
            end
        end
        return count
    end)
end

function MonsterController:getTotalPetCount()
    return Computed(function()
        local pets = playerPets:get()
        local activeCount = 0
        for _, petId in pairs(pets.active) do
            if petId then
                activeCount = activeCount + 1
            end
        end
        return activeCount + #pets.stored
    end)
end

function MonsterController:getCompletionRate()
    return Computed(function()
        local stats = pokedexStats:get()
        return math.floor(stats.completionRate * 100)
    end)
end

return MonsterController
