-- 裝備服務 - 服務端網路處理
local EquipmentService = {}

-- 依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local EquipmentManager = require(game.ReplicatedStorage.Shared.EquipmentManager)
local EquipmentConfig = require(game.ReplicatedStorage.Configuration.EquipmentConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 創建 Knit 服務
EquipmentService = Knit.CreateService({
    Name = "EquipmentService",
    
    -- 客戶端可調用的方法
    Client = {
        -- 獲取裝備數據
        GetPlayerEquipment = Knit.CreateSignal(),
        GetPlayerInventory = Knit.CreateSignal(),
        GetEquipmentStats = Knit.CreateSignal(),
        
        -- 裝備操作
        EquipItem = Knit.CreateSignal(),
        UnequipItem = Knit.CreateSignal(),
        EnhanceEquipment = Knit.CreateSignal(),
        SellEquipment = Knit.CreateSignal(),
        RepairEquipment = Knit.CreateSignal(),
        CompareEquipment = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnEquipmentEquipped = Knit.CreateSignal(),
        OnEquipmentUnequipped = Knit.CreateSignal(),
        OnEquipmentEnhanced = Knit.CreateSignal(),
        OnEquipmentObtained = Knit.CreateSignal(),
        OnInventoryUpdated = Knit.CreateSignal()
    }
})

-- 服務初始化
function EquipmentService:KnitInit()
    print("⚔️ 初始化裝備服務...")
    
    -- 初始化裝備管理器
    EquipmentManager:Initialize()
    
    -- 設置事件監聽
    self:SetupEventListeners()
end

-- 服務啟動
function EquipmentService:KnitStart()
    print("✅ 裝備服務啟動完成")
end

-- 設置事件監聽
function EquipmentService:SetupEventListeners()
    -- 監聽裝備事件並轉發給客戶端
    EventManager:Connect(EventManager.EventTypes.EQUIPMENT_EQUIPPED, function(data)
        self.Client.OnEquipmentEquipped:Fire(data.player, {
            equipment = self:SanitizeEquipmentData(data.equipment),
            slot = data.slot
        })
        
        -- 通知背包更新
        self.Client.OnInventoryUpdated:Fire(data.player)
    end)
    
    EventManager:Connect(EventManager.EventTypes.EQUIPMENT_UNEQUIPPED, function(data)
        self.Client.OnEquipmentUnequipped:Fire(data.player, {
            equipment = self:SanitizeEquipmentData(data.equipment),
            slot = data.slot
        })
        
        -- 通知背包更新
        self.Client.OnInventoryUpdated:Fire(data.player)
    end)
    
    EventManager:Connect(EventManager.EventTypes.EQUIPMENT_ENHANCED, function(data)
        self.Client.OnEquipmentEnhanced:Fire(data.player, {
            equipment = self:SanitizeEquipmentData(data.equipment),
            newLevel = data.newLevel,
            success = data.success,
            penalty = data.penalty
        })
    end)
    
    EventManager:Connect(EventManager.EventTypes.EQUIPMENT_OBTAINED, function(data)
        self.Client.OnEquipmentObtained:Fire(data.player, {
            equipment = self:SanitizeEquipmentData(data.equipment)
        })
        
        -- 通知背包更新
        self.Client.OnInventoryUpdated:Fire(data.player)
    end)
end

-- 客戶端方法：獲取玩家裝備
function EquipmentService.Client:GetPlayerEquipment(player)
    local equipment = EquipmentManager:GetPlayerEquipment(player)
    local sanitizedEquipment = {}
    
    for slot, equipmentData in pairs(equipment) do
        sanitizedEquipment[slot] = self.Server:SanitizeEquipmentData(equipmentData)
    end
    
    return sanitizedEquipment
end

-- 客戶端方法：獲取玩家背包
function EquipmentService.Client:GetPlayerInventory(player)
    local inventory = EquipmentManager:GetPlayerInventory(player)
    local sanitizedInventory = {}
    
    for instanceId, equipmentData in pairs(inventory) do
        sanitizedInventory[instanceId] = self.Server:SanitizeEquipmentData(equipmentData)
    end
    
    return sanitizedInventory
end

-- 客戶端方法：獲取裝備屬性加成
function EquipmentService.Client:GetEquipmentStats(player)
    local equipmentStats = EquipmentManager:GetPlayerEquipmentStats(player)
    local setBonuses = EquipmentManager:CalculateSetBonuses(player)
    
    return {
        equipmentStats = equipmentStats,
        setBonuses = setBonuses,
        totalStats = equipmentStats -- 已包含套裝加成
    }
end

-- 客戶端方法：裝備物品
function EquipmentService.Client:EquipItem(player, instanceId)
    if not instanceId or instanceId == "" then
        return {
            success = false,
            error = "無效的裝備ID"
        }
    end
    
    local success, error = EquipmentManager:EquipItem(player, instanceId)
    
    return {
        success = success,
        error = error
    }
end

-- 客戶端方法：卸下裝備
function EquipmentService.Client:UnequipItem(player, slot)
    if not slot or slot == "" then
        return {
            success = false,
            error = "無效的裝備部位"
        }
    end
    
    local success, error = EquipmentManager:UnequipItem(player, slot)
    
    return {
        success = success,
        error = error
    }
end

-- 客戶端方法：強化裝備
function EquipmentService.Client:EnhanceEquipment(player, instanceId)
    if not instanceId or instanceId == "" then
        return {
            success = false,
            error = "無效的裝備ID"
        }
    end
    
    local success, message = EquipmentManager:EnhanceEquipment(player, instanceId)
    
    return {
        success = success,
        message = message
    }
end

-- 客戶端方法：出售裝備
function EquipmentService.Client:SellEquipment(player, instanceId)
    if not instanceId or instanceId == "" then
        return {
            success = false,
            error = "無效的裝備ID"
        }
    end
    
    -- 獲取裝備數據
    local inventory = EquipmentManager:GetPlayerInventory(player)
    local equipment = inventory[instanceId]
    
    if not equipment then
        return {
            success = false,
            error = "裝備不存在"
        }
    end
    
    -- 計算出售價格 (裝備評分的10%)
    local sellPrice = math.floor(equipment.score * 0.1)
    
    -- 移除裝備
    inventory[instanceId] = nil
    
    -- 給予金幣 (這裡應該調用貨幣系統)
    print(string.format("💰 %s 出售了 %s，獲得 %d 金幣", player.Name, equipment.name, sellPrice))
    
    -- 觸發背包更新事件
    self.Client.OnInventoryUpdated:Fire(player)
    
    return {
        success = true,
        sellPrice = sellPrice
    }
end

-- 客戶端方法：修理裝備
function EquipmentService.Client:RepairEquipment(player, instanceId)
    if not instanceId or instanceId == "" then
        return {
            success = false,
            error = "無效的裝備ID"
        }
    end
    
    -- 這裡可以實現耐久度系統
    -- 暫時返回成功
    return {
        success = true,
        message = "裝備修理完成"
    }
end

-- 客戶端方法：比較裝備
function EquipmentService.Client:CompareEquipment(player, instanceId1, instanceId2)
    if not instanceId1 or not instanceId2 then
        return {
            success = false,
            error = "需要兩個裝備ID"
        }
    end
    
    local inventory = EquipmentManager:GetPlayerInventory(player)
    local equipped = EquipmentManager:GetPlayerEquipment(player)
    
    -- 查找兩個裝備
    local equipment1 = inventory[instanceId1]
    local equipment2 = inventory[instanceId2]
    
    -- 如果在背包中找不到，嘗試在已裝備中查找
    if not equipment1 then
        for slot, equippedItem in pairs(equipped) do
            if equippedItem.instanceId == instanceId1 then
                equipment1 = equippedItem
                break
            end
        end
    end
    
    if not equipment2 then
        for slot, equippedItem in pairs(equipped) do
            if equippedItem.instanceId == instanceId2 then
                equipment2 = equippedItem
                break
            end
        end
    end
    
    if not equipment1 or not equipment2 then
        return {
            success = false,
            error = "裝備不存在"
        }
    end
    
    -- 比較屬性
    local comparison = {
        equipment1 = self:SanitizeEquipmentData(equipment1),
        equipment2 = self:SanitizeEquipmentData(equipment2),
        statComparison = {}
    }
    
    -- 比較各項屬性
    local allStats = {}
    for statName, _ in pairs(equipment1.finalStats) do
        allStats[statName] = true
    end
    for statName, _ in pairs(equipment2.finalStats) do
        allStats[statName] = true
    end
    
    for statName, _ in pairs(allStats) do
        local value1 = equipment1.finalStats[statName] or 0
        local value2 = equipment2.finalStats[statName] or 0
        
        comparison.statComparison[statName] = {
            equipment1 = value1,
            equipment2 = value2,
            difference = value2 - value1
        }
    end
    
    return {
        success = true,
        comparison = comparison
    }
end

-- 服務端方法：給玩家添加裝備
function EquipmentService:GiveEquipment(player, equipmentId, quality, enhanceLevel)
    quality = quality or "COMMON"
    enhanceLevel = enhanceLevel or 0
    
    return EquipmentManager:AddEquipmentToInventory(player, equipmentId, quality, enhanceLevel)
end

-- 服務端方法：獲取裝備管理器
function EquipmentService:GetEquipmentManager()
    return EquipmentManager
end

-- 清理敏感數據
function EquipmentService:SanitizeEquipmentData(equipmentData)
    if not equipmentData then
        return nil
    end
    
    -- 創建副本並移除敏感信息
    local sanitized = {}
    
    -- 複製安全的字段
    local safeFields = {
        "id", "instanceId", "name", "description", "slot", "requiredLevel",
        "suitableClasses", "icon", "quality", "enhanceLevel", "finalStats",
        "score", "createdTime"
    }
    
    for _, field in ipairs(safeFields) do
        sanitized[field] = equipmentData[field]
    end
    
    return sanitized
end

-- 調試信息
function EquipmentService:Debug()
    print("⚔️ 裝備服務調試信息:")
    EquipmentManager:Debug()
end

return EquipmentService
