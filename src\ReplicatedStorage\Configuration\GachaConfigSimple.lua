-- 簡化版扭蛋配置系統 - 寵物獲得的唯一途徑
local GachaConfig = {}

-- ⚠️ 重要設計原則 ⚠️
-- 寵物只能從扭蛋獲得，不能從打怪獲得
-- 打怪只掉落：武器、裝備、材料、金幣、經驗值

-- 扭蛋類型配置
GachaConfig.GACHA_TYPES = {
    -- 金幣扭蛋 (100金幣)
    COIN = {
        name = "金幣扭蛋",
        description = "使用金幣進行的基礎扭蛋",
        cost = 100,
        currency = "coins",
        icon = "💰",
        
        -- 寵物獲得機率
        petRates = {
            COMMON = 70,    -- 70% 普通寵物
            UNCOMMON = 20,  -- 20% 稀有寵物
            RARE = 8,       -- 8% 史詩寵物
            EPIC = 2,       -- 2% 神話寵物
            LEGENDARY = 0   -- 0% 傳說寵物 (金幣扭蛋無法獲得)
        },
        
        -- 每日限制
        dailyLimit = 50
    },
    
    -- 寶石扭蛋 (10寶石)
    GEM = {
        name = "寶石扭蛋",
        description = "使用寶石進行的高級扭蛋",
        cost = 10,
        currency = "gems",
        icon = "💎",
        
        -- 寵物獲得機率
        petRates = {
            COMMON = 50,    -- 50% 普通寵物
            UNCOMMON = 30,  -- 30% 稀有寵物
            RARE = 15,      -- 15% 史詩寵物
            EPIC = 4,       -- 4% 神話寵物
            LEGENDARY = 1   -- 1% 傳說寵物
        },
        
        -- 每日限制
        dailyLimit = 20
    },
    
    -- R幣扭蛋 (1 R幣)
    ROBUX = {
        name = "R幣扭蛋",
        description = "使用R幣進行的頂級扭蛋",
        cost = 1,
        currency = "robux",
        icon = "🔥",
        
        -- 寵物獲得機率
        petRates = {
            COMMON = 30,    -- 30% 普通寵物
            UNCOMMON = 35,  -- 35% 稀有寵物
            RARE = 25,      -- 25% 史詩寵物
            EPIC = 8,       -- 8% 神話寵物
            LEGENDARY = 2   -- 2% 傳說寵物
        },
        
        -- 每日限制
        dailyLimit = 10
    },
    
    -- 超級扭蛋 (50寶石)
    SUPER = {
        name = "超級扭蛋",
        description = "頂級扭蛋，高機率獲得稀有寵物",
        cost = 50,
        currency = "gems",
        icon = "⭐",
        
        -- 寵物獲得機率
        petRates = {
            COMMON = 0,     -- 0% 普通寵物 (超級扭蛋不出普通)
            UNCOMMON = 40,  -- 40% 稀有寵物
            RARE = 35,      -- 35% 史詩寵物
            EPIC = 20,      -- 20% 神話寵物
            LEGENDARY = 5   -- 5% 傳說寵物
        },
        
        -- 每日限制
        dailyLimit = 5
    }
}

-- 寵物稀有度配置
GachaConfig.PET_RARITY = {
    COMMON = {
        name = "普通",
        color = Color3.fromRGB(150, 150, 150),
        weight = 1.0,
        description = "常見的寵物，容易獲得"
    },
    UNCOMMON = {
        name = "稀有",
        color = Color3.fromRGB(100, 200, 100),
        weight = 1.2,
        description = "較為稀有的寵物，有一定價值"
    },
    RARE = {
        name = "史詩",
        color = Color3.fromRGB(100, 100, 200),
        weight = 1.5,
        description = "史詩級寵物，非常珍貴"
    },
    EPIC = {
        name = "神話",
        color = Color3.fromRGB(200, 100, 200),
        weight = 2.0,
        description = "神話級寵物，極其稀有"
    },
    LEGENDARY = {
        name = "傳說",
        color = Color3.fromRGB(255, 200, 0),
        weight = 3.0,
        description = "傳說級寵物，萬中無一"
    }
}

-- 寵物獲得來源驗證
GachaConfig.PET_SOURCE_VALIDATION = {
    -- 允許的寵物獲得來源
    allowedSources = {
        "gacha_coin",
        "gacha_gem", 
        "gacha_robux",
        "gacha_super",
        "admin_gift",      -- 管理員贈送
        "event_reward",    -- 活動獎勵
        "starter_pet"      -- 新手寵物
    },
    
    -- 禁止的寵物獲得來源
    forbiddenSources = {
        "monster_drop",    -- ❌ 禁止從打怪獲得
        "wild_encounter",  -- ❌ 禁止從野外遭遇獲得
        "dungeon_drop",    -- ❌ 禁止從副本掉落獲得
        "trade_npc"        -- ❌ 禁止從NPC交易獲得
    }
}

-- 驗證機率總和
function GachaConfig.validateRates()
    for gachaType, config in pairs(GachaConfig.GACHA_TYPES) do
        local total = 0
        for rarity, rate in pairs(config.petRates) do
            total = total + rate
        end
        
        if total ~= 100 then
            warn(string.format("⚠️ %s 扭蛋機率總和不等於100%% (當前: %d%%)", gachaType, total))
            return false
        end
    end
    return true
end

-- 驗證寵物來源
function GachaConfig.validatePetSource(source)
    local allowed = GachaConfig.PET_SOURCE_VALIDATION.allowedSources
    local forbidden = GachaConfig.PET_SOURCE_VALIDATION.forbiddenSources
    
    -- 檢查是否在禁止列表中
    for _, forbiddenSource in ipairs(forbidden) do
        if source == forbiddenSource then
            warn(string.format("❌ 非法寵物來源: %s (寵物只能從扭蛋獲得)", source))
            return false
        end
    end
    
    -- 檢查是否在允許列表中
    for _, allowedSource in ipairs(allowed) do
        if source == allowedSource then
            return true
        end
    end
    
    warn(string.format("⚠️ 未知寵物來源: %s", source))
    return false
end

-- 系統初始化檢查
function GachaConfig:Initialize()
    print("🎰 初始化扭蛋配置系統...")
    
    -- 驗證機率配置
    if not GachaConfig.validateRates() then
        error("❌ 扭蛋機率配置錯誤")
    end
    
    print("✅ 扭蛋配置系統初始化完成")
    print("📋 重要提醒: 寵物只能從扭蛋獲得，不能從打怪獲得")
    
    return true
end

return GachaConfig
