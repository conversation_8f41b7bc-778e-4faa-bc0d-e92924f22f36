-- 裝備配置系統
local EquipmentConfig = {}

-- 裝備品質系統 (普通→優良→稀有→史詩→傳說)
EquipmentConfig.QUALITY = {
    COMMON = {
        name = "普通",
        color = Color3.fromRGB(150, 150, 150),
        multiplier = 1.0,
        description = "基礎品質的裝備"
    },
    UNCOMMON = {
        name = "優良",
        color = Color3.fromRGB(100, 200, 100),
        multiplier = 1.3,
        description = "品質不錯的裝備"
    },
    RARE = {
        name = "稀有",
        color = Color3.fromRGB(100, 100, 200),
        multiplier = 1.6,
        description = "稀有的優秀裝備"
    },
    EPIC = {
        name = "史詩",
        color = Color3.fromRGB(200, 100, 200),
        multiplier = 2.0,
        description = "史詩級的強大裝備"
    },
    LEGENDARY = {
        name = "傳說",
        color = Color3.fromRGB(255, 200, 0),
        multiplier = 2.5,
        description = "傳說級的神器"
    }
}

-- 裝備類型
EquipmentConfig.EQUIPMENT_TYPES = {
    WEAPON = "weapon",
    ARMOR = "armor",
    ACCESSORY = "accessory"
}

-- 裝備部位
EquipmentConfig.EQUIPMENT_SLOTS = {
    -- 武器
    MAIN_HAND = {
        name = "主手",
        type = "weapon",
        description = "主要武器"
    },
    OFF_HAND = {
        name = "副手",
        type = "weapon",
        description = "副手武器或盾牌"
    },
    
    -- 護甲
    HELMET = {
        name = "頭盔",
        type = "armor",
        description = "頭部防護"
    },
    CHEST = {
        name = "胸甲",
        type = "armor",
        description = "胸部防護"
    },
    LEGS = {
        name = "腿甲",
        type = "armor",
        description = "腿部防護"
    },
    BOOTS = {
        name = "靴子",
        type = "armor",
        description = "腳部防護"
    },
    GLOVES = {
        name = "手套",
        type = "armor",
        description = "手部防護"
    },
    
    -- 飾品
    RING = {
        name = "戒指",
        type = "accessory",
        description = "魔法戒指"
    },
    NECKLACE = {
        name = "項鍊",
        type = "accessory",
        description = "魔法項鍊"
    },
    EARRING = {
        name = "耳環",
        type = "accessory",
        description = "魔法耳環"
    }
}

-- 武器配置 (10件武器，涵蓋1-50級)
EquipmentConfig.WEAPONS = {
    -- 戰士武器
    {
        id = "sword_basic",
        name = "新手劍",
        description = "適合新手戰士的基礎劍",
        slot = "MAIN_HAND",
        requiredLevel = 1,
        suitableClasses = {"WARRIOR"},
        baseStats = {
            attack = 15,
            strength = 2
        },
        icon = "🗡️"
    },
    {
        id = "sword_iron",
        name = "鐵劍",
        description = "堅固的鐵製劍",
        slot = "MAIN_HAND",
        requiredLevel = 10,
        suitableClasses = {"WARRIOR"},
        baseStats = {
            attack = 35,
            strength = 5,
            defense = 2
        },
        icon = "⚔️"
    },
    {
        id = "sword_steel",
        name = "鋼劍",
        description = "精煉的鋼製劍",
        slot = "MAIN_HAND",
        requiredLevel = 25,
        suitableClasses = {"WARRIOR"},
        baseStats = {
            attack = 65,
            strength = 10,
            defense = 5
        },
        icon = "🗡️"
    },
    
    -- 法師武器
    {
        id = "staff_basic",
        name = "新手法杖",
        description = "適合新手法師的基礎法杖",
        slot = "MAIN_HAND",
        requiredLevel = 1,
        suitableClasses = {"MAGE"},
        baseStats = {
            magicAttack = 20,
            intelligence = 3,
            mana = 10
        },
        icon = "🪄"
    },
    {
        id = "staff_oak",
        name = "橡木法杖",
        description = "橡木製成的法杖",
        slot = "MAIN_HAND",
        requiredLevel = 15,
        suitableClasses = {"MAGE"},
        baseStats = {
            magicAttack = 45,
            intelligence = 7,
            mana = 25
        },
        icon = "🔮"
    },
    {
        id = "staff_crystal",
        name = "水晶法杖",
        description = "鑲嵌水晶的強力法杖",
        slot = "MAIN_HAND",
        requiredLevel = 35,
        suitableClasses = {"MAGE"},
        baseStats = {
            magicAttack = 85,
            intelligence = 15,
            mana = 50,
            magicResist = 8
        },
        icon = "✨"
    },
    
    -- 弓箭手武器
    {
        id = "bow_basic",
        name = "新手弓",
        description = "適合新手弓箭手的基礎弓",
        slot = "MAIN_HAND",
        requiredLevel = 1,
        suitableClasses = {"ARCHER"},
        baseStats = {
            attack = 18,
            agility = 3
        },
        icon = "🏹"
    },
    {
        id = "bow_composite",
        name = "複合弓",
        description = "精製的複合材料弓",
        slot = "MAIN_HAND",
        requiredLevel = 20,
        suitableClasses = {"ARCHER"},
        baseStats = {
            attack = 55,
            agility = 8,
            strength = 3
        },
        icon = "🎯"
    },
    
    -- 牧師武器
    {
        id = "wand_basic",
        name = "新手魔杖",
        description = "適合新手牧師的基礎魔杖",
        slot = "MAIN_HAND",
        requiredLevel = 1,
        suitableClasses = {"CLERIC"},
        baseStats = {
            healPower = 15,
            intelligence = 2,
            mana = 15
        },
        icon = "🪄"
    },
    {
        id = "wand_blessed",
        name = "祝福魔杖",
        description = "受到神聖祝福的魔杖",
        slot = "MAIN_HAND",
        requiredLevel = 30,
        suitableClasses = {"CLERIC"},
        baseStats = {
            healPower = 70,
            intelligence = 12,
            mana = 40,
            magicResist = 10
        },
        icon = "✨"
    }
}

-- 護甲配置 (10件護甲，涵蓋1-50級)
EquipmentConfig.ARMOR = {
    -- 頭盔
    {
        id = "helmet_leather",
        name = "皮革頭盔",
        description = "基礎的皮革頭盔",
        slot = "HELMET",
        requiredLevel = 5,
        baseStats = {
            defense = 8,
            vitality = 2
        },
        icon = "🪖"
    },
    {
        id = "helmet_iron",
        name = "鐵頭盔",
        description = "堅固的鐵製頭盔",
        slot = "HELMET",
        requiredLevel = 20,
        baseStats = {
            defense = 20,
            vitality = 5,
            strength = 2
        },
        icon = "⛑️"
    },
    
    -- 胸甲
    {
        id = "chest_cloth",
        name = "布甲",
        description = "輕便的布製胸甲",
        slot = "CHEST",
        requiredLevel = 3,
        baseStats = {
            defense = 5,
            magicResist = 3
        },
        icon = "🦺"
    },
    {
        id = "chest_leather",
        name = "皮甲",
        description = "靈活的皮革胸甲",
        slot = "CHEST",
        requiredLevel = 10,
        baseStats = {
            defense = 15,
            agility = 3,
            vitality = 2
        },
        icon = "🦺"
    },
    {
        id = "chest_chainmail",
        name = "鎖子甲",
        description = "防護性優秀的鎖子甲",
        slot = "CHEST",
        requiredLevel = 25,
        baseStats = {
            defense = 35,
            vitality = 8,
            strength = 3
        },
        icon = "🛡️"
    },
    
    -- 腿甲
    {
        id = "legs_cloth",
        name = "布褲",
        description = "基礎的布製腿甲",
        slot = "LEGS",
        requiredLevel = 8,
        baseStats = {
            defense = 6,
            agility = 2
        },
        icon = "👖"
    },
    {
        id = "legs_plate",
        name = "板甲褲",
        description = "重型的板甲腿甲",
        slot = "LEGS",
        requiredLevel = 30,
        baseStats = {
            defense = 30,
            vitality = 6,
            strength = 4
        },
        icon = "🦵"
    },
    
    -- 靴子
    {
        id = "boots_leather",
        name = "皮靴",
        description = "舒適的皮革靴子",
        slot = "BOOTS",
        requiredLevel = 6,
        baseStats = {
            defense = 4,
            agility = 3
        },
        icon = "👢"
    },
    
    -- 手套
    {
        id = "gloves_iron",
        name = "鐵手套",
        description = "堅固的鐵製手套",
        slot = "GLOVES",
        requiredLevel = 15,
        baseStats = {
            defense = 10,
            strength = 3,
            attack = 5
        },
        icon = "🧤"
    },
    
    -- 盾牌 (副手)
    {
        id = "shield_wooden",
        name = "木盾",
        description = "基礎的木製盾牌",
        slot = "OFF_HAND",
        requiredLevel = 12,
        suitableClasses = {"WARRIOR", "CLERIC"},
        baseStats = {
            defense = 18,
            vitality = 4
        },
        icon = "🛡️"
    }
}

-- 飾品配置 (10件飾品，涵蓋1-50級)
EquipmentConfig.ACCESSORIES = {
    -- 戒指
    {
        id = "ring_power",
        name = "力量戒指",
        description = "增強力量的魔法戒指",
        slot = "RING",
        requiredLevel = 10,
        baseStats = {
            strength = 5,
            attack = 8
        },
        icon = "💍"
    },
    {
        id = "ring_wisdom",
        name = "智慧戒指",
        description = "增強智慧的魔法戒指",
        slot = "RING",
        requiredLevel = 15,
        baseStats = {
            intelligence = 6,
            mana = 20
        },
        icon = "💍"
    },
    {
        id = "ring_agility",
        name = "敏捷戒指",
        description = "增強敏捷的魔法戒指",
        slot = "RING",
        requiredLevel = 20,
        baseStats = {
            agility = 7,
            attack = 5
        },
        icon = "💍"
    },
    
    -- 項鍊
    {
        id = "necklace_health",
        name = "生命項鍊",
        description = "增強生命力的魔法項鍊",
        slot = "NECKLACE",
        requiredLevel = 12,
        baseStats = {
            vitality = 8,
            health = 50
        },
        icon = "📿"
    },
    {
        id = "necklace_mana",
        name = "魔力項鍊",
        description = "增強魔力的魔法項鍊",
        slot = "NECKLACE",
        requiredLevel = 18,
        baseStats = {
            intelligence = 5,
            mana = 40
        },
        icon = "📿"
    },
    {
        id = "necklace_protection",
        name = "守護項鍊",
        description = "提供全面保護的項鍊",
        slot = "NECKLACE",
        requiredLevel = 35,
        baseStats = {
            defense = 15,
            magicResist = 15,
            vitality = 6
        },
        icon = "📿"
    },
    
    -- 耳環
    {
        id = "earring_focus",
        name = "專注耳環",
        description = "提升專注力的魔法耳環",
        slot = "EARRING",
        requiredLevel = 25,
        baseStats = {
            intelligence = 8,
            magicAttack = 12
        },
        icon = "👂"
    },
    {
        id = "earring_speed",
        name = "迅捷耳環",
        description = "提升速度的魔法耳環",
        slot = "EARRING",
        requiredLevel = 28,
        baseStats = {
            agility = 10,
            attack = 8
        },
        icon = "👂"
    },
    {
        id = "earring_balance",
        name = "平衡耳環",
        description = "提供全面屬性的耳環",
        slot = "EARRING",
        requiredLevel = 40,
        baseStats = {
            strength = 4,
            agility = 4,
            intelligence = 4,
            vitality = 4
        },
        icon = "👂"
    },
    {
        id = "earring_legendary",
        name = "傳說耳環",
        description = "傳說級的強力耳環",
        slot = "EARRING",
        requiredLevel = 50,
        baseStats = {
            strength = 8,
            agility = 8,
            intelligence = 8,
            vitality = 8,
            attack = 15,
            defense = 10
        },
        icon = "✨"
    }
}

-- 裝備強化系統 (成功率遞減，材料消耗)
EquipmentConfig.ENHANCEMENT = {
    -- 最大強化等級
    maxLevel = 15,

    -- 強化成功率 (隨等級遞減)
    successRates = {
        [1] = 100,  -- +1: 100%
        [2] = 95,   -- +2: 95%
        [3] = 90,   -- +3: 90%
        [4] = 85,   -- +4: 85%
        [5] = 80,   -- +5: 80%
        [6] = 70,   -- +6: 70%
        [7] = 60,   -- +7: 60%
        [8] = 50,   -- +8: 50%
        [9] = 40,   -- +9: 40%
        [10] = 30,  -- +10: 30%
        [11] = 25,  -- +11: 25%
        [12] = 20,  -- +12: 20%
        [13] = 15,  -- +13: 15%
        [14] = 10,  -- +14: 10%
        [15] = 5    -- +15: 5%
    },

    -- 強化材料消耗
    materialCosts = {
        [1] = {IRON_ORE = 1, coins = 100},
        [2] = {IRON_ORE = 2, coins = 200},
        [3] = {IRON_ORE = 3, coins = 300},
        [4] = {IRON_ORE = 4, coins = 400},
        [5] = {IRON_ORE = 5, SILVER_ORE = 1, coins = 500},
        [6] = {SILVER_ORE = 2, coins = 750},
        [7] = {SILVER_ORE = 3, coins = 1000},
        [8] = {SILVER_ORE = 4, coins = 1500},
        [9] = {SILVER_ORE = 5, GOLD_ORE = 1, coins = 2000},
        [10] = {GOLD_ORE = 2, coins = 3000},
        [11] = {GOLD_ORE = 3, coins = 4000},
        [12] = {GOLD_ORE = 4, MITHRIL = 1, coins = 6000},
        [13] = {MITHRIL = 2, coins = 8000},
        [14] = {MITHRIL = 3, coins = 12000},
        [15] = {MITHRIL = 5, VOID_CRYSTAL = 1, coins = 20000}
    },

    -- 強化失敗懲罰
    failurePenalty = {
        -- 1-5級失敗無懲罰
        [6] = "none",
        [7] = "none",
        [8] = "none",
        [9] = "materials_lost",     -- 失敗時材料消失
        [10] = "materials_lost",
        [11] = "level_decrease",    -- 失敗時等級-1
        [12] = "level_decrease",
        [13] = "level_decrease",
        [14] = "equipment_destroy", -- 失敗時裝備銷毀
        [15] = "equipment_destroy"
    },

    -- 強化屬性加成 (品質×強化等級加成)
    statMultiplier = function(quality, enhanceLevel)
        local qualityMultiplier = EquipmentConfig.QUALITY[quality].multiplier
        local enhanceMultiplier = 1 + (enhanceLevel * 0.1) -- 每級+10%
        return qualityMultiplier * enhanceMultiplier
    end
}

-- 裝備套裝系統
EquipmentConfig.EQUIPMENT_SETS = {
    WARRIOR_SET = {
        name = "戰士套裝",
        description = "專為戰士設計的套裝",
        pieces = {"sword_steel", "helmet_iron", "chest_chainmail", "shield_wooden"},
        setBonuses = {
            [2] = {strength = 5, defense = 10},      -- 2件套
            [3] = {vitality = 8, attack = 15},       -- 3件套
            [4] = {strength = 10, defense = 20, vitality = 15} -- 4件套
        }
    },
    MAGE_SET = {
        name = "法師套裝",
        description = "專為法師設計的套裝",
        pieces = {"staff_crystal", "ring_wisdom", "necklace_mana", "earring_focus"},
        setBonuses = {
            [2] = {intelligence = 8, mana = 30},
            [3] = {magicAttack = 20, magicResist = 10},
            [4] = {intelligence = 15, mana = 60, magicAttack = 35}
        }
    }
}

-- 裝備屬性計算公式
EquipmentConfig.STAT_FORMULAS = {
    -- 計算最終屬性值
    calculateFinalStats = function(baseStats, quality, enhanceLevel)
        local multiplier = EquipmentConfig.ENHANCEMENT.statMultiplier(quality, enhanceLevel)
        local finalStats = {}

        for statName, baseValue in pairs(baseStats) do
            finalStats[statName] = math.floor(baseValue * multiplier)
        end

        return finalStats
    end,

    -- 計算裝備評分
    calculateEquipmentScore = function(stats, quality, enhanceLevel)
        local score = 0
        local weights = {
            attack = 2.0,
            magicAttack = 2.0,
            defense = 1.5,
            magicResist = 1.5,
            strength = 1.0,
            agility = 1.0,
            intelligence = 1.0,
            vitality = 1.0,
            health = 0.1,
            mana = 0.1
        }

        for statName, value in pairs(stats) do
            local weight = weights[statName] or 1.0
            score = score + (value * weight)
        end

        local qualityMultiplier = EquipmentConfig.QUALITY[quality].multiplier
        local enhanceMultiplier = 1 + (enhanceLevel * 0.2)

        return math.floor(score * qualityMultiplier * enhanceMultiplier)
    end
}

return EquipmentConfig
