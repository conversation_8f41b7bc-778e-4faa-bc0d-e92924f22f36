-- 野外怪物生成管理系統
local WildMonsterManager = {}

-- 依賴
local WorldMapConfig = require(game.ReplicatedStorage.Configuration.WorldMapConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 怪物數據存儲
WildMonsterManager.activeMonsters = {} -- 當前活躍的怪物
WildMonsterManager.spawnTimers = {}    -- 生成計時器
WildMonsterManager.isInitialized = false

-- 初始化野外怪物管理系統
function WildMonsterManager:Initialize()
    if self.isInitialized then
        warn("⚠️ 野外怪物管理系統已經初始化")
        return true
    end
    
    print("🐾 初始化野外怪物管理系統...")
    
    -- 重置數據
    self.activeMonsters = {}
    self.spawnTimers = {}
    
    -- 為每個野外區域初始化怪物生成
    for areaName, areaConfig in pairs(WorldMapConfig.MAIN_WORLD.wildAreas) do
        self:InitializeAreaSpawning(areaName, areaConfig)
    end
    
    -- 設置事件處理器
    self:SetupEventHandlers()
    
    self.isInitialized = true
    print("✅ 野外怪物管理系統初始化完成")
    
    return true
end

-- 初始化區域怪物生成
function WildMonsterManager:InitializeAreaSpawning(areaName, areaConfig)
    print(string.format("🗺️ 初始化 %s 區域怪物生成", areaConfig.name))
    
    -- 初始化該區域的怪物列表
    self.activeMonsters[areaName] = {}
    
    -- 開始生成怪物
    self:StartAreaSpawning(areaName, areaConfig)
end

-- 開始區域怪物生成
function WildMonsterManager:StartAreaSpawning(areaName, areaConfig)
    local monsters = areaConfig.monsters
    
    -- 初始生成
    self:SpawnMonstersInArea(areaName, areaConfig)
    
    -- 設置重生計時器
    self.spawnTimers[areaName] = task.spawn(function()
        while self.isInitialized do
            task.wait(monsters.respawnTime)
            self:RespawnMonstersInArea(areaName, areaConfig)
        end
    end)
    
    print(string.format("✅ %s 區域怪物生成已啟動", areaConfig.name))
end

-- 在區域中生成怪物
function WildMonsterManager:SpawnMonstersInArea(areaName, areaConfig)
    local monsters = areaConfig.monsters
    local currentCount = #self.activeMonsters[areaName]
    
    -- 計算需要生成的怪物數量
    local spawnCount = math.floor(monsters.maxCount * monsters.spawnRate) - currentCount
    
    if spawnCount <= 0 then
        return
    end
    
    print(string.format("🐾 在 %s 生成 %d 隻怪物", areaConfig.name, spawnCount))
    
    for i = 1, spawnCount do
        local monster = self:CreateRandomMonster(areaName, areaConfig)
        if monster then
            table.insert(self.activeMonsters[areaName], monster)
            
            -- 觸發怪物生成事件
            EventManager:Fire(EventManager.EventTypes.WILD_MONSTER_SPAWNED, {
                areaName = areaName,
                monster = monster
            })
        end
    end
end

-- 重生區域怪物
function WildMonsterManager:RespawnMonstersInArea(areaName, areaConfig)
    -- 清理死亡的怪物
    self:CleanupDeadMonsters(areaName)
    
    -- 生成新怪物
    self:SpawnMonstersInArea(areaName, areaConfig)
end

-- 創建隨機怪物
function WildMonsterManager:CreateRandomMonster(areaName, areaConfig)
    local monsters = areaConfig.monsters
    
    -- 根據權重選擇怪物類型
    local selectedType = self:SelectMonsterTypeByWeight(monsters.types)
    if not selectedType then
        return nil
    end
    
    -- 隨機選擇模型
    local modelName = selectedType.models[math.random(1, #selectedType.models)]
    
    -- 隨機等級
    local level = math.random(selectedType.level.min, selectedType.level.max)
    
    -- 隨機位置 (在區域範圍內)
    local position = self:GetRandomPositionInArea(areaConfig.area)
    
    -- 創建怪物數據
    local monster = {
        id = self:GenerateMonsterID(),
        areaName = areaName,
        modelName = modelName,
        rarity = selectedType.rarity,
        level = level,
        position = position,
        
        -- 屬性計算
        maxHealth = self:CalculateMonsterHealth(level, selectedType.rarity),
        currentHealth = nil, -- 將在下面設置
        attack = self:CalculateMonsterAttack(level, selectedType.rarity),
        defense = self:CalculateMonsterDefense(level, selectedType.rarity),
        
        -- 獎勵
        experience = math.random(selectedType.experience.min, selectedType.experience.max),
        
        -- 狀態
        isAlive = true,
        isInCombat = false,
        spawnTime = tick(),
        lastCombatTime = 0,
        
        -- AI 狀態
        aiState = "idle", -- idle, patrolling, chasing, attacking
        target = nil,
        homePosition = position,
        patrolRadius = 20
    }
    
    monster.currentHealth = monster.maxHealth
    
    return monster
end

-- 根據權重選擇怪物類型
function WildMonsterManager:SelectMonsterTypeByWeight(monsterTypes)
    local totalWeight = 0
    for _, monsterType in ipairs(monsterTypes) do
        totalWeight = totalWeight + monsterType.weight
    end
    
    local randomValue = math.random(1, totalWeight)
    local currentWeight = 0
    
    for _, monsterType in ipairs(monsterTypes) do
        currentWeight = currentWeight + monsterType.weight
        if randomValue <= currentWeight then
            return monsterType
        end
    end
    
    return monsterTypes[1] -- 備用
end

-- 在區域內獲取隨機位置
function WildMonsterManager:GetRandomPositionInArea(areaConfig)
    local center = areaConfig.center
    local innerRadius = areaConfig.innerRadius
    local outerRadius = areaConfig.outerRadius
    
    -- 在環形區域內隨機生成位置
    local angle = math.random() * 2 * math.pi
    local distance = math.random() * (outerRadius - innerRadius) + innerRadius
    
    local x = center.X + math.cos(angle) * distance
    local z = center.Z + math.sin(angle) * distance
    local y = center.Y -- 假設地面高度相同
    
    return Vector3.new(x, y, z)
end

-- 計算怪物血量
function WildMonsterManager:CalculateMonsterHealth(level, rarity)
    local baseHealth = 100
    local levelMultiplier = level * 15
    
    local rarityMultiplier = {
        COMMON = 1.0,
        UNCOMMON = 1.3,
        RARE = 1.6,
        EPIC = 2.0,
        LEGENDARY = 2.5
    }
    
    return math.floor(baseHealth + levelMultiplier * (rarityMultiplier[rarity] or 1.0))
end

-- 計算怪物攻擊力
function WildMonsterManager:CalculateMonsterAttack(level, rarity)
    local baseAttack = 20
    local levelMultiplier = level * 3
    
    local rarityMultiplier = {
        COMMON = 1.0,
        UNCOMMON = 1.2,
        RARE = 1.4,
        EPIC = 1.7,
        LEGENDARY = 2.0
    }
    
    return math.floor(baseAttack + levelMultiplier * (rarityMultiplier[rarity] or 1.0))
end

-- 計算怪物防禦力
function WildMonsterManager:CalculateMonsterDefense(level, rarity)
    local baseDefense = 10
    local levelMultiplier = level * 2
    
    local rarityMultiplier = {
        COMMON = 1.0,
        UNCOMMON = 1.1,
        RARE = 1.3,
        EPIC = 1.5,
        LEGENDARY = 1.8
    }
    
    return math.floor(baseDefense + levelMultiplier * (rarityMultiplier[rarity] or 1.0))
end

-- 生成怪物ID
function WildMonsterManager:GenerateMonsterID()
    return "monster_" .. tick() .. "_" .. math.random(1000, 9999)
end

-- 設置事件處理器
function WildMonsterManager:SetupEventHandlers()
    -- 監聽玩家進入區域事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_ENTERED_AREA, function(data)
        self:OnPlayerEnteredArea(data)
    end)
    
    -- 監聽玩家離開區域事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_LEFT_AREA, function(data)
        self:OnPlayerLeftArea(data)
    end)
    
    -- 監聽怪物死亡事件
    EventManager:Connect(EventManager.EventTypes.WILD_MONSTER_DIED, function(data)
        self:OnMonsterDied(data)
    end)
end

-- 玩家進入區域事件處理
function WildMonsterManager:OnPlayerEnteredArea(data)
    local areaName = data.areaName
    local player = data.player
    
    print(string.format("👤 %s 進入了 %s", player.Name, areaName))
    
    -- 可以在這裡添加區域特定的邏輯
    -- 例如：增加怪物生成率、觸發特殊事件等
end

-- 玩家離開區域事件處理
function WildMonsterManager:OnPlayerLeftArea(data)
    local areaName = data.areaName
    local player = data.player
    
    print(string.format("👤 %s 離開了 %s", player.Name, areaName))
end

-- 怪物死亡事件處理
function WildMonsterManager:OnMonsterDied(data)
    local monster = data.monster
    monster.isAlive = false
    monster.deathTime = tick()
    
    print(string.format("💀 怪物 %s (等級 %d) 死亡", monster.modelName, monster.level))
    
    -- 給予玩家經驗值
    if data.killer then
        EventManager:Fire(EventManager.EventTypes.PLAYER_GAINED_EXP, {
            player = data.killer,
            experience = monster.experience,
            source = "wild_monster"
        })
    end
end

-- 清理死亡的怪物
function WildMonsterManager:CleanupDeadMonsters(areaName)
    local monsters = self.activeMonsters[areaName]
    if not monsters then return end
    
    local aliveMonsters = {}
    local cleanupCount = 0
    
    for _, monster in ipairs(monsters) do
        if monster.isAlive then
            table.insert(aliveMonsters, monster)
        else
            cleanupCount = cleanupCount + 1
        end
    end
    
    self.activeMonsters[areaName] = aliveMonsters
    
    if cleanupCount > 0 then
        print(string.format("🧹 清理了 %s 區域的 %d 隻死亡怪物", areaName, cleanupCount))
    end
end

-- 獲取區域內的怪物
function WildMonsterManager:GetMonstersInArea(areaName)
    return self.activeMonsters[areaName] or {}
end

-- 獲取玩家附近的怪物
function WildMonsterManager:GetNearbyMonsters(playerPosition, radius)
    local nearbyMonsters = {}
    
    for areaName, monsters in pairs(self.activeMonsters) do
        for _, monster in ipairs(monsters) do
            if monster.isAlive then
                local distance = (monster.position - playerPosition).Magnitude
                if distance <= radius then
                    table.insert(nearbyMonsters, monster)
                end
            end
        end
    end
    
    return nearbyMonsters
end

-- 攻擊怪物
function WildMonsterManager:AttackMonster(monsterId, damage, attacker)
    local monster = self:FindMonsterById(monsterId)
    if not monster or not monster.isAlive then
        return false
    end
    
    -- 應用傷害
    monster.currentHealth = math.max(0, monster.currentHealth - damage)
    monster.isInCombat = true
    monster.lastCombatTime = tick()
    
    -- 觸發怪物受傷事件
    EventManager:Fire(EventManager.EventTypes.WILD_MONSTER_DAMAGED, {
        monster = monster,
        damage = damage,
        attacker = attacker,
        remainingHealth = monster.currentHealth
    })
    
    -- 檢查死亡
    if monster.currentHealth <= 0 then
        EventManager:Fire(EventManager.EventTypes.WILD_MONSTER_DIED, {
            monster = monster,
            killer = attacker
        })
    end
    
    return true
end

-- 根據ID查找怪物
function WildMonsterManager:FindMonsterById(monsterId)
    for areaName, monsters in pairs(self.activeMonsters) do
        for _, monster in ipairs(monsters) do
            if monster.id == monsterId then
                return monster
            end
        end
    end
    return nil
end

-- 獲取統計信息
function WildMonsterManager:GetStats()
    local stats = {
        totalMonsters = 0,
        aliveMonsters = 0,
        monstersByArea = {},
        monstersByRarity = {}
    }
    
    for areaName, monsters in pairs(self.activeMonsters) do
        local areaCount = 0
        local areaAlive = 0
        
        for _, monster in ipairs(monsters) do
            stats.totalMonsters = stats.totalMonsters + 1
            areaCount = areaCount + 1
            
            if monster.isAlive then
                stats.aliveMonsters = stats.aliveMonsters + 1
                areaAlive = areaAlive + 1
                
                -- 按稀有度統計
                stats.monstersByRarity[monster.rarity] = (stats.monstersByRarity[monster.rarity] or 0) + 1
            end
        end
        
        stats.monstersByArea[areaName] = {
            total = areaCount,
            alive = areaAlive
        }
    end
    
    return stats
end

-- 調試信息
function WildMonsterManager:Debug()
    print("🐾 野外怪物管理系統調試信息:")
    print("  初始化狀態:", self.isInitialized)
    
    local stats = self:GetStats()
    print("  總怪物數:", stats.totalMonsters)
    print("  存活怪物數:", stats.aliveMonsters)
    
    print("  各區域怪物數:")
    for areaName, areaStats in pairs(stats.monstersByArea) do
        print(string.format("    %s: %d/%d (存活/總數)", areaName, areaStats.alive, areaStats.total))
    end
    
    print("  各稀有度怪物數:")
    for rarity, count in pairs(stats.monstersByRarity) do
        print(string.format("    %s: %d", rarity, count))
    end
end

-- 停止系統
function WildMonsterManager:Shutdown()
    if not self.isInitialized then
        return
    end
    
    print("🛑 停止野外怪物管理系統...")
    
    -- 停止所有計時器
    for areaName, timer in pairs(self.spawnTimers) do
        if timer then
            task.cancel(timer)
        end
    end
    
    -- 清理數據
    self.activeMonsters = {}
    self.spawnTimers = {}
    self.isInitialized = false
    
    print("✅ 野外怪物管理系統已停止")
end

return WildMonsterManager
