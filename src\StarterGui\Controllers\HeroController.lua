-- 英雄控制器 - 客戶端界面處理
local HeroController = {}

-- 依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local HeroConfig = require(game.ReplicatedStorage.Configuration.HeroConfig)

-- Fusion 組件
local New = Fusion.New
local State = Fusion.State
local Computed = Fusion.Computed

-- 創建 Knit 控制器
HeroController = Knit.CreateController({
    Name = "HeroController"
})

-- 狀態管理
HeroController.heroData = State(nil)
HeroController.heroStats = State(nil)
HeroController.isLoading = State(true)
HeroController.selectedTarget = State(nil)

-- UI 元素
HeroController.mainUI = nil
HeroController.heroService = nil

-- 控制器初始化
function HeroController:KnitInit()
    print("🦸 初始化英雄控制器...")
    
    -- 獲取英雄服務
    self.heroService = Knit.GetService("HeroService")
    
    -- 設置事件監聽
    self:SetupEventListeners()
end

-- 控制器啟動
function HeroController:KnitStart()
    print("✅ 英雄控制器啟動完成")
    
    -- 載入英雄數據
    self:LoadHeroData()
    
    -- 創建 UI
    self:CreateUI()
end

-- 設置事件監聽
function HeroController:SetupEventListeners()
    -- 監聽英雄事件
    self.heroService.OnHeroLevelUp:Connect(function(data)
        self:OnHeroLevelUp(data)
    end)
    
    self.heroService.OnHeroDied:Connect(function(data)
        self:OnHeroDied(data)
    end)
    
    self.heroService.OnHeroResurrected:Connect(function(data)
        self:OnHeroResurrected(data)
    end)
    
    self.heroService.OnHeroGainedExp:Connect(function(data)
        self:OnHeroGainedExp(data)
    end)
    
    self.heroService.OnHeroDamaged:Connect(function(data)
        self:OnHeroDamaged(data)
    end)
    
    self.heroService.OnHeroHealed:Connect(function(data)
        self:OnHeroHealed(data)
    end)
    
    self.heroService.OnCriticalHit:Connect(function(data)
        self:OnCriticalHit(data)
    end)
end

-- 載入英雄數據
function HeroController:LoadHeroData()
    self.isLoading:set(true)
    
    self.heroService:GetHeroData():andThen(function(heroData)
        if heroData then
            self.heroData:set(heroData)
            self:RefreshHeroStats()
        else
            -- 沒有英雄，顯示創建界面
            self:ShowCreateHeroUI()
        end
        self.isLoading:set(false)
    end):catch(function(error)
        warn("載入英雄數據失敗:", error)
        self.isLoading:set(false)
    end)
end

-- 刷新英雄統計
function HeroController:RefreshHeroStats()
    self.heroService:GetHeroStats():andThen(function(stats)
        self.heroStats:set(stats)
    end):catch(function(error)
        warn("獲取英雄統計失敗:", error)
    end)
end

-- 創建英雄
function HeroController:CreateHero(heroClass)
    self.heroService:CreateHero(heroClass):andThen(function(result)
        if result.success then
            self.heroData:set(result.heroData)
            self:RefreshHeroStats()
            self:HideCreateHeroUI()
            self:ShowNotification("英雄創建成功！", "success")
        else
            self:ShowNotification("創建失敗: " .. result.error, "error")
        end
    end):catch(function(error)
        self:ShowNotification("創建失敗: " .. tostring(error), "error")
    end)
end

-- 使用技能
function HeroController:UseSkill(skillName, targetPlayer)
    self.heroService:UseSkill(skillName, targetPlayer):andThen(function(result)
        if result.success then
            self:ShowNotification("技能使用成功！", "success")
            self:RefreshHeroStats()
        else
            self:ShowNotification("技能失敗: " .. result.error, "error")
        end
    end):catch(function(error)
        self:ShowNotification("技能失敗: " .. tostring(error), "error")
    end)
end

-- 復活英雄
function HeroController:ResurrectHero(method)
    self.heroService:ResurrectHero(method):andThen(function(result)
        if result.success then
            self:ShowNotification("復活成功！", "success")
            self:RefreshHeroStats()
        else
            self:ShowNotification("復活失敗: " .. result.error, "error")
        end
    end):catch(function(error)
        self:ShowNotification("復活失敗: " .. tostring(error), "error")
    end)
end

-- 創建主 UI
function HeroController:CreateUI()
    local playerGui = game.Players.LocalPlayer:WaitForChild("PlayerGui")
    
    self.mainUI = New "ScreenGui" {
        Name = "HeroUI",
        Parent = playerGui,
        ResetOnSpawn = false,
        
        [Fusion.Children] = {
            -- 英雄信息面板
            self:CreateHeroInfoPanel(),
            
            -- 技能面板
            self:CreateSkillPanel(),
            
            -- 狀態面板
            self:CreateStatusPanel(),
            
            -- 通知系統
            self:CreateNotificationPanel()
        }
    }
end

-- 創建英雄信息面板
function HeroController:CreateHeroInfoPanel()
    return New "Frame" {
        Name = "HeroInfoPanel",
        Size = UDim2.new(0, 300, 0, 200),
        Position = UDim2.new(0, 10, 0, 10),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        
        Visible = Computed(function()
            return self.heroData:get() ~= nil and not self.isLoading:get()
        end),
        
        [Fusion.Children] = {
            -- 標題
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 30),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "英雄信息",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            },
            
            -- 英雄名稱和職業
            New "TextLabel" {
                Name = "HeroName",
                Size = UDim2.new(1, -10, 0, 25),
                Position = UDim2.new(0, 5, 0, 35),
                BackgroundTransparency = 1,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSans,
                TextXAlignment = Enum.TextXAlignment.Left,
                
                Text = Computed(function()
                    local heroData = self.heroData:get()
                    if heroData then
                        local className = HeroConfig.CLASSES[heroData.class].name
                        return string.format("%s - %s", heroData.playerName, className)
                    end
                    return ""
                end)
            },
            
            -- 等級和經驗
            New "TextLabel" {
                Name = "LevelExp",
                Size = UDim2.new(1, -10, 0, 25),
                Position = UDim2.new(0, 5, 0, 65),
                BackgroundTransparency = 1,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSans,
                TextXAlignment = Enum.TextXAlignment.Left,
                
                Text = Computed(function()
                    local stats = self.heroStats:get()
                    if stats then
                        return string.format("等級 %d (經驗: %d/%d)", 
                              stats.basicInfo.level, 
                              stats.basicInfo.experience,
                              stats.basicInfo.nextLevelExp)
                    end
                    return ""
                end)
            },
            
            -- 血量條
            self:CreateHealthBar(),
            
            -- 魔力條
            self:CreateManaBar()
        }
    }
end

-- 創建血量條
function HeroController:CreateHealthBar()
    return New "Frame" {
        Name = "HealthBar",
        Size = UDim2.new(1, -10, 0, 20),
        Position = UDim2.new(0, 5, 0, 95),
        BackgroundColor3 = Color3.fromRGB(100, 20, 20),
        BorderSizePixel = 0,
        
        [Fusion.Children] = {
            New "Frame" {
                Name = "HealthFill",
                Size = Computed(function()
                    local stats = self.heroStats:get()
                    if stats and stats.health.max > 0 then
                        local percentage = stats.health.current / stats.health.max
                        return UDim2.new(percentage, 0, 1, 0)
                    end
                    return UDim2.new(0, 0, 1, 0)
                end),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(200, 50, 50),
                BorderSizePixel = 0
            },
            
            New "TextLabel" {
                Name = "HealthText",
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundTransparency = 1,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSans,
                
                Text = Computed(function()
                    local stats = self.heroStats:get()
                    if stats then
                        return string.format("血量: %d/%d", stats.health.current, stats.health.max)
                    end
                    return "血量: 0/0"
                end)
            }
        }
    }
end

-- 創建魔力條
function HeroController:CreateManaBar()
    return New "Frame" {
        Name = "ManaBar",
        Size = UDim2.new(1, -10, 0, 20),
        Position = UDim2.new(0, 5, 0, 120),
        BackgroundColor3 = Color3.fromRGB(20, 20, 100),
        BorderSizePixel = 0,
        
        [Fusion.Children] = {
            New "Frame" {
                Name = "ManaFill",
                Size = Computed(function()
                    local stats = self.heroStats:get()
                    if stats and stats.mana.max > 0 then
                        local percentage = stats.mana.current / stats.mana.max
                        return UDim2.new(percentage, 0, 1, 0)
                    end
                    return UDim2.new(0, 0, 1, 0)
                end),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(50, 50, 200),
                BorderSizePixel = 0
            },
            
            New "TextLabel" {
                Name = "ManaText",
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundTransparency = 1,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSans,
                
                Text = Computed(function()
                    local stats = self.heroStats:get()
                    if stats then
                        return string.format("魔力: %d/%d", stats.mana.current, stats.mana.max)
                    end
                    return "魔力: 0/0"
                end)
            }
        }
    }
end

-- 創建技能面板
function HeroController:CreateSkillPanel()
    return New "Frame" {
        Name = "SkillPanel",
        Size = UDim2.new(0, 200, 0, 150),
        Position = UDim2.new(0, 320, 0, 10),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        
        Visible = Computed(function()
            return self.heroData:get() ~= nil and not self.isLoading:get()
        end),
        
        [Fusion.Children] = {
            -- 標題
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 30),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "技能",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            },
            
            -- 技能按鈕
            self:CreateSkillButtons()
        }
    }
end

-- 創建技能按鈕
function HeroController:CreateSkillButtons()
    return New "Frame" {
        Name = "SkillButtons",
        Size = UDim2.new(1, 0, 1, -30),
        Position = UDim2.new(0, 0, 0, 30),
        BackgroundTransparency = 1,
        
        [Fusion.Children] = Computed(function()
            local heroData = self.heroData:get()
            if not heroData or not heroData.skills then
                return {}
            end
            
            local buttons = {}
            for i, skill in ipairs(heroData.skills) do
                buttons[skill] = New "TextButton" {
                    Name = skill,
                    Size = UDim2.new(0.45, 0, 0.4, 0),
                    Position = UDim2.new(
                        ((i - 1) % 2) * 0.5 + 0.025,
                        0,
                        math.floor((i - 1) / 2) * 0.5 + 0.05,
                        0
                    ),
                    BackgroundColor3 = Color3.fromRGB(80, 80, 80),
                    BorderSizePixel = 0,
                    Text = skill,
                    TextColor3 = Color3.fromRGB(255, 255, 255),
                    TextScaled = true,
                    Font = Enum.Font.SourceSans,
                    
                    [Fusion.OnEvent "Activated"] = function()
                        self:UseSkill(skill, self.selectedTarget:get())
                    end
                }
            end
            
            return buttons
        end)
    }
end

-- 創建狀態面板
function HeroController:CreateStatusPanel()
    return New "Frame" {
        Name = "StatusPanel",
        Size = UDim2.new(0, 150, 0, 100),
        Position = UDim2.new(0, 530, 0, 10),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        
        Visible = Computed(function()
            return self.heroData:get() ~= nil and not self.isLoading:get()
        end),
        
        [Fusion.Children] = {
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 25),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "狀態",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            },
            
            New "TextLabel" {
                Name = "Status",
                Size = UDim2.new(1, -10, 1, -30),
                Position = UDim2.new(0, 5, 0, 30),
                BackgroundTransparency = 1,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSans,
                TextXAlignment = Enum.TextXAlignment.Left,
                TextYAlignment = Enum.TextYAlignment.Top,
                
                Text = Computed(function()
                    local stats = self.heroStats:get()
                    if stats then
                        local statusText = string.format("狀態: %s\n戰鬥: %s", 
                              stats.status.baseState, stats.status.combatState)
                        
                        if stats.status.baseState == "dead" then
                            statusText = statusText .. "\n\n[復活]"
                        end
                        
                        return statusText
                    end
                    return ""
                end)
            }
        }
    }
end

-- 創建通知面板
function HeroController:CreateNotificationPanel()
    return New "Frame" {
        Name = "NotificationPanel",
        Size = UDim2.new(0, 300, 0, 50),
        Position = UDim2.new(0.5, -150, 0, 100),
        BackgroundTransparency = 1,
        
        [Fusion.Children] = {
            New "TextLabel" {
                Name = "NotificationText",
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundColor3 = Color3.fromRGB(0, 0, 0),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold,
                Visible = false
            }
        }
    }
end

-- 顯示創建英雄 UI
function HeroController:ShowCreateHeroUI()
    -- 簡單的創建界面
    local createUI = New "Frame" {
        Name = "CreateHeroUI",
        Size = UDim2.new(0, 400, 0, 300),
        Position = UDim2.new(0.5, -200, 0.5, -150),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        Parent = self.mainUI,
        
        [Fusion.Children] = {
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 50),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "創建英雄",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            }
        }
    }
    
    -- 添加職業選擇按鈕
    local yPos = 60
    for className, classConfig in pairs(HeroConfig.CLASSES) do
        New "TextButton" {
            Name = className,
            Size = UDim2.new(0.8, 0, 0, 40),
            Position = UDim2.new(0.1, 0, 0, yPos),
            BackgroundColor3 = Color3.fromRGB(80, 80, 80),
            BorderSizePixel = 0,
            Text = classConfig.name .. " - " .. classConfig.description,
            TextColor3 = Color3.fromRGB(255, 255, 255),
            TextScaled = true,
            Font = Enum.Font.SourceSans,
            Parent = createUI,
            
            [Fusion.OnEvent "Activated"] = function()
                self:CreateHero(className)
            end
        }
        
        yPos = yPos + 50
    end
end

-- 隱藏創建英雄 UI
function HeroController:HideCreateHeroUI()
    local createUI = self.mainUI:FindFirstChild("CreateHeroUI")
    if createUI then
        createUI:Destroy()
    end
end

-- 顯示通知
function HeroController:ShowNotification(message, type)
    local notificationPanel = self.mainUI:FindFirstChild("NotificationPanel")
    if notificationPanel then
        local textLabel = notificationPanel:FindFirstChild("NotificationText")
        if textLabel then
            textLabel.Text = message
            textLabel.Visible = true
            
            -- 根據類型設置顏色
            if type == "success" then
                textLabel.BackgroundColor3 = Color3.fromRGB(0, 100, 0)
            elseif type == "error" then
                textLabel.BackgroundColor3 = Color3.fromRGB(100, 0, 0)
            else
                textLabel.BackgroundColor3 = Color3.fromRGB(0, 0, 100)
            end
            
            -- 3秒後隱藏
            task.spawn(function()
                task.wait(3)
                textLabel.Visible = false
            end)
        end
    end
end

-- 事件處理函數
function HeroController:OnHeroLevelUp(data)
    self:ShowNotification(string.format("升級到 %d 級！", data.newLevel), "success")
    self:RefreshHeroStats()
end

function HeroController:OnHeroDied(data)
    self:ShowNotification(string.format("你死了！失去 %d 經驗值", data.expLoss), "error")
    self:RefreshHeroStats()
end

function HeroController:OnHeroResurrected(data)
    self:ShowNotification("你復活了！", "success")
    self:RefreshHeroStats()
end

function HeroController:OnHeroGainedExp(data)
    self:ShowNotification(string.format("獲得 %d 經驗值", data.expGained), "info")
    self:RefreshHeroStats()
end

function HeroController:OnHeroDamaged(data)
    if data.attacker then
        self:ShowNotification(string.format("受到 %d 傷害", data.damage), "error")
    end
    self:RefreshHeroStats()
end

function HeroController:OnHeroHealed(data)
    if data.healer then
        self:ShowNotification(string.format("恢復 %d 血量", data.healAmount), "success")
    end
    self:RefreshHeroStats()
end

function HeroController:OnCriticalHit(data)
    self:ShowNotification(string.format("暴擊！造成 %d 傷害", data.damage), "success")
end

return HeroController
