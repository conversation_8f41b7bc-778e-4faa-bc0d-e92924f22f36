-- 玩家管理服務
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local ProfileService = require(game.ReplicatedStorage.Packages.ProfileService)
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local PlayerService = Knit.CreateService({
    Name = "PlayerService",
    Client = {
        -- 客戶端可調用的方法
        GetPlayerData = Knit.CreateSignal(),
        UpdatePlayerData = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnDataLoaded = Knit.CreateSignal(),
        OnDataUpdated = Knit.CreateSignal(),
        OnLevelUp = Knit.CreateSignal()
    }
})

-- ProfileService 配置
local ProfileTemplate = {
    -- 版本控制
    dataVersion = 1,
    
    -- 英雄數據
    hero = {
        level = 1,
        experience = 0,
        maxHealth = GameConfig.HERO.BASE_HEALTH,
        currentHealth = GameConfig.HERO.BASE_HEALTH,
        attack = GameConfig.HERO.BASE_ATTACK,
        defense = GameConfig.HERO.BASE_DEFENSE,
        critRate = GameConfig.HERO.BASE_CRIT_RATE,
        critDamage = GameConfig.HERO.BASE_CRIT_DAMAGE
    },
    
    -- 貨幣數據
    currencies = {
        coins = GameConfig.ECONOMY.STARTING_COINS,
        gems = GameConfig.ECONOMY.STARTING_GEMS,
        robux = GameConfig.ECONOMY.STARTING_ROBUX,
        experience = 0
    },
    
    -- 寵物數據
    pets = {
        active = {}, -- 當前攜帶的寵物 (最多5隻)
        collection = {}, -- 所有擁有的寵物
        nextId = 1
    },
    
    -- 裝備數據
    equipment = {
        weapon = nil,
        armor = nil,
        accessory = nil,
        inventory = {}
    },
    
    -- 任務數據
    quests = {
        daily = {},
        weekly = {},
        monthly = {},
        dailyResetTime = 0,
        weeklyResetTime = 0,
        monthlyResetTime = 0,
        activePoints = 0
    },
    
    -- VIP數據
    vip = {
        level = 0,
        totalSpent = 0,
        premiumExpiry = 0,
        premiumActive = false,
        lastRewardClaim = 0
    },
    
    -- 登入獎勵數據
    loginRewards = {
        currentDay = 1,
        lastClaimTime = 0,
        claimedDays = {},
        missedDays = {}
    },
    
    -- 進度數據
    progress = {
        unlockedZones = {"castle"},
        completedQuests = {},
        achievements = {},
        encounterHistory = {},
        pokedex = {
            encountered = {},
            owned = {}
        }
    },
    
    -- 社交數據
    social = {
        friends = {},
        guildId = nil,
        lastLogin = 0,
        loginStreak = 0
    },
    
    -- 設置數據
    settings = {
        soundEnabled = true,
        musicEnabled = true,
        notificationsEnabled = true,
        language = "zh-TW"
    }
}

local ProfileStore = ProfileService.GetProfileStore("PlayerData_v1", ProfileTemplate)
local profiles = {} -- 存儲活躍的玩家檔案

function PlayerService:KnitInit()
    self._world = _G.ECSWorld
    print("📊 PlayerService 初始化完成")
end

function PlayerService:KnitStart()
    print("🎮 PlayerService 啟動")
    
    -- 監聽玩家加入
    game.Players.PlayerAdded:Connect(function(player)
        self:_loadPlayerProfile(player)
    end)
    
    -- 監聽玩家離開
    game.Players.PlayerRemoving:Connect(function(player)
        self:_saveAndReleaseProfile(player)
    end)
end

-- 載入玩家檔案
function PlayerService:_loadPlayerProfile(player)
    local profileKey = "Player_" .. player.UserId
    local profile = ProfileStore:LoadProfileAsync(profileKey)
    
    if profile then
        profile:AddUserId(player.UserId)
        profile:Reconcile()
        
        if player.Parent == game.Players then
            profiles[player] = profile
            
            -- 更新 ECS 實體數據
            self:_syncProfileToECS(player, profile.Data)
            
            -- 通知客戶端數據已載入
            self.Client.OnDataLoaded:Fire(player, profile.Data)
            
            print("✅ 玩家數據載入成功:", player.Name)
        else
            profile:Release()
        end
    else
        -- 數據載入失敗
        warn("❌ 玩家數據載入失敗:", player.Name)
        player:Kick("數據載入失敗，請重新加入遊戲")
    end
end

-- 保存並釋放玩家檔案
function PlayerService:_saveAndReleaseProfile(player)
    local profile = profiles[player]
    if profile then
        -- 從 ECS 同步數據到檔案
        self:_syncECSToProfile(player, profile.Data)
        
        profile:Release()
        profiles[player] = nil
        
        print("💾 玩家數據已保存:", player.Name)
    end
end

-- 將檔案數據同步到 ECS 實體
function PlayerService:_syncProfileToECS(player, data)
    local entityId = player:GetAttribute("EntityId")
    if not entityId or not self._world:contains(entityId) then
        return
    end
    
    -- 更新英雄組件
    self._world:insert(entityId, Components.HeroComponent(data.hero))
    
    -- 更新貨幣組件
    self._world:insert(entityId, Components.CurrencyComponent(data.currencies))
    
    -- 更新VIP組件
    self._world:insert(entityId, Components.VIPComponent(data.vip))
    
    -- 更新區域組件
    self._world:insert(entityId, Components.ZoneComponent({
        currentZone = data.progress.unlockedZones[#data.progress.unlockedZones] or "castle",
        lastZoneChange = tick(),
        allowedZones = data.progress.unlockedZones
    }))
    
    -- 更新圖鑑組件
    self._world:insert(entityId, Components.PokedexComponent({
        encounteredSpecies = data.progress.pokedex.encountered,
        ownedSpecies = data.progress.pokedex.owned,
        completionRate = self:_calculatePokedexCompletion(data.progress.pokedex)
    }))
end

-- 將 ECS 實體數據同步到檔案
function PlayerService:_syncECSToProfile(player, data)
    local entityId = player:GetAttribute("EntityId")
    if not entityId or not self._world:contains(entityId) then
        return
    end
    
    -- 同步英雄數據
    local heroComponent = self._world:get(entityId, Components.HeroComponent)
    if heroComponent then
        data.hero = Utils.deepCopy(heroComponent)
    end
    
    -- 同步貨幣數據
    local currencyComponent = self._world:get(entityId, Components.CurrencyComponent)
    if currencyComponent then
        data.currencies = Utils.deepCopy(currencyComponent)
    end
    
    -- 同步VIP數據
    local vipComponent = self._world:get(entityId, Components.VIPComponent)
    if vipComponent then
        data.vip = Utils.deepCopy(vipComponent)
    end
    
    -- 同步圖鑑數據
    local pokedexComponent = self._world:get(entityId, Components.PokedexComponent)
    if pokedexComponent then
        data.progress.pokedex.encountered = Utils.deepCopy(pokedexComponent.encounteredSpecies)
        data.progress.pokedex.owned = Utils.deepCopy(pokedexComponent.ownedSpecies)
    end
    
    -- 更新最後登入時間
    data.social.lastLogin = tick()
end

-- 計算圖鑑完成度
function PlayerService:_calculatePokedexCompletion(pokedex)
    local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)
    local totalSpecies = 0
    local ownedSpecies = 0
    
    for _ in pairs(PetConfig.Species) do
        totalSpecies = totalSpecies + 1
    end
    
    for _ in pairs(pokedex.owned) do
        ownedSpecies = ownedSpecies + 1
    end
    
    return totalSpecies > 0 and (ownedSpecies / totalSpecies) or 0
end

-- 客戶端方法：獲取玩家數據
function PlayerService.Client:GetPlayerData(player)
    local profile = profiles[player]
    if profile then
        return profile.Data
    end
    return nil
end

-- 獲取玩家檔案
function PlayerService:GetPlayerProfile(player)
    return profiles[player]
end

-- 獲取玩家實體ID
function PlayerService:GetPlayerEntity(player)
    return player:GetAttribute("EntityId")
end

-- 更新玩家數據並通知客戶端
function PlayerService:UpdatePlayerData(player, updateData)
    local profile = profiles[player]
    if not profile then
        return false
    end
    
    -- 更新檔案數據
    for key, value in pairs(updateData) do
        if profile.Data[key] then
            profile.Data[key] = Utils.mergeTables(profile.Data[key], value)
        end
    end
    
    -- 同步到 ECS
    self:_syncProfileToECS(player, profile.Data)
    
    -- 通知客戶端
    self.Client.OnDataUpdated:Fire(player, updateData)
    
    return true
end

return PlayerService
