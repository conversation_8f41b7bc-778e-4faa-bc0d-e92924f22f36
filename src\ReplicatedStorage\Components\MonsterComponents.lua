-- 怪物系統ECS組件
-- 實現怪物地圖生成、遭遇檢測、圖鑑記錄等功能

local Matter = require(game.ReplicatedStorage.Packages.Matter)

local MonsterComponents = {}

-- 怪物實體組件
MonsterComponents.Monster = Matter.component("Monster", {
    speciesId = "", -- 物種ID (如 "Cat", "Bear")
    name = "", -- 顯示名稱
    rarity = "", -- 稀有度 (COMMON, RARE, EPIC, MYTHIC, LEGENDARY)
    level = 1, -- 等級
    zone = "", -- 所屬區域 (FOREST, ICE, LAVA)
    isWild = true, -- 是否為野生怪物
    spawnTime = 0, -- 生成時間
    lastSeenTime = 0 -- 最後被看見時間
})

-- 怪物統計組件
MonsterComponents.MonsterStats = Matter.component("MonsterStats", {
    health = 100,
    maxHealth = 100,
    attack = 10,
    defense = 5,
    speed = 10,
    critRate = 0.05,
    critDamage = 1.5,
    experience = 0 -- 擊敗獲得的經驗值
})

-- 怪物位置組件
MonsterComponents.MonsterPosition = Matter.component("MonsterPosition", {
    x = 0,
    y = 0,
    z = 0,
    rotation = 0,
    zone = "", -- 所在區域
    isMoving = false,
    targetPosition = nil -- 移動目標位置
})

-- 怪物視覺組件
MonsterComponents.MonsterVisual = Matter.component("MonsterVisual", {
    modelName = "", -- 模型名稱
    instance = nil, -- Roblox實例
    material = "SmoothPlastic", -- 材質
    primaryColor = Color3.new(1, 1, 1), -- 主要顏色
    secondaryColor = Color3.new(0.8, 0.8, 0.8), -- 次要顏色
    hasNeonParts = false, -- 是否有霓虹部件
    hasParticles = false, -- 是否有粒子效果
    scale = 1.0 -- 縮放比例
})

-- 怪物AI組件
MonsterComponents.MonsterAI = Matter.component("MonsterAI", {
    state = "idle", -- 狀態: idle, patrol, chase, attack, flee
    detectionRange = 50, -- 檢測範圍
    attackRange = 10, -- 攻擊範圍
    patrolRadius = 30, -- 巡邏半徑
    lastActionTime = 0, -- 最後行動時間
    target = nil, -- 目標玩家
    homePosition = nil -- 家位置
})

-- 怪物生成器組件
MonsterComponents.MonsterSpawner = Matter.component("MonsterSpawner", {
    zone = "", -- 生成區域
    position = Vector3.new(0, 0, 0), -- 生成位置
    radius = 100, -- 生成半徑
    maxMonsters = 10, -- 最大怪物數量
    spawnInterval = 30, -- 生成間隔(秒)
    lastSpawnTime = 0, -- 最後生成時間
    activeMonsters = {}, -- 活躍怪物列表
    spawnRates = { -- 生成機率
        COMMON = 0.7,
        RARE = 0.2,
        EPIC = 0.08,
        MYTHIC = 0.02,
        LEGENDARY = 0.001
    }
})

-- 遭遇記錄組件
MonsterComponents.EncounterRecord = Matter.component("EncounterRecord", {
    playerId = 0, -- 玩家ID
    speciesId = "", -- 物種ID
    firstEncounterTime = 0, -- 首次遭遇時間
    totalEncounters = 0, -- 總遭遇次數
    totalDefeated = 0, -- 總擊敗次數
    lastEncounterTime = 0, -- 最後遭遇時間
    isUnlocked = false -- 是否已解鎖圖鑑
})

-- 圖鑑條目組件
MonsterComponents.PokedexEntry = Matter.component("PokedexEntry", {
    playerId = 0, -- 玩家ID
    speciesId = "", -- 物種ID
    isDiscovered = false, -- 是否已發現
    isOwned = false, -- 是否已擁有(通過扭蛋獲得)
    discoveryTime = 0, -- 發現時間
    ownedTime = 0, -- 擁有時間
    encounterCount = 0, -- 遭遇次數
    defeatCount = 0, -- 擊敗次數
    notes = "" -- 備註
})

-- 怪物戰鬥組件
MonsterComponents.MonsterCombat = Matter.component("MonsterCombat", {
    isInCombat = false, -- 是否在戰鬥中
    combatTarget = nil, -- 戰鬥目標
    combatStartTime = 0, -- 戰鬥開始時間
    lastAttackTime = 0, -- 最後攻擊時間
    attackCooldown = 2.0, -- 攻擊冷卻時間
    damageDealt = 0, -- 造成的傷害
    damageTaken = 0, -- 受到的傷害
    isStunned = false, -- 是否被眩暈
    stunEndTime = 0 -- 眩暈結束時間
})

-- 怪物掉落組件
MonsterComponents.MonsterLoot = Matter.component("MonsterLoot", {
    experienceReward = 10, -- 經驗值獎勵
    coinReward = 5, -- 金幣獎勵
    gemReward = 0, -- 寶石獎勵
    dropTable = {}, -- 掉落表
    guaranteedDrops = {}, -- 保證掉落
    rareDrops = {}, -- 稀有掉落
    dropChance = 0.1 -- 掉落機率
})

-- 怪物效果組件
MonsterComponents.MonsterEffects = Matter.component("MonsterEffects", {
    activeEffects = {}, -- 活躍效果列表
    immunities = {}, -- 免疫效果
    resistances = {}, -- 抗性效果
    weaknesses = {}, -- 弱點效果
    buffs = {}, -- 增益效果
    debuffs = {} -- 減益效果
})

-- 怪物群組組件
MonsterComponents.MonsterGroup = Matter.component("MonsterGroup", {
    groupId = "", -- 群組ID
    leaderId = nil, -- 領導者ID
    members = {}, -- 成員列表
    formation = "circle", -- 隊形: circle, line, triangle
    groupBehavior = "follow", -- 群組行為: follow, scatter, surround
    maxDistance = 20 -- 最大距離
})

-- 怪物事件組件
MonsterComponents.MonsterEvents = Matter.component("MonsterEvents", {
    onSpawn = nil, -- 生成事件
    onDeath = nil, -- 死亡事件
    onEncounter = nil, -- 遭遇事件
    onAttack = nil, -- 攻擊事件
    onDefeat = nil, -- 被擊敗事件
    onEscape = nil -- 逃跑事件
})

return MonsterComponents
