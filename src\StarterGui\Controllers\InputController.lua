-- 輸入控制器 - 處理玩家輸入和控制
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local UserInputService = game:GetService("UserInputService")
local Players = game:GetService("Players")

local InputController = Knit.CreateController({
    Name = "InputController"
})

function InputController:KnitStart()
    print("🎮 InputController 啟動")
    
    -- 設置輸入處理
    self:setupInputHandlers()
    
    -- 設置移動控制
    self:setupMovementControls()
    
    -- 設置戰鬥控制
    self:setupCombatControls()
    
    -- 設置UI控制
    self:setupUIControls()
end

-- 設置輸入處理器
function InputController:setupInputHandlers()
    -- 鍵盤輸入
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        self:handleInputBegan(input)
    end)
    
    UserInputService.InputEnded:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        self:handleInputEnded(input)
    end)
    
    -- 滑鼠輸入
    UserInputService.InputChanged:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        self:handleInputChanged(input)
    end)
end

-- 處理輸入開始
function InputController:handleInputBegan(input)
    local inputType = input.KeyCode or input.UserInputType
    
    -- 移動控制
    if inputType == Enum.KeyCode.W then
        self:startMovement("forward")
    elseif inputType == Enum.KeyCode.S then
        self:startMovement("backward")
    elseif inputType == Enum.KeyCode.A then
        self:startMovement("left")
    elseif inputType == Enum.KeyCode.D then
        self:startMovement("right")
    
    -- 戰鬥控制
    elseif inputType == Enum.KeyCode.Space then
        self:performAttack()
    elseif inputType == Enum.KeyCode.R then
        self:startCombatWithNearestMonster()
    elseif inputType == Enum.KeyCode.Escape then
        self:endCombat()
    
    -- UI控制
    elseif inputType == Enum.KeyCode.I then
        self:toggleInventory()
    elseif inputType == Enum.KeyCode.P then
        self:togglePetMenu()
    elseif inputType == Enum.KeyCode.Q then
        self:toggleQuestMenu()
    elseif inputType == Enum.KeyCode.M then
        self:toggleMap()
    
    -- 快捷鍵
    elseif inputType == Enum.KeyCode.One then
        self:useQuickSlot(1)
    elseif inputType == Enum.KeyCode.Two then
        self:useQuickSlot(2)
    elseif inputType == Enum.KeyCode.Three then
        self:useQuickSlot(3)
    elseif inputType == Enum.KeyCode.Four then
        self:useQuickSlot(4)
    elseif inputType == Enum.KeyCode.Five then
        self:useQuickSlot(5)
    
    -- 滑鼠點擊
    elseif inputType == Enum.UserInputType.MouseButton1 then
        self:handleLeftClick(input)
    elseif inputType == Enum.UserInputType.MouseButton2 then
        self:handleRightClick(input)
    end
end

-- 處理輸入結束
function InputController:handleInputEnded(input)
    local inputType = input.KeyCode or input.UserInputType
    
    -- 停止移動
    if inputType == Enum.KeyCode.W or 
       inputType == Enum.KeyCode.S or 
       inputType == Enum.KeyCode.A or 
       inputType == Enum.KeyCode.D then
        self:stopMovement()
    end
end

-- 處理輸入變化
function InputController:handleInputChanged(input)
    -- 滑鼠移動
    if input.UserInputType == Enum.UserInputType.MouseMovement then
        self:handleMouseMovement(input)
    end
end

-- 設置移動控制
function InputController:setupMovementControls()
    self.movementState = {
        forward = false,
        backward = false,
        left = false,
        right = false
    }
end

-- 開始移動
function InputController:startMovement(direction)
    self.movementState[direction] = true
    self:updateMovement()
end

-- 停止移動
function InputController:stopMovement()
    for direction, _ in pairs(self.movementState) do
        self.movementState[direction] = false
    end
    self:updateMovement()
end

-- 更新移動
function InputController:updateMovement()
    local player = Players.LocalPlayer
    local character = player.Character
    if not character then return end
    
    local humanoid = character:FindFirstChild("Humanoid")
    if not humanoid then return end
    
    -- 計算移動方向
    local moveVector = Vector3.new(0, 0, 0)
    
    if self.movementState.forward then
        moveVector = moveVector + Vector3.new(0, 0, -1)
    end
    if self.movementState.backward then
        moveVector = moveVector + Vector3.new(0, 0, 1)
    end
    if self.movementState.left then
        moveVector = moveVector + Vector3.new(-1, 0, 0)
    end
    if self.movementState.right then
        moveVector = moveVector + Vector3.new(1, 0, 0)
    end
    
    -- 正規化移動向量
    if moveVector.Magnitude > 0 then
        moveVector = moveVector.Unit
        humanoid:Move(moveVector)
    else
        humanoid:Move(Vector3.new(0, 0, 0))
    end
end

-- 設置戰鬥控制
function InputController:setupCombatControls()
    self.combatState = {
        inCombat = false,
        target = nil,
        lastAttackTime = 0
    }
end

-- 執行攻擊
function InputController:performAttack()
    local currentTime = tick()
    
    -- 檢查攻擊冷卻
    if currentTime - self.combatState.lastAttackTime < 1 then -- 1秒冷卻
        return
    end
    
    -- 獲取戰鬥服務
    local CombatService = Knit.GetService("CombatService")
    if not CombatService then
        return
    end
    
    -- 發送攻擊請求
    CombatService:AttackMonster():andThen(function(result)
        if result.success then
            self.combatState.lastAttackTime = currentTime
            print("⚔️ 攻擊成功")
        else
            print("❌ 攻擊失敗:", result.error)
        end
    end):catch(function(err)
        warn("攻擊錯誤:", err)
    end)
end

-- 與最近的怪物開始戰鬥
function InputController:startCombatWithNearestMonster()
    -- 這裡需要實現尋找最近怪物的邏輯
    -- 暫時使用模擬的怪物ID
    local mockMonsterId = 12345
    
    local CombatService = Knit.GetService("CombatService")
    if not CombatService then
        return
    end
    
    CombatService:StartCombat(mockMonsterId):andThen(function(result)
        if result.success then
            self.combatState.inCombat = true
            self.combatState.target = result.monsterId
            print("⚔️ 戰鬥開始")
        else
            print("❌ 無法開始戰鬥:", result.error)
        end
    end):catch(function(err)
        warn("戰鬥開始錯誤:", err)
    end)
end

-- 結束戰鬥
function InputController:endCombat()
    if not self.combatState.inCombat then
        return
    end
    
    local CombatService = Knit.GetService("CombatService")
    if not CombatService then
        return
    end
    
    CombatService:EndCombat():andThen(function(result)
        if result.success then
            self.combatState.inCombat = false
            self.combatState.target = nil
            print("🏃 戰鬥結束")
        end
    end):catch(function(err)
        warn("結束戰鬥錯誤:", err)
    end)
end

-- 設置UI控制
function InputController:setupUIControls()
    self.uiState = {
        inventoryOpen = false,
        petMenuOpen = false,
        questMenuOpen = false,
        mapOpen = false
    }
end

-- 切換背包
function InputController:toggleInventory()
    self.uiState.inventoryOpen = not self.uiState.inventoryOpen
    
    local UIController = Knit.GetController("UIController")
    if UIController then
        UIController:toggleInventoryUI(self.uiState.inventoryOpen)
    end
    
    print("🎒 背包", self.uiState.inventoryOpen and "開啟" or "關閉")
end

-- 切換寵物選單
function InputController:togglePetMenu()
    self.uiState.petMenuOpen = not self.uiState.petMenuOpen
    
    local UIController = Knit.GetController("UIController")
    if UIController then
        UIController:togglePetMenuUI(self.uiState.petMenuOpen)
    end
    
    print("🐾 寵物選單", self.uiState.petMenuOpen and "開啟" or "關閉")
end

-- 切換任務選單
function InputController:toggleQuestMenu()
    self.uiState.questMenuOpen = not self.uiState.questMenuOpen
    
    local UIController = Knit.GetController("UIController")
    if UIController then
        UIController:toggleQuestMenuUI(self.uiState.questMenuOpen)
    end
    
    print("📋 任務選單", self.uiState.questMenuOpen and "開啟" or "關閉")
end

-- 切換地圖
function InputController:toggleMap()
    self.uiState.mapOpen = not self.uiState.mapOpen
    
    local UIController = Knit.GetController("UIController")
    if UIController then
        UIController:toggleMapUI(self.uiState.mapOpen)
    end
    
    print("🗺️ 地圖", self.uiState.mapOpen and "開啟" or "關閉")
end

-- 使用快捷欄
function InputController:useQuickSlot(slotNumber)
    print("⚡ 使用快捷欄", slotNumber)
    
    -- 這裡可以添加快捷欄邏輯
    -- 例如：使用藥水、召喚寵物等
end

-- 處理左鍵點擊
function InputController:handleLeftClick(input)
    -- 獲取滑鼠位置
    local mouse = Players.LocalPlayer:GetMouse()
    local target = mouse.Target
    
    if target then
        print("🖱️ 左鍵點擊:", target.Name)
        
        -- 檢查是否點擊了怪物
        if target.Parent and target.Parent:FindFirstChild("Humanoid") then
            -- 可能是怪物，嘗試開始戰鬥
            self:startCombatWithNearestMonster()
        end
    end
end

-- 處理右鍵點擊
function InputController:handleRightClick(input)
    local mouse = Players.LocalPlayer:GetMouse()
    local target = mouse.Target
    
    if target then
        print("🖱️ 右鍵點擊:", target.Name)
        
        -- 這裡可以添加右鍵選單邏輯
    end
end

-- 處理滑鼠移動
function InputController:handleMouseMovement(input)
    -- 這裡可以添加滑鼠移動邏輯
    -- 例如：攝影機控制、UI hover效果等
end

-- 獲取輸入狀態
function InputController:getMovementState()
    return self.movementState
end

function InputController:getCombatState()
    return self.combatState
end

function InputController:getUIState()
    return self.uiState
end

-- 設置輸入啟用/禁用
function InputController:setInputEnabled(enabled)
    self.inputEnabled = enabled
    
    if not enabled then
        -- 停止所有移動
        self:stopMovement()
    end
end

return InputController
