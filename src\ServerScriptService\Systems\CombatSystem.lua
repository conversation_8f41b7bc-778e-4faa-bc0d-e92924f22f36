-- 戰鬥系統 - 處理玩家與怪物的戰鬥邏輯
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local CombatSystem = {}

function CombatSystem:step(world, state)
    -- 處理戰鬥狀態的實體
    for entityId, combat, health, hero in world:query(
        Components.CombatComponent,
        Components.HealthComponent,
        Components.HeroComponent
    ) do
        if combat.isInCombat and combat.target then
            self:processCombat(world, entityId, combat, health, hero, state)
        end
        
        -- 檢查戰鬥超時 (30秒自動結束)
        if combat.isInCombat and state.currentTime - combat.combatStartTime > 30 then
            self:endCombat(world, entityId, "timeout")
        end
    end
    
    -- 處理怪物戰鬥邏輯
    for entityId, monster, health, combat in world:query(
        Components.MonsterComponent,
        Components.HealthComponent,
        Components.CombatComponent
    ) do
        if combat.isInCombat and combat.target then
            self:processMonsterCombat(world, entityId, monster, health, combat, state)
        end
    end
end

function CombatSystem:processCombat(world, entityId, combat, health, hero, state)
    -- 檢查攻擊冷卻時間
    local attackCooldown = GameConfig.COMBAT.TURN_DURATION
    if state.currentTime - combat.lastAttackTime < attackCooldown then
        return
    end
    
    -- 獲取目標
    local target = combat.target
    if not world:contains(target) then
        self:endCombat(world, entityId, "target_lost")
        return
    end
    
    local targetHealth = world:get(target, Components.HealthComponent)
    local targetMonster = world:get(target, Components.MonsterComponent)
    
    if not targetHealth or not targetMonster then
        self:endCombat(world, entityId, "invalid_target")
        return
    end
    
    -- 計算傷害
    local damage = self:calculateDamage(hero, targetHealth)
    local newTargetHealth = math.max(0, targetHealth.current - damage)
    
    -- 更新目標血量
    world:insert(target, Components.HealthComponent({
        current = newTargetHealth,
        maximum = targetHealth.maximum,
        regeneration = targetHealth.regeneration,
        lastRegenTime = targetHealth.lastRegenTime
    }))
    
    -- 更新攻擊時間
    world:insert(entityId, Components.CombatComponent({
        isInCombat = combat.isInCombat,
        target = combat.target,
        lastAttackTime = state.currentTime,
        combatStartTime = combat.combatStartTime
    }))
    
    print("🗡️ 玩家攻擊怪物，造成傷害:", damage, "剩餘血量:", newTargetHealth)
    
    -- 檢查目標是否死亡
    if newTargetHealth <= 0 then
        self:onMonsterDefeated(world, entityId, target, targetMonster)
    end
end

function CombatSystem:processMonsterCombat(world, entityId, monster, health, combat, state)
    -- 怪物反擊邏輯
    local attackCooldown = GameConfig.COMBAT.TURN_DURATION * 1.5 -- 怪物攻擊稍慢
    if state.currentTime - combat.lastAttackTime < attackCooldown then
        return
    end
    
    local target = combat.target
    if not world:contains(target) then
        self:endMonsterCombat(world, entityId)
        return
    end
    
    local targetHealth = world:get(target, Components.HealthComponent)
    local targetHero = world:get(target, Components.HeroComponent)
    
    if not targetHealth or not targetHero then
        self:endMonsterCombat(world, entityId)
        return
    end
    
    -- 計算怪物傷害 (基於怪物等級)
    local monsterLevel = self:getMonsterLevel(monster)
    local monsterAttack = 10 + (monsterLevel * 2)
    local damage = self:calculateMonsterDamage(monsterAttack, targetHero)
    
    local newTargetHealth = math.max(0, targetHealth.current - damage)
    
    -- 更新玩家血量
    world:insert(target, Components.HealthComponent({
        current = newTargetHealth,
        maximum = targetHealth.maximum,
        regeneration = targetHealth.regeneration,
        lastRegenTime = targetHealth.lastRegenTime
    }))
    
    -- 更新怪物攻擊時間
    world:insert(entityId, Components.CombatComponent({
        isInCombat = combat.isInCombat,
        target = combat.target,
        lastAttackTime = state.currentTime,
        combatStartTime = combat.combatStartTime
    }))
    
    print("👹 怪物反擊，造成傷害:", damage, "玩家剩餘血量:", newTargetHealth)
    
    -- 檢查玩家是否死亡
    if newTargetHealth <= 0 then
        self:onPlayerDefeated(world, target)
    end
end

function CombatSystem:calculateDamage(attacker, defender)
    local baseDamage = attacker.attack or 15
    local defense = defender.current and (defender.current * 0.1) or 0 -- 血量影響防禦
    
    -- 基礎傷害計算
    local damage = math.max(1, baseDamage - defense)
    
    -- 暴擊計算
    if math.random() < (attacker.critRate or 0.05) then
        damage = damage * (attacker.critDamage or 1.5)
        print("💥 暴擊！")
    end
    
    -- 隨機浮動 ±20%
    local variance = 0.8 + math.random() * 0.4
    return math.floor(damage * variance)
end

function CombatSystem:calculateMonsterDamage(attack, defender)
    local baseDamage = attack
    local defense = defender.defense or 8
    
    -- 基礎傷害計算
    local damage = math.max(1, baseDamage - defense * 0.5)
    
    -- 隨機浮動 ±15%
    local variance = 0.85 + math.random() * 0.3
    return math.floor(damage * variance)
end

function CombatSystem:getMonsterLevel(monster)
    -- 根據怪物種類和區域確定等級
    local zoneConfig = GameConfig.ZONES[string.upper(monster.spawnZone)]
    if zoneConfig and zoneConfig.monsterLevelRange then
        return math.random(zoneConfig.monsterLevelRange.min, zoneConfig.monsterLevelRange.max)
    end
    return 1
end

function CombatSystem:onMonsterDefeated(world, playerId, monsterId, monster)
    print("🎉 怪物被擊敗！")
    
    -- 獲取玩家組件
    local hero = world:get(playerId, Components.HeroComponent)
    local currency = world:get(playerId, Components.CurrencyComponent)
    
    if hero and currency then
        -- 計算獎勵
        local expReward = self:calculateExpReward(monster)
        local coinReward = self:calculateCoinReward(monster)
        
        -- 給予經驗值
        local HeroSystem = require(script.Parent.HeroSystem)
        HeroSystem:giveExperience(world, playerId, expReward)
        
        -- 給予金幣
        world:insert(playerId, Components.CurrencyComponent({
            coins = currency.coins + coinReward,
            gems = currency.gems,
            robux = currency.robux,
            experience = currency.experience + expReward
        }))
        
        print("💰 獲得獎勵 - 經驗值:", expReward, "金幣:", coinReward)
    end
    
    -- 結束戰鬥
    self:endCombat(world, playerId, "victory")
    
    -- 移除怪物實體
    world:despawn(monsterId)
    
    -- 觸發怪物遭遇事件 (用於圖鑑)
    self:triggerMonsterEncounter(world, playerId, monster)
end

function CombatSystem:onPlayerDefeated(world, playerId)
    print("💀 玩家被擊敗！")
    
    -- 結束戰鬥
    self:endCombat(world, playerId, "defeat")
    
    -- 復活玩家 (回復到最大血量的50%)
    local hero = world:get(playerId, Components.HeroComponent)
    if hero then
        local reviveHealth = math.floor(hero.maxHealth * 0.5)
        
        world:insert(playerId, Components.HealthComponent({
            current = reviveHealth,
            maximum = hero.maxHealth,
            regeneration = 1,
            lastRegenTime = tick()
        }))
        
        print("🔄 玩家已復活，血量:", reviveHealth)
    end
end

function CombatSystem:calculateExpReward(monster)
    local baseExp = 10
    local monsterLevel = self:getMonsterLevel(monster)
    return baseExp + (monsterLevel * 5)
end

function CombatSystem:calculateCoinReward(monster)
    local baseCoins = 50
    local monsterLevel = self:getMonsterLevel(monster)
    return baseCoins + (monsterLevel * 10)
end

function CombatSystem:triggerMonsterEncounter(world, playerId, monster)
    -- 更新圖鑑組件
    local pokedex = world:get(playerId, Components.PokedexComponent)
    if pokedex then
        local encountered = Utils.deepCopy(pokedex.encounteredSpecies)
        encountered[monster.speciesId] = (encountered[monster.speciesId] or 0) + 1
        
        world:insert(playerId, Components.PokedexComponent({
            encounteredSpecies = encountered,
            ownedSpecies = pokedex.ownedSpecies,
            completionRate = pokedex.completionRate
        }))
        
        print("📖 圖鑑更新 - 遭遇:", monster.speciesId)
    end
end

-- 開始戰鬥
function CombatSystem:startCombat(world, playerId, monsterId)
    local currentTime = tick()
    
    -- 設置玩家戰鬥狀態
    world:insert(playerId, Components.CombatComponent({
        isInCombat = true,
        target = monsterId,
        lastAttackTime = 0,
        combatStartTime = currentTime
    }))
    
    -- 設置怪物戰鬥狀態
    world:insert(monsterId, Components.CombatComponent({
        isInCombat = true,
        target = playerId,
        lastAttackTime = 0,
        combatStartTime = currentTime
    }))
    
    print("⚔️ 戰鬥開始！")
    return true
end

-- 結束戰鬥
function CombatSystem:endCombat(world, entityId, reason)
    local combat = world:get(entityId, Components.CombatComponent)
    if not combat then return end
    
    -- 清除戰鬥狀態
    world:insert(entityId, Components.CombatComponent({
        isInCombat = false,
        target = nil,
        lastAttackTime = 0,
        combatStartTime = 0
    }))
    
    -- 如果有目標，也清除目標的戰鬥狀態
    if combat.target and world:contains(combat.target) then
        world:insert(combat.target, Components.CombatComponent({
            isInCombat = false,
            target = nil,
            lastAttackTime = 0,
            combatStartTime = 0
        }))
    end
    
    print("🏁 戰鬥結束，原因:", reason)
end

function CombatSystem:endMonsterCombat(world, monsterId)
    world:insert(monsterId, Components.CombatComponent({
        isInCombat = false,
        target = nil,
        lastAttackTime = 0,
        combatStartTime = 0
    }))
end

return CombatSystem
