-- 寵物清單組件 - 顯示玩家的寵物收藏
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local StateManager = require(script.Parent.Parent.StateManager)
local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)

local PetList = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

function PetList.new()
    return scope:New "Frame" {
        Name = "PetListFrame",
        Size = UDim2.new(0.8, 0, 0.8, 0),
        Position = UDim2.new(0.1, 0, 0.1, 0),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Visible = scope:Computed(function(use)
            local ui = use(StateManager.UI)
            return ui.petMenuOpen
        end),
        
        [scope.Children] = {
            -- 標題欄
            scope:New "Frame" {
                Name = "TitleBar",
                Size = UDim2.new(1, 0, 0.1, 0),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(30, 30, 30),
                BorderSizePixel = 0,
                
                [scope.Children] = {
                    scope:New "TextLabel" {
                        Name = "Title",
                        Size = UDim2.new(0.8, 0, 1, 0),
                        Position = UDim2.new(0.1, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function(use)
                            local pets = use(StateManager.Pets)
                            return string.format("我的寵物 (%d/%d)", #pets.allPets, 100) -- 假設最大100隻
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    },
                    
                    -- 關閉按鈕
                    scope:New "TextButton" {
                        Name = "CloseButton",
                        Size = UDim2.new(0.08, 0, 0.8, 0),
                        Position = UDim2.new(0.9, 0, 0.1, 0),
                        BackgroundColor3 = Color3.fromRGB(255, 0, 0),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "✕",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        
                        [scope.OnEvent "Activated"] = function()
                            StateManager:UpdateUI({ petMenuOpen = false })
                        end
                    }
                }
            },
            
            -- 標籤欄
            scope:New "Frame" {
                Name = "TabBar",
                Size = UDim2.new(1, 0, 0.08, 0),
                Position = UDim2.new(0, 0, 0.1, 0),
                BackgroundColor3 = Color3.fromRGB(35, 35, 35),
                BorderSizePixel = 0,
                
                [scope.Children] = {
                    scope:New "TextButton" {
                        Name = "AllPetsTab",
                        Size = UDim2.new(0.25, -2, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 150, 255),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "全部寵物",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    },
                    
                    scope:New "TextButton" {
                        Name = "ActivePetsTab",
                        Size = UDim2.new(0.25, -2, 1, 0),
                        Position = UDim2.new(0.25, 2, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "攜帶中",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    },
                    
                    scope:New "TextButton" {
                        Name = "FavoritesTab",
                        Size = UDim2.new(0.25, -2, 1, 0),
                        Position = UDim2.new(0.5, 4, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "收藏",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    },
                    
                    scope:New "TextButton" {
                        Name = "SortTab",
                        Size = UDim2.new(0.25, -2, 1, 0),
                        Position = UDim2.new(0.75, 6, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "排序",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    }
                }
            },
            
            -- 寵物網格
            scope:New "ScrollingFrame" {
                Name = "PetGrid",
                Size = UDim2.new(1, -20, 0.82, -20),
                Position = UDim2.new(0, 10, 0.18, 10),
                BackgroundColor3 = Color3.fromRGB(50, 50, 50),
                BackgroundTransparency = 0.5,
                BorderSizePixel = 0,
                ScrollBarThickness = 8,
                ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100),
                
                [scope.Children] = {
                    scope:New "UIGridLayout" {
                        CellSize = UDim2.new(0, 120, 0, 150),
                        CellPadding = UDim2.new(0, 10, 0, 10),
                        StartCorner = Enum.StartCorner.TopLeft,
                        SortOrder = Enum.SortOrder.LayoutOrder
                    },
                    
                    scope:New "UIPadding" {
                        PaddingAll = UDim.new(0, 10)
                    },
                    
                    -- 動態生成寵物卡片
                    [scope.Children] = scope:Computed(function(use)
                        local pets = use(StateManager.Pets)
                        local petCards = {}
                        
                        for i, pet in ipairs(pets.allPets) do
                            local petCard = PetList.createPetCard(pet, i)
                            table.insert(petCards, petCard)
                        end
                        
                        return petCards
                    end)
                }
            }
        }
    }
end

function PetList.createPetCard(petData, index)
    local speciesConfig = PetConfig.Species[petData.speciesId]
    if not speciesConfig then
        return nil
    end
    
    return scope:New "Frame" {
        Name = "PetCard_" .. index,
        LayoutOrder = index,
        BackgroundColor3 = scope:Computed(function()
            local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
            local rarityConfig = GameConfig.PET.RARITY[string.upper(petData.rarity)]
            return rarityConfig and rarityConfig.color or Color3.fromRGB(100, 100, 100)
        end),
        BackgroundTransparency = 0.3,
        BorderSizePixel = 2,
        BorderColor3 = scope:Computed(function()
            local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
            local rarityConfig = GameConfig.PET.RARITY[string.upper(petData.rarity)]
            return rarityConfig and rarityConfig.color or Color3.fromRGB(100, 100, 100)
        end),
        
        [scope.Children] = {
            -- 寵物圖標
            scope:New "ImageLabel" {
                Name = "PetIcon",
                Size = UDim2.new(0.8, 0, 0.5, 0),
                Position = UDim2.new(0.1, 0, 0.05, 0),
                BackgroundTransparency = 1,
                Image = "rbxassetid://6031075938", -- 預設寵物圖標，應該根據寵物種類替換
                ScaleType = Enum.ScaleType.Fit
            },
            
            -- 寵物名稱
            scope:New "TextLabel" {
                Name = "PetName",
                Size = UDim2.new(0.9, 0, 0.15, 0),
                Position = UDim2.new(0.05, 0, 0.55, 0),
                BackgroundTransparency = 1,
                Text = petData.name or speciesConfig.name,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                TextStrokeTransparency = 0,
                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
            },
            
            -- 寵物等級
            scope:New "TextLabel" {
                Name = "PetLevel",
                Size = UDim2.new(0.4, 0, 0.12, 0),
                Position = UDim2.new(0.05, 0, 0.7, 0),
                BackgroundTransparency = 1,
                Text = "Lv." .. (petData.level or 1),
                TextColor3 = Color3.fromRGB(255, 255, 0),
                TextScaled = true,
                Font = Enum.Font.Gotham,
                TextStrokeTransparency = 0,
                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
            },
            
            -- 稀有度標籤
            scope:New "TextLabel" {
                Name = "RarityLabel",
                Size = UDim2.new(0.5, 0, 0.12, 0),
                Position = UDim2.new(0.45, 0, 0.7, 0),
                BackgroundTransparency = 1,
                Text = string.upper(petData.rarity or "common"),
                TextColor3 = scope:Computed(function()
                    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
                    local rarityConfig = GameConfig.PET.RARITY[string.upper(petData.rarity)]
                    return rarityConfig and rarityConfig.color or Color3.fromRGB(255, 255, 255)
                end),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                TextStrokeTransparency = 0,
                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
            },
            
            -- 閃光效果 (如果是閃光寵物)
            scope:New "ImageLabel" {
                Name = "ShinyEffect",
                Size = UDim2.new(0.3, 0, 0.3, 0),
                Position = UDim2.new(0.65, 0, 0.05, 0),
                BackgroundTransparency = 1,
                Image = "rbxassetid://6031075938", -- 閃光效果圖標
                ScaleType = Enum.ScaleType.Fit,
                Visible = petData.isShiny or false
            },
            
            -- 操作按鈕
            scope:New "Frame" {
                Name = "ActionButtons",
                Size = UDim2.new(0.9, 0, 0.15, 0),
                Position = UDim2.new(0.05, 0, 0.82, 0),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    scope:New "TextButton" {
                        Name = "SelectButton",
                        Size = UDim2.new(0.48, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 150, 0),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "選擇",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        
                        [scope.OnEvent "Activated"] = function()
                            StateManager:UpdatePets({ selectedPet = petData })
                            print("選擇寵物:", petData.name)
                        end
                    },
                    
                    scope:New "TextButton" {
                        Name = "InfoButton",
                        Size = UDim2.new(0.48, 0, 1, 0),
                        Position = UDim2.new(0.52, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 100, 200),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "詳情",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        
                        [scope.OnEvent "Activated"] = function()
                            print("查看寵物詳情:", petData.name)
                            -- 這裡可以打開寵物詳情界面
                        end
                    }
                }
            },
            
            -- 點擊效果
            scope:New "TextButton" {
                Name = "ClickArea",
                Size = UDim2.new(1, 0, 1, 0),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundTransparency = 1,
                Text = "",
                ZIndex = 10,
                
                [scope.OnEvent "Activated"] = function()
                    StateManager:UpdatePets({ selectedPet = petData })
                    print("點擊寵物卡片:", petData.name)
                end
            }
        }
    }
end

function PetList.cleanup()
    scope:doCleanup()
end

return PetList
