-- 簡化版英雄配置系統
local HeroConfig = {}

-- 英雄職業配置
HeroConfig.CLASSES = {
    WARRIOR = {
        name = "戰士",
        description = "近戰坦克，高血量高防禦",
        primaryStat = "strength",
        role = "tank",
        baseStats = {
            health = 150,
            mana = 50,
            strength = 12,
            agility = 6,
            intelligence = 4,
            vitality = 10,
            defense = 8,
            magicResist = 4
        },
        statGrowth = {
            health = 15,
            mana = 3,
            strength = 2.5,
            agility = 1.2,
            intelligence = 0.8,
            vitality = 2.0,
            defense = 1.8,
            magicResist = 0.6
        },
        skills = {
            "Shield Bash",
            "Taunt",
            "Defensive Stance",
            "Charge"
        }
    },
    MAGE = {
        name = "法師",
        description = "遠程魔法輸出，高魔法傷害",
        primaryStat = "intelligence",
        role = "dps",
        baseStats = {
            health = 80,
            mana = 120,
            strength = 4,
            agility = 6,
            intelligence = 12,
            vitality = 6,
            defense = 3,
            magicResist = 8
        },
        statGrowth = {
            health = 8,
            mana = 12,
            strength = 0.8,
            agility = 1.2,
            intelligence = 2.5,
            vitality = 1.2,
            defense = 0.6,
            magicResist = 1.8
        },
        skills = {
            "Fireball",
            "Ice Shard",
            "Lightning Bolt",
            "Mana Shield"
        }
    }
}

-- 韓式慢速升級體驗系統
HeroConfig.LEVEL_SYSTEM = {
    maxLevel = 100,
    
    -- 4階段成長曲線
    growthPhases = {
        {
            name = "新手期",
            levelRange = {1, 20},
            expMultiplier = 1.0,
            description = "快速成長期，體驗遊戲基礎"
        },
        {
            name = "成長期", 
            levelRange = {21, 50},
            expMultiplier = 1.5,
            description = "穩定成長期，學習進階技能"
        },
        {
            name = "挑戰期",
            levelRange = {51, 80},
            expMultiplier = 2.5,
            description = "緩慢成長期，需要更多努力"
        },
        {
            name = "大師期",
            levelRange = {81, 100},
            expMultiplier = 4.0,
            description = "極慢成長期，追求完美"
        }
    }
}

-- 簡化的經驗值計算
function HeroConfig.LEVEL_SYSTEM.getTotalExpForLevel(level)
    if level <= 1 then return 0 end
    
    local totalExp = 0
    for i = 2, level do
        local baseExp = math.floor(100 * (i ^ 1.8))
        
        -- 找到對應階段
        local multiplier = 1.0
        if i <= 20 then
            multiplier = 1.0
        elseif i <= 50 then
            multiplier = 1.5
        elseif i <= 80 then
            multiplier = 2.5
        else
            multiplier = 4.0
        end
        
        totalExp = totalExp + math.floor(baseExp * multiplier)
    end
    return totalExp
end

-- 英雄狀態系統
HeroConfig.STATUS_SYSTEM = {
    baseStates = {
        ALIVE = "alive",
        DEAD = "dead",
        UNCONSCIOUS = "unconscious"
    },
    
    combatStates = {
        IDLE = "idle",
        FIGHTING = "fighting",
        CASTING = "casting",
        STUNNED = "stunned"
    }
}

-- 傷害系統
HeroConfig.DAMAGE_SYSTEM = {
    damageTypes = {
        PHYSICAL = "physical",
        MAGICAL = "magical",
        TRUE = "true"
    },
    
    damageFormulas = {
        physical = function(attack, defense)
            local reduction = defense / (defense + 100)
            return math.max(1, math.floor(attack * (1 - reduction)))
        end,
        
        magical = function(magicPower, magicResist)
            local reduction = magicResist / (magicResist + 100)
            return math.max(1, math.floor(magicPower * (1 - reduction)))
        end,
        
        ["true"] = function(damage)
            return damage
        end
    },
    
    criticalHit = {
        baseCritRate = 0.05,
        baseCritDamage = 1.5,
        agilityCritBonus = 0.001,
        critDamageBonus = 0.002
    }
}

-- 治療系統
HeroConfig.HEALING_SYSTEM = {
    healingTypes = {
        INSTANT = "instant",
        OVER_TIME = "over_time",
        PERCENTAGE = "percentage"
    },
    
    healingSkills = {
        MINOR_HEAL = {
            name = "輕微治療",
            type = "instant",
            healAmount = 50,
            manaCost = 20,
            castTime = 2,
            cooldown = 5
        }
    }
}

-- 屬性計算公式
HeroConfig.STAT_FORMULAS = {
    maxHealth = function(baseHealth, vitality, level, healthGrowth)
        return baseHealth + (vitality * 10) + (level * healthGrowth)
    end,
    
    maxMana = function(baseMana, intelligence, level, manaGrowth)
        return baseMana + (intelligence * 8) + (level * manaGrowth)
    end,
    
    physicalAttack = function(strength, agility)
        return (strength * 2) + (agility * 0.5)
    end,
    
    magicalAttack = function(intelligence)
        return intelligence * 2.5
    end,
    
    physicalDefense = function(defense, strength)
        return defense + (strength * 0.3)
    end,
    
    magicalDefense = function(magicResist, intelligence)
        return magicResist + (intelligence * 0.2)
    end
}

return HeroConfig
