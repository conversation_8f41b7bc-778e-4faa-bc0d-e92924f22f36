-- 共享工具函數
local Utils = {}

local TableUtil = require(script.Parent.Parent.Packages.TableUtil)

-- 數學工具
function Utils.clamp(value, min, max)
    return math.max(min, math.min(max, value))
end

function Utils.lerp(a, b, t)
    return a + (b - a) * t
end

function Utils.round(number, decimals)
    local multiplier = 10 ^ (decimals or 0)
    return math.floor(number * multiplier + 0.5) / multiplier
end

-- 隨機工具
function Utils.randomFloat(min, max)
    return min + math.random() * (max - min)
end

function Utils.randomChoice(array)
    if #array == 0 then return nil end
    return array[math.random(1, #array)]
end

function Utils.weightedRandom(weights)
    local totalWeight = 0
    for _, weight in pairs(weights) do
        totalWeight = totalWeight + weight
    end
    
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for key, weight in pairs(weights) do
        currentWeight = currentWeight + weight
        if random <= currentWeight then
            return key
        end
    end
    
    -- 備用返回第一個
    return next(weights)
end

-- 時間工具
function Utils.getCurrentTime()
    return tick()
end

function Utils.formatTime(seconds)
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    if hours > 0 then
        return string.format("%02d:%02d:%02d", hours, minutes, secs)
    else
        return string.format("%02d:%02d", minutes, secs)
    end
end

function Utils.isTimeExpired(timestamp, duration)
    return tick() - timestamp >= duration
end

-- 字符串工具
function Utils.formatNumber(number)
    if number >= 1000000000 then
        return string.format("%.1fB", number / 1000000000)
    elseif number >= 1000000 then
        return string.format("%.1fM", number / 1000000)
    elseif number >= 1000 then
        return string.format("%.1fK", number / 1000)
    else
        return tostring(number)
    end
end

function Utils.capitalizeFirst(str)
    return str:sub(1, 1):upper() .. str:sub(2):lower()
end

function Utils.splitString(str, delimiter)
    local result = {}
    local pattern = "([^" .. delimiter .. "]+)"
    
    for match in str:gmatch(pattern) do
        table.insert(result, match)
    end
    
    return result
end

-- 表格工具 (基於 TableUtil)
function Utils.deepCopy(original)
    return TableUtil.Copy(original, true)
end

function Utils.mergeTables(...)
    return TableUtil.Assign({}, ...)
end

function Utils.filterTable(tbl, predicate)
    return TableUtil.Filter(tbl, predicate)
end

function Utils.mapTable(tbl, mapper)
    return TableUtil.Map(tbl, mapper)
end

function Utils.findInTable(tbl, predicate)
    for key, value in pairs(tbl) do
        if predicate(value, key) then
            return value, key
        end
    end
    return nil
end

-- 顏色工具
function Utils.hexToColor3(hex)
    hex = hex:gsub("#", "")
    local r = tonumber(hex:sub(1, 2), 16) / 255
    local g = tonumber(hex:sub(3, 4), 16) / 255
    local b = tonumber(hex:sub(5, 6), 16) / 255
    return Color3.new(r, g, b)
end

function Utils.lerpColor3(color1, color2, t)
    return Color3.new(
        Utils.lerp(color1.R, color2.R, t),
        Utils.lerp(color1.G, color2.G, t),
        Utils.lerp(color1.B, color2.B, t)
    )
end

-- 遊戲特定工具
function Utils.calculateExpForLevel(level)
    local GameConfig = require(script.Parent.Parent.Configuration.GameConfig)
    local formula = GameConfig.HERO.EXP_FORMULA
    
    if level <= formula.PHASE_1.MAX_LEVEL then
        return math.floor(formula.PHASE_1.BASE * math.pow(formula.PHASE_1.MULTIPLIER, level - 1))
    elseif level <= formula.PHASE_2.MAX_LEVEL then
        return math.floor(formula.PHASE_2.BASE * math.pow(formula.PHASE_2.MULTIPLIER, level - formula.PHASE_2.MIN_LEVEL))
    elseif level <= formula.PHASE_3.MAX_LEVEL then
        return math.floor(formula.PHASE_3.BASE * math.pow(formula.PHASE_3.MULTIPLIER, level - formula.PHASE_3.MIN_LEVEL))
    else
        return math.floor(formula.PHASE_4.BASE * math.pow(formula.PHASE_4.MULTIPLIER, level - formula.PHASE_4.MIN_LEVEL))
    end
end

function Utils.calculateDamage(attacker, defender)
    local baseDamage = attacker.attack or 0
    local defense = defender.defense or 0
    local critChance = attacker.critRate or 0.05
    local critMultiplier = attacker.critDamage or 1.5
    
    -- 基礎傷害計算
    local damage = math.max(1, baseDamage - defense * 0.5)
    
    -- 暴擊計算
    if math.random() < critChance then
        damage = damage * critMultiplier
    end
    
    -- 隨機浮動 ±20%
    local variance = 0.8 + math.random() * 0.4
    return math.floor(damage * variance)
end

function Utils.getRarityColor(rarity)
    local GameConfig = require(script.Parent.Parent.Configuration.GameConfig)
    local rarityData = GameConfig.PET.RARITY[string.upper(rarity)]
    return rarityData and rarityData.color or Color3.fromRGB(255, 255, 255)
end

-- 驗證工具
function Utils.validatePlayerData(data)
    if type(data) ~= "table" then return false end
    if type(data.hero) ~= "table" then return false end
    if type(data.currencies) ~= "table" then return false end
    if type(data.pets) ~= "table" then return false end
    return true
end

function Utils.sanitizeInput(input, maxLength)
    if type(input) ~= "string" then return "" end
    input = input:gsub("[^%w%s]", "") -- 只保留字母數字和空格
    return input:sub(1, maxLength or 50)
end

-- 性能工具
function Utils.debounce(func, delay)
    local lastCall = 0
    return function(...)
        local now = tick()
        if now - lastCall >= delay then
            lastCall = now
            return func(...)
        end
    end
end

function Utils.throttle(func, interval)
    local lastCall = 0
    local timer = nil
    
    return function(...)
        local now = tick()
        local args = {...}
        
        if now - lastCall >= interval then
            lastCall = now
            return func(unpack(args))
        elseif not timer then
            timer = game:GetService("RunService").Heartbeat:Connect(function()
                local currentTime = tick()
                if currentTime - lastCall >= interval then
                    lastCall = currentTime
                    timer:Disconnect()
                    timer = nil
                    func(unpack(args))
                end
            end)
        end
    end
end

-- 錯誤處理
function Utils.safeCall(func, ...)
    local success, result = pcall(func, ...)
    if success then
        return result
    else
        warn("Utils.safeCall 錯誤:", result)
        return nil
    end
end

function Utils.retry(func, maxAttempts, delay)
    maxAttempts = maxAttempts or 3
    delay = delay or 1
    
    for attempt = 1, maxAttempts do
        local success, result = pcall(func)
        if success then
            return result
        elseif attempt < maxAttempts then
            wait(delay)
        end
    end
    
    error("重試失敗，已達到最大嘗試次數")
end

return Utils
