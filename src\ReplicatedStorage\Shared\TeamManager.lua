-- 組隊管理系統
local TeamManager = {}

-- 依賴
local MapConfig = require(game.ReplicatedStorage.Configuration.MapConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 隊伍數據
TeamManager.teams = {}
TeamManager.playerTeams = {} -- 玩家ID -> 隊伍ID映射
TeamManager.teamCounter = 0

-- 隊伍狀態
TeamManager.TEAM_STATUS = {
    FORMING = "forming",     -- 組建中
    READY = "ready",         -- 準備就緒
    IN_DUNGEON = "in_dungeon", -- 副本中
    COMPLETED = "completed"   -- 已完成
}

-- 玩家角色
TeamManager.PLAYER_ROLES = {
    TANK = "tank",       -- 坦克
    DPS = "dps",         -- 輸出
    SUPPORT = "support"  -- 輔助
}

-- 初始化組隊系統
function TeamManager:Initialize()
    print("👥 初始化組隊管理系統...")
    
    -- 重置數據
    self.teams = {}
    self.playerTeams = {}
    self.teamCounter = 0
    
    print("✅ 組隊管理系統初始化完成")
end

-- 創建隊伍
function TeamManager:CreateTeam(leader, targetDungeon)
    if not leader or not targetDungeon then
        return nil, "無效的參數"
    end
    
    -- 檢查玩家是否已在隊伍中
    if self.playerTeams[leader.UserId] then
        return nil, "玩家已在隊伍中"
    end
    
    -- 檢查副本是否存在
    local dungeonConfig = MapConfig.DUNGEONS[targetDungeon]
    if not dungeonConfig then
        return nil, "副本不存在"
    end
    
    -- 檢查玩家等級
    local playerLevel = self:GetPlayerLevel(leader)
    if playerLevel < dungeonConfig.requiredLevel then
        return nil, string.format("等級不足，需要等級 %d", dungeonConfig.requiredLevel)
    end
    
    -- 創建新隊伍
    self.teamCounter = self.teamCounter + 1
    local teamId = "team_" .. self.teamCounter
    
    local team = {
        id = teamId,
        leader = leader,
        members = {leader},
        targetDungeon = targetDungeon,
        status = self.TEAM_STATUS.FORMING,
        createdTime = tick(),
        memberRoles = {
            [leader.UserId] = self.PLAYER_ROLES.TANK -- 隊長默認為坦克
        },
        settings = {
            autoAccept = false,
            levelRange = {
                min = math.max(1, playerLevel - 5),
                max = playerLevel + 5
            }
        }
    }
    
    self.teams[teamId] = team
    self.playerTeams[leader.UserId] = teamId
    
    -- 觸發隊伍創建事件
    EventManager:Fire(EventManager.EventTypes.TEAM_CREATED, {
        teamId = teamId,
        leader = leader,
        targetDungeon = targetDungeon
    })
    
    print(string.format("👥 %s 創建了隊伍 %s (目標: %s)", leader.Name, teamId, targetDungeon))
    
    return teamId, nil
end

-- 加入隊伍
function TeamManager:JoinTeam(player, teamId)
    if not player or not teamId then
        return false, "無效的參數"
    end
    
    -- 檢查玩家是否已在隊伍中
    if self.playerTeams[player.UserId] then
        return false, "玩家已在隊伍中"
    end
    
    -- 檢查隊伍是否存在
    local team = self.teams[teamId]
    if not team then
        return false, "隊伍不存在"
    end
    
    -- 檢查隊伍狀態
    if team.status ~= self.TEAM_STATUS.FORMING then
        return false, "隊伍不在組建狀態"
    end
    
    -- 檢查隊伍人數限制
    local dungeonConfig = MapConfig.DUNGEONS[team.targetDungeon]
    if #team.members >= dungeonConfig.teamSize.max then
        return false, "隊伍已滿"
    end
    
    -- 檢查玩家等級
    local playerLevel = self:GetPlayerLevel(player)
    if playerLevel < dungeonConfig.requiredLevel then
        return false, string.format("等級不足，需要等級 %d", dungeonConfig.requiredLevel)
    end
    
    -- 檢查等級範圍
    if playerLevel < team.settings.levelRange.min or playerLevel > team.settings.levelRange.max then
        return false, "等級不在隊伍要求範圍內"
    end
    
    -- 加入隊伍
    table.insert(team.members, player)
    self.playerTeams[player.UserId] = teamId
    
    -- 分配角色 (簡單的自動分配)
    team.memberRoles[player.UserId] = self:AutoAssignRole(team)
    
    -- 觸發加入隊伍事件
    EventManager:Fire(EventManager.EventTypes.TEAM_MEMBER_JOINED, {
        teamId = teamId,
        player = player,
        memberCount = #team.members
    })
    
    print(string.format("👥 %s 加入了隊伍 %s", player.Name, teamId))
    
    -- 檢查是否達到最小人數要求
    if #team.members >= dungeonConfig.teamSize.min then
        team.status = self.TEAM_STATUS.READY
        
        EventManager:Fire(EventManager.EventTypes.TEAM_READY, {
            teamId = teamId,
            memberCount = #team.members
        })
    end
    
    return true, nil
end

-- 離開隊伍
function TeamManager:LeaveTeam(player)
    if not player then
        return false, "無效的參數"
    end
    
    local teamId = self.playerTeams[player.UserId]
    if not teamId then
        return false, "玩家不在任何隊伍中"
    end
    
    local team = self.teams[teamId]
    if not team then
        return false, "隊伍不存在"
    end
    
    -- 從隊伍中移除玩家
    for i, member in ipairs(team.members) do
        if member.UserId == player.UserId then
            table.remove(team.members, i)
            break
        end
    end
    
    self.playerTeams[player.UserId] = nil
    team.memberRoles[player.UserId] = nil
    
    -- 觸發離開隊伍事件
    EventManager:Fire(EventManager.EventTypes.TEAM_MEMBER_LEFT, {
        teamId = teamId,
        player = player,
        memberCount = #team.members
    })
    
    print(string.format("👥 %s 離開了隊伍 %s", player.Name, teamId))
    
    -- 如果是隊長離開，解散隊伍或轉移隊長
    if team.leader.UserId == player.UserId then
        if #team.members > 0 then
            -- 轉移隊長給第一個成員
            team.leader = team.members[1]
            
            EventManager:Fire(EventManager.EventTypes.TEAM_LEADER_CHANGED, {
                teamId = teamId,
                newLeader = team.leader
            })
        else
            -- 解散隊伍
            self:DisbandTeam(teamId)
            return true, nil
        end
    end
    
    -- 檢查人數是否低於最小要求
    local dungeonConfig = MapConfig.DUNGEONS[team.targetDungeon]
    if #team.members < dungeonConfig.teamSize.min then
        team.status = self.TEAM_STATUS.FORMING
    end
    
    return true, nil
end

-- 解散隊伍
function TeamManager:DisbandTeam(teamId)
    local team = self.teams[teamId]
    if not team then
        return false, "隊伍不存在"
    end
    
    -- 清理所有成員的隊伍映射
    for _, member in ipairs(team.members) do
        self.playerTeams[member.UserId] = nil
    end
    
    -- 觸發隊伍解散事件
    EventManager:Fire(EventManager.EventTypes.TEAM_DISBANDED, {
        teamId = teamId,
        reason = "隊伍解散"
    })
    
    print(string.format("👥 隊伍 %s 已解散", teamId))
    
    -- 刪除隊伍
    self.teams[teamId] = nil
    
    return true
end

-- 開始副本
function TeamManager:StartDungeon(teamId)
    local team = self.teams[teamId]
    if not team then
        return false, "隊伍不存在"
    end
    
    if team.status ~= self.TEAM_STATUS.READY then
        return false, "隊伍未準備就緒"
    end
    
    local dungeonConfig = MapConfig.DUNGEONS[team.targetDungeon]
    
    -- 檢查人數要求
    if #team.members < dungeonConfig.teamSize.min or #team.members > dungeonConfig.teamSize.max then
        return false, "隊伍人數不符合要求"
    end
    
    -- 更新隊伍狀態
    team.status = self.TEAM_STATUS.IN_DUNGEON
    team.dungeonStartTime = tick()
    
    -- 觸發副本開始事件
    EventManager:Fire(EventManager.EventTypes.DUNGEON_STARTED, {
        teamId = teamId,
        dungeon = team.targetDungeon,
        members = team.members
    })
    
    print(string.format("🏰 隊伍 %s 開始了 %s 副本", teamId, team.targetDungeon))
    
    return true, nil
end

-- 自動分配角色
function TeamManager:AutoAssignRole(team)
    local roleCount = {
        tank = 0,
        dps = 0,
        support = 0
    }
    
    -- 統計現有角色
    for _, role in pairs(team.memberRoles) do
        roleCount[role] = roleCount[role] + 1
    end
    
    local requirements = MapConfig.TEAM_REQUIREMENTS[team.targetDungeon].roles
    
    -- 優先分配缺少的角色
    if roleCount.tank < requirements.tank.min then
        return self.PLAYER_ROLES.TANK
    elseif roleCount.support < requirements.support.min then
        return self.PLAYER_ROLES.SUPPORT
    else
        return self.PLAYER_ROLES.DPS
    end
end

-- 獲取玩家等級 (模擬)
function TeamManager:GetPlayerLevel(player)
    -- 這裡應該連接到實際的玩家數據系統
    -- 現在返回隨機等級用於測試
    return math.random(1, 50)
end

-- 獲取隊伍信息
function TeamManager:GetTeamInfo(teamId)
    return self.teams[teamId]
end

-- 獲取玩家的隊伍
function TeamManager:GetPlayerTeam(player)
    local teamId = self.playerTeams[player.UserId]
    if teamId then
        return self.teams[teamId]
    end
    return nil
end

-- 獲取所有可加入的隊伍
function TeamManager:GetAvailableTeams(player, targetDungeon)
    local availableTeams = {}
    local playerLevel = self:GetPlayerLevel(player)
    
    for teamId, team in pairs(self.teams) do
        if team.status == self.TEAM_STATUS.FORMING and 
           team.targetDungeon == targetDungeon then
            
            local dungeonConfig = MapConfig.DUNGEONS[targetDungeon]
            
            -- 檢查人數限制
            if #team.members < dungeonConfig.teamSize.max then
                -- 檢查等級要求
                if playerLevel >= dungeonConfig.requiredLevel and
                   playerLevel >= team.settings.levelRange.min and
                   playerLevel <= team.settings.levelRange.max then
                    
                    table.insert(availableTeams, {
                        teamId = teamId,
                        leader = team.leader.Name,
                        memberCount = #team.members,
                        maxMembers = dungeonConfig.teamSize.max,
                        averageLevel = self:CalculateAverageLevel(team)
                    })
                end
            end
        end
    end
    
    return availableTeams
end

-- 計算隊伍平均等級
function TeamManager:CalculateAverageLevel(team)
    local totalLevel = 0
    for _, member in ipairs(team.members) do
        totalLevel = totalLevel + self:GetPlayerLevel(member)
    end
    return math.floor(totalLevel / #team.members)
end

-- 獲取統計信息
function TeamManager:GetStats()
    local stats = {
        totalTeams = 0,
        formingTeams = 0,
        readyTeams = 0,
        inDungeonTeams = 0,
        teamsByDungeon = {}
    }
    
    for _, team in pairs(self.teams) do
        stats.totalTeams = stats.totalTeams + 1
        
        if team.status == self.TEAM_STATUS.FORMING then
            stats.formingTeams = stats.formingTeams + 1
        elseif team.status == self.TEAM_STATUS.READY then
            stats.readyTeams = stats.readyTeams + 1
        elseif team.status == self.TEAM_STATUS.IN_DUNGEON then
            stats.inDungeonTeams = stats.inDungeonTeams + 1
        end
        
        stats.teamsByDungeon[team.targetDungeon] = (stats.teamsByDungeon[team.targetDungeon] or 0) + 1
    end
    
    return stats
end

-- 調試信息
function TeamManager:Debug()
    print("👥 組隊管理系統調試信息:")
    print("  總隊伍數:", #self.teams)
    print("  玩家-隊伍映射數:", #self.playerTeams)
    
    local stats = self:GetStats()
    print("  組建中隊伍:", stats.formingTeams)
    print("  準備就緒隊伍:", stats.readyTeams)
    print("  副本中隊伍:", stats.inDungeonTeams)
    
    print("  各副本隊伍數:")
    for dungeon, count in pairs(stats.teamsByDungeon) do
        print(string.format("    %s: %d", dungeon, count))
    end
end

return TeamManager
