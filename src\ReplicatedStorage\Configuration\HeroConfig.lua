-- 英雄配置系統
local HeroConfig = {}

-- 英雄職業配置
HeroConfig.CLASSES = {
    WARRIOR = {
        name = "戰士",
        description = "近戰坦克，高血量高防禦",
        primaryStat = "strength",
        role = "tank",
        baseStats = {
            health = 150,
            mana = 50,
            strength = 12,
            agility = 6,
            intelligence = 4,
            vitality = 10,
            defense = 8,
            magicResist = 4
        },
        statGrowth = {
            health = 15,
            mana = 3,
            strength = 2.5,
            agility = 1.2,
            intelligence = 0.8,
            vitality = 2.0,
            defense = 1.8,
            magicResist = 0.6
        },
        skills = {
            "Shield Bash",
            "Taunt",
            "Defensive Stance",
            "Charge"
        }
    },
    MAGE = {
        name = "法師",
        description = "遠程魔法輸出，高魔法傷害",
        primaryStat = "intelligence",
        role = "dps",
        baseStats = {
            health = 80,
            mana = 120,
            strength = 4,
            agility = 6,
            intelligence = 12,
            vitality = 6,
            defense = 3,
            magicResist = 8
        },
        statGrowth = {
            health = 8,
            mana = 12,
            strength = 0.8,
            agility = 1.2,
            intelligence = 2.5,
            vitality = 1.2,
            defense = 0.6,
            magicResist = 1.8
        },
        skills = {
            "Fireball",
            "Ice Shard",
            "Lightning Bolt",
            "Mana Shield"
        }
    },
    ARCHER = {
        name = "弓箭手",
        description = "遠程物理輸出，高敏捷高暴擊",
        primaryStat = "agility",
        role = "dps",
        baseStats = {
            health = 100,
            mana = 80,
            strength = 8,
            agility = 12,
            intelligence = 6,
            vitality = 8,
            defense = 5,
            magicResist = 5
        },
        statGrowth = {
            health = 10,
            mana = 6,
            strength = 1.5,
            agility = 2.5,
            intelligence = 1.2,
            vitality = 1.5,
            defense = 1.0,
            magicResist = 1.0
        },
        skills = {
            "Power Shot",
            "Multi Shot",
            "Eagle Eye",
            "Evasion"
        }
    },
    CLERIC = {
        name = "牧師",
        description = "輔助治療，團隊支援",
        primaryStat = "intelligence",
        role = "support",
        baseStats = {
            health = 90,
            mana = 100,
            strength = 5,
            agility = 7,
            intelligence = 11,
            vitality = 9,
            defense = 6,
            magicResist = 9
        },
        statGrowth = {
            health = 9,
            mana = 10,
            strength = 1.0,
            agility = 1.4,
            intelligence = 2.2,
            vitality = 1.8,
            defense = 1.2,
            magicResist = 2.0
        },
        skills = {
            "Heal",
            "Group Heal",
            "Blessing",
            "Resurrection"
        }
    }
}

-- 韓式慢速升級體驗系統 (4階段成長曲線)
HeroConfig.LEVEL_SYSTEM = {
    maxLevel = 100,
    
    -- 4階段成長曲線
    growthPhases = {
        {
            name = "新手期",
            levelRange = {1, 20},
            expMultiplier = 1.0,
            description = "快速成長期，體驗遊戲基礎"
        },
        {
            name = "成長期", 
            levelRange = {21, 50},
            expMultiplier = 1.5,
            description = "穩定成長期，學習進階技能"
        },
        {
            name = "挑戰期",
            levelRange = {51, 80},
            expMultiplier = 2.5,
            description = "緩慢成長期，需要更多努力"
        },
        {
            name = "大師期",
            levelRange = {81, 100},
            expMultiplier = 4.0,
            description = "極慢成長期，追求完美"
        }
    },
    
    -- 基礎經驗值計算公式
    baseExpFormula = function(level)
        return math.floor(100 * (level ^ 1.8))
    end,
    
    -- 獲取等級所需總經驗
    getTotalExpForLevel = function(level)
        if level <= 1 then return 0 end

        local totalExp = 0
        for i = 2, level do
            local baseExp = math.floor(100 * (i ^ 1.8)) -- 直接使用公式

            -- 找到對應階段
            local phase = nil
            local growthPhases = {
                {levelRange = {1, 20}, expMultiplier = 1.0},
                {levelRange = {21, 50}, expMultiplier = 1.5},
                {levelRange = {51, 80}, expMultiplier = 2.5},
                {levelRange = {81, 100}, expMultiplier = 4.0}
            }

            for _, p in ipairs(growthPhases) do
                if i >= p.levelRange[1] and i <= p.levelRange[2] then
                    phase = p
                    break
                end
            end

            local multiplier = phase and phase.expMultiplier or 1.0
            totalExp = totalExp + math.floor(baseExp * multiplier)
        end
        return totalExp
    end,
    
    -- 獲取等級對應的階段
    getPhaseForLevel = function(self, level)
        for _, phase in ipairs(self.growthPhases) do
            if level >= phase.levelRange[1] and level <= phase.levelRange[2] then
                return phase
            end
        end
        return self.growthPhases[#self.growthPhases] -- 默認返回最後階段
    end
}

-- 英雄狀態系統
HeroConfig.STATUS_SYSTEM = {
    -- 基礎狀態
    baseStates = {
        ALIVE = "alive",
        DEAD = "dead",
        UNCONSCIOUS = "unconscious"
    },
    
    -- 戰鬥狀態
    combatStates = {
        IDLE = "idle",
        FIGHTING = "fighting",
        CASTING = "casting",
        STUNNED = "stunned",
        CHANNELING = "channeling"
    },
    
    -- 增益/減益效果
    effects = {
        -- 增益效果
        buffs = {
            BLESSING = {
                name = "祝福",
                duration = 300, -- 5分鐘
                effects = {
                    allStats = 1.1 -- 所有屬性+10%
                }
            },
            REGENERATION = {
                name = "再生",
                duration = 60,
                effects = {
                    healthRegen = 5 -- 每秒回復5血
                }
            },
            HASTE = {
                name = "急速",
                duration = 30,
                effects = {
                    agility = 1.2 -- 敏捷+20%
                }
            }
        },
        
        -- 減益效果
        debuffs = {
            POISON = {
                name = "中毒",
                duration = 30,
                effects = {
                    healthDrain = 3 -- 每秒扣3血
                }
            },
            WEAKNESS = {
                name = "虛弱",
                duration = 60,
                effects = {
                    strength = 0.8 -- 力量-20%
                }
            },
            SILENCE = {
                name = "沉默",
                duration = 15,
                effects = {
                    canCastSpells = false
                }
            }
        }
    },
    
    -- 復活系統
    resurrection = {
        -- 復活方式
        methods = {
            CLERIC_SKILL = {
                name = "牧師復活",
                castTime = 5,
                manaCost = 50,
                cooldown = 300,
                reviveHealthPercent = 0.25
            },
            RESURRECTION_ITEM = {
                name = "復活道具",
                castTime = 0,
                itemRequired = "Resurrection Potion",
                reviveHealthPercent = 0.5
            },
            AUTO_REVIVE = {
                name = "自動復活",
                delay = 30, -- 30秒後自動復活
                reviveHealthPercent = 0.1,
                location = "spawn" -- 在出生點復活
            }
        },
        
        -- 死亡懲罰
        deathPenalty = {
            expLoss = 0.05, -- 失去5%經驗
            durabilityLoss = 0.1, -- 裝備耐久度-10%
            temporaryStatReduction = {
                duration = 600, -- 10分鐘
                reduction = 0.9 -- 所有屬性-10%
            }
        }
    }
}

-- 治療系統
HeroConfig.HEALING_SYSTEM = {
    -- 治療類型
    healingTypes = {
        INSTANT = "instant", -- 瞬間治療
        OVER_TIME = "over_time", -- 持續治療
        PERCENTAGE = "percentage" -- 百分比治療
    },
    
    -- 治療技能配置
    healingSkills = {
        MINOR_HEAL = {
            name = "輕微治療",
            type = "instant",
            healAmount = 50,
            manaCost = 20,
            castTime = 2,
            cooldown = 5
        },
        MAJOR_HEAL = {
            name = "強力治療",
            type = "instant", 
            healAmount = 150,
            manaCost = 50,
            castTime = 4,
            cooldown = 10
        },
        REGENERATION = {
            name = "再生術",
            type = "over_time",
            healAmount = 10,
            duration = 30,
            tickInterval = 3,
            manaCost = 40,
            castTime = 3,
            cooldown = 60
        },
        FULL_HEAL = {
            name = "完全治療",
            type = "percentage",
            healPercent = 1.0, -- 100%
            manaCost = 100,
            castTime = 8,
            cooldown = 300
        }
    },
    
    -- 自然回復
    naturalRegeneration = {
        healthRegenRate = 0.01, -- 每秒回復最大血量的1%
        manaRegenRate = 0.02, -- 每秒回復最大魔力的2%
        combatRegenPenalty = 0.5, -- 戰鬥中回復速度減半
        outOfCombatDelay = 5 -- 脫離戰鬥5秒後開始正常回復
    }
}

-- 傷害系統
HeroConfig.DAMAGE_SYSTEM = {
    -- 傷害類型
    damageTypes = {
        PHYSICAL = "physical",
        MAGICAL = "magical",
        TRUE = "true" -- 真實傷害，無視防禦
    },
    
    -- 傷害計算公式
    damageFormulas = {
        physical = function(attack, defense)
            local reduction = defense / (defense + 100)
            return math.max(1, math.floor(attack * (1 - reduction)))
        end,
        
        magical = function(magicPower, magicResist)
            local reduction = magicResist / (magicResist + 100)
            return math.max(1, math.floor(magicPower * (1 - reduction)))
        end,
        
        ["true"] = function(damage)
            return damage -- 真實傷害不減免
        end
    },
    
    -- 暴擊系統
    criticalHit = {
        baseCritRate = 0.05, -- 基礎暴擊率5%
        baseCritDamage = 1.5, -- 基礎暴擊傷害150%
        agilityCritBonus = 0.001, -- 每點敏捷增加0.1%暴擊率
        critDamageBonus = 0.002 -- 每點敏捷增加0.2%暴擊傷害
    }
}

-- 屬性計算公式
HeroConfig.STAT_FORMULAS = {
    -- 最大血量 = 基礎血量 + (體力 * 10) + (等級 * 血量成長)
    maxHealth = function(baseHealth, vitality, level, healthGrowth)
        return baseHealth + (vitality * 10) + (level * healthGrowth)
    end,
    
    -- 最大魔力 = 基礎魔力 + (智力 * 8) + (等級 * 魔力成長)
    maxMana = function(baseMana, intelligence, level, manaGrowth)
        return baseMana + (intelligence * 8) + (level * manaGrowth)
    end,
    
    -- 物理攻擊力 = 力量 * 2 + 敏捷 * 0.5
    physicalAttack = function(strength, agility)
        return (strength * 2) + (agility * 0.5)
    end,
    
    -- 魔法攻擊力 = 智力 * 2.5
    magicalAttack = function(intelligence)
        return intelligence * 2.5
    end,
    
    -- 物理防禦 = 防禦 + (力量 * 0.3)
    physicalDefense = function(defense, strength)
        return defense + (strength * 0.3)
    end,
    
    -- 魔法防禦 = 魔抗 + (智力 * 0.2)
    magicalDefense = function(magicResist, intelligence)
        return magicResist + (intelligence * 0.2)
    end,
    
    -- 移動速度 = 基礎速度 + (敏捷 * 0.1)
    moveSpeed = function(baseSpeed, agility)
        return baseSpeed + (agility * 0.1)
    end,
    
    -- 攻擊速度 = 基礎攻速 + (敏捷 * 0.02)
    attackSpeed = function(baseAttackSpeed, agility)
        return baseAttackSpeed + (agility * 0.02)
    end
}

return HeroConfig
