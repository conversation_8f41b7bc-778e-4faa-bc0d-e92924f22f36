-- 寵物背包管理系統
-- 攜帶5隻寵物(只能跟隨1隻一起打怪)，倉庫無限存儲

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local Matter = require(ReplicatedStorage.Packages.Matter)
local MonsterComponents = require(ReplicatedStorage.Components.MonsterComponents)
local Signal = require(ReplicatedStorage.Packages.Signal)

local PetInventorySystem = {}

-- 事件信號
PetInventorySystem.onPetEquipped = Signal.new()
PetInventorySystem.onPetUnequipped = Signal.new()
PetInventorySystem.onPetStored = Signal.new()
PetInventorySystem.onPetRetrieved = Signal.new()

-- 背包配置
local INVENTORY_CONFIG = {
    MAX_ACTIVE_PETS = 5,    -- 最大攜帶寵物數量
    MAX_FOLLOWING_PETS = 1, -- 最大跟隨寵物數量
    UNLIMITED_STORAGE = true -- 倉庫無限存儲
}

-- 寵物背包組件
local PetInventory = Matter.component("PetInventory", {
    playerId = 0,
    activePets = {}, -- 攜帶的寵物ID列表 (最多5隻)
    followingPet = nil, -- 當前跟隨的寵物ID (只能1隻)
    storedPets = {}, -- 倉庫中的寵物ID列表 (無限)
    lastUpdated = 0
})

-- 寵物狀態組件
local PetStatus = Matter.component("PetStatus", {
    ownerId = 0, -- 擁有者ID
    status = "stored", -- 狀態: stored(倉庫), active(攜帶), following(跟隨)
    slot = 0, -- 槽位 (1-5 for active pets)
    lastStatusChange = 0,
    isLocked = false -- 是否鎖定
})

-- 獲取玩家的寵物背包
function PetInventorySystem.getPlayerInventory(world, playerId)
    for entityId, inventory in world:query(PetInventory) do
        if inventory.playerId == playerId then
            return entityId, inventory
        end
    end
    return nil, nil
end

-- 創建玩家寵物背包
function PetInventorySystem.createPlayerInventory(world, playerId)
    return world:spawn(
        PetInventory({
            playerId = playerId,
            activePets = {},
            followingPet = nil,
            storedPets = {},
            lastUpdated = tick()
        })
    )
end

-- 獲取寵物狀態
function PetInventorySystem.getPetStatus(world, petId)
    for entityId, status in world:query(PetStatus) do
        if entityId == petId then
            return status
        end
    end
    return nil
end

-- 設置寵物狀態
function PetInventorySystem.setPetStatus(world, petId, ownerId, status, slot)
    local currentTime = tick()
    
    world:insert(petId, PetStatus({
        ownerId = ownerId,
        status = status,
        slot = slot or 0,
        lastStatusChange = currentTime,
        isLocked = false
    }))
end

-- 檢查是否可以攜帶更多寵物
function PetInventorySystem.canCarryMorePets(inventory)
    return #inventory.activePets < INVENTORY_CONFIG.MAX_ACTIVE_PETS
end

-- 檢查是否可以設置跟隨寵物
function PetInventorySystem.canSetFollowingPet(inventory)
    return inventory.followingPet == nil
end

-- 添加寵物到背包
function PetInventorySystem.addPetToInventory(world, playerId, petId)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    
    if not inventoryId then
        inventoryId = PetInventorySystem.createPlayerInventory(world, playerId)
        inventory = world:get(inventoryId, PetInventory)
    end
    
    -- 默認存儲到倉庫
    table.insert(inventory.storedPets, petId)
    inventory.lastUpdated = tick()
    
    -- 設置寵物狀態
    PetInventorySystem.setPetStatus(world, petId, playerId, "stored")
    
    -- 更新背包
    world:insert(inventoryId, PetInventory(inventory))
    
    print(string.format("📦 寵物 %d 已添加到玩家 %d 的倉庫", petId, playerId))
    return true
end

-- 裝備寵物到攜帶槽位
function PetInventorySystem.equipPet(world, playerId, petId, slot)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    if not inventoryId then return false, "背包不存在" end
    
    -- 檢查槽位是否有效
    if slot < 1 or slot > INVENTORY_CONFIG.MAX_ACTIVE_PETS then
        return false, "無效的槽位"
    end
    
    -- 檢查寵物是否在倉庫中
    local petIndex = nil
    for i, storedPetId in ipairs(inventory.storedPets) do
        if storedPetId == petId then
            petIndex = i
            break
        end
    end
    
    if not petIndex then
        return false, "寵物不在倉庫中"
    end
    
    -- 檢查槽位是否已被占用
    for i, activePetId in ipairs(inventory.activePets) do
        if i == slot and activePetId ~= nil then
            return false, "槽位已被占用"
        end
    end
    
    -- 從倉庫移除
    table.remove(inventory.storedPets, petIndex)
    
    -- 添加到攜帶槽位
    inventory.activePets[slot] = petId
    inventory.lastUpdated = tick()
    
    -- 設置寵物狀態
    PetInventorySystem.setPetStatus(world, petId, playerId, "active", slot)
    
    -- 更新背包
    world:insert(inventoryId, PetInventory(inventory))
    
    -- 觸發事件
    PetInventorySystem.onPetEquipped:Fire(playerId, petId, slot)
    
    print(string.format("⚔️ 玩家 %d 裝備了寵物 %d 到槽位 %d", playerId, petId, slot))
    return true
end

-- 卸下寵物到倉庫
function PetInventorySystem.unequipPet(world, playerId, slot)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    if not inventoryId then return false, "背包不存在" end
    
    -- 檢查槽位是否有效
    if slot < 1 or slot > INVENTORY_CONFIG.MAX_ACTIVE_PETS then
        return false, "無效的槽位"
    end
    
    local petId = inventory.activePets[slot]
    if not petId then
        return false, "槽位為空"
    end
    
    -- 如果是跟隨寵物，先取消跟隨
    if inventory.followingPet == petId then
        PetInventorySystem.stopFollowing(world, playerId)
    end
    
    -- 從攜帶槽位移除
    inventory.activePets[slot] = nil
    
    -- 添加到倉庫
    table.insert(inventory.storedPets, petId)
    inventory.lastUpdated = tick()
    
    -- 設置寵物狀態
    PetInventorySystem.setPetStatus(world, petId, playerId, "stored")
    
    -- 更新背包
    world:insert(inventoryId, PetInventory(inventory))
    
    -- 觸發事件
    PetInventorySystem.onPetUnequipped:Fire(playerId, petId, slot)
    
    print(string.format("📦 玩家 %d 卸下了槽位 %d 的寵物 %d", playerId, slot, petId))
    return true
end

-- 設置跟隨寵物
function PetInventorySystem.setFollowingPet(world, playerId, petId)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    if not inventoryId then return false, "背包不存在" end
    
    -- 檢查寵物是否在攜帶槽位中
    local isActive = false
    for _, activePetId in ipairs(inventory.activePets) do
        if activePetId == petId then
            isActive = true
            break
        end
    end
    
    if not isActive then
        return false, "寵物必須先裝備到攜帶槽位"
    end
    
    -- 如果已有跟隨寵物，先停止跟隨
    if inventory.followingPet then
        PetInventorySystem.stopFollowing(world, playerId)
    end
    
    -- 設置新的跟隨寵物
    inventory.followingPet = petId
    inventory.lastUpdated = tick()
    
    -- 設置寵物狀態
    PetInventorySystem.setPetStatus(world, petId, playerId, "following")
    
    -- 更新背包
    world:insert(inventoryId, PetInventory(inventory))
    
    print(string.format("🐕 玩家 %d 設置寵物 %d 為跟隨寵物", playerId, petId))
    return true
end

-- 停止跟隨
function PetInventorySystem.stopFollowing(world, playerId)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    if not inventoryId then return false, "背包不存在" end
    
    local petId = inventory.followingPet
    if not petId then return false, "沒有跟隨寵物" end
    
    -- 清除跟隨寵物
    inventory.followingPet = nil
    inventory.lastUpdated = tick()
    
    -- 設置寵物狀態為攜帶
    PetInventorySystem.setPetStatus(world, petId, playerId, "active")
    
    -- 更新背包
    world:insert(inventoryId, PetInventory(inventory))
    
    print(string.format("🚫 玩家 %d 停止了寵物 %d 的跟隨", playerId, petId))
    return true
end

-- 獲取玩家的寵物統計
function PetInventorySystem.getPlayerPetStats(world, playerId)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    if not inventoryId then
        return {
            totalPets = 0,
            activePets = 0,
            storedPets = 0,
            followingPet = nil
        }
    end
    
    local activePetCount = 0
    for _, petId in ipairs(inventory.activePets) do
        if petId then
            activePetCount = activePetCount + 1
        end
    end
    
    return {
        totalPets = activePetCount + #inventory.storedPets,
        activePets = activePetCount,
        storedPets = #inventory.storedPets,
        followingPet = inventory.followingPet
    }
end

-- 獲取玩家的所有寵物
function PetInventorySystem.getPlayerPets(world, playerId)
    local inventoryId, inventory = PetInventorySystem.getPlayerInventory(world, playerId)
    if not inventoryId then return {} end
    
    local pets = {
        active = {},
        stored = {},
        following = inventory.followingPet
    }
    
    -- 獲取攜帶的寵物
    for slot, petId in ipairs(inventory.activePets) do
        if petId then
            pets.active[slot] = petId
        end
    end
    
    -- 獲取倉庫中的寵物
    pets.stored = inventory.storedPets
    
    return pets
end

-- 初始化系統
function PetInventorySystem.initialize()
    print("✅ 寵物背包管理系統初始化完成")
    print(string.format("📊 配置: 最大攜帶 %d 隻，最大跟隨 %d 隻", 
        INVENTORY_CONFIG.MAX_ACTIVE_PETS, 
        INVENTORY_CONFIG.MAX_FOLLOWING_PETS))
end

return PetInventorySystem
