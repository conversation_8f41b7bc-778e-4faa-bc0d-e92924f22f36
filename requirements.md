# 英雄寵物冒險遊戲 - 需求規格書

## 1. 遊戲概述

### 1.1 遊戲類型
- **類型**: 角色扮演 + 寵物收集 + 社交互動
- **平台**: Roblox (支援PC、手機、平板)
- **目標**: 創造一個耐玩的長期遊戲體驗

### 1.2 核心設計理念
- 🎯 **超級黏性**: 多層次成癮機制，讓玩家每天都想回來
- 💰 **高付費轉換**: 巧妙的付費點設計，讓玩家自願花錢
- 🔄 **持續參與**: 每日、週、月任務系統保持活躍度
- 👑 **VIP體驗**: 分層付費服務提供差異化體驗

### 1.3 核心玩法
- 英雄角色成長和裝備系統
- 寵物收集、培養和戰鬥
- 區域探索和BOSS挑戰
- 社交互動和玩家交易
- 多層次任務系統 (每日/週/月)
- VIP等級和會員制度

## 2. 英雄系統需求

### 2.1 英雄屬性
- **等級系統**: 1-100級，韓式慢速成長體驗
- **血量系統**: 隨等級提升，基礎公式 HP = 100 + (等級 * 50)
- **戰鬥屬性**: 攻擊力、防禦力、暴擊率、暴擊傷害
- **裝備系統**: 武器、防具、飾品槽位

### 2.2 裝備系統
- **武器類型**: 劍、法杖、弓箭等多種類型
- **品質等級**: 普通/稀有/史詩/神話/傳說 (5個等級)
- **屬性加成**: 攻擊力、血量、特殊效果
- **強化系統**: 裝備可升級提升屬性

### 2.3 英雄進度 (韓式慢速成長)
- **經驗值獲得**: 戰鬥、任務、每日活動
- **升級曲線**: 參考韓國天堂的慢速升級體驗
  - 1-10級: 相對快速 (新手體驗)
  - 11-30級: 開始變慢 (建立習慣)
  - 31-60級: 明顯變慢 (長期目標)
  - 61-100級: 極慢升級 (頂級挑戰)
- **技能解鎖**: 特定等級解鎖新技能
- **屬性成長**: 每級自動提升基礎屬性

## 3. 貨幣系統需求

### 3.1 貨幣類型
- **金幣**: 基礎貨幣，戰鬥和任務獲得
- **寶石**: 高級貨幣，充值或特殊活動獲得 (少量)
- **R幣**: ROBLOX官方貨幣，玩家充值購買 (不可贈送)
- **經驗值**: 英雄和寵物升級消耗

### 3.2 貨幣用途
- **金幣**: 購買基礎裝備、寵物食物、區域解鎖
- **寶石**: 購買高級扭蛋、稀有裝備、加速道具、補簽功能
- **R幣**: 購買VIP等級、Premium會員、限定商品
- **經驗值**: 直接用於角色和寵物升級

### 3.3 獲得方式
- **戰鬥獎勵**: 擊敗怪物和BOSS (金幣、經驗值)
- **每日登入**: 連續登入獎勵 (金幣、少量寶石)
- **任務完成**: 每日任務和成就任務 (金幣、少量寶石)
- **玩家交易**: 與其他玩家交換物品 (金幣)
- **充值購買**: R幣只能通過充值獲得

### 3.4 貨幣限制
- **R幣不可贈送**: 遊戲內無法獲得R幣，只能充值
- **寶石稀缺性**: 免費獲得的寶石數量有限，營造稀缺感
- **金幣通脹控制**: 合理的金幣產出和消耗平衡

## 4. 寵物系統需求 (怪物收集系統)

### 4.1 怪物-寵物統一設計
- **雙重身份**: 90+種生物既是地圖怪物，也是抽扭蛋寵物
- **遭遇系統**: 玩家在地圖探索時遭遇各種怪物
- **戰鬥預覽**: 與怪物戰鬥了解其能力和特色
- **收集動機**: 戰鬥後激發玩家收集該怪物的慾望
- **圖鑑解鎖**: 遭遇怪物後在圖鑑中解鎖該生物資訊

### 4.2 寵物分級系統
- **普通 (Common)**: 白色，基礎屬性，地圖常見怪物
- **稀有 (Rare)**: 綠色，1.5倍屬性加成，地圖精英怪物
- **史詩 (Epic)**: 藍色，2倍屬性加成，特殊技能，小BOSS
- **神話 (Mythic)**: 紫色，3倍屬性加成，強力技能，區域BOSS
- **傳說 (Legendary)**: 金色，5倍屬性加成，獨特技能，終極BOSS

### 4.3 寵物獲得方式 (純扭蛋制)
- **唯一獲得方式**: 寵物只能通過扭蛋獲得，無法直接捕捉
- **金幣扭蛋**: 1000金幣，主要獲得普通(70%)和稀有(25%)寵物
- **寶石扭蛋**: 100寶石，提高史詩(35%)和神話(20%)機率
- **R幣扭蛋**: 50R幣，保證稀有以上，傳說機率5%
- **區域限定**: 不同區域扭蛋池包含該區域特有怪物
- **新手歡迎**: 遊戲開始免費獲得，3隻普通寵物選1隻

### 4.4 寵物屬性
- **等級系統**: 1-100級，與英雄同步成長
- **血量系統**: 基礎公式 HP = 50 + (等級 * 25) * 稀有度倍數
- **戰鬥屬性**: 攻擊力、防禦力、速度、技能威力
- **技能系統**: 每個寵物1-3個主動/被動技能

### 4.5 寵物管理
- **背包容量**: 最多同時5隻寵物，只能攜帶跟隨1隻寵物參與戰鬥
- **倉庫系統**: 可儲存更多寵物，但不參與戰鬥
- **管理功能**: 查看屬性、升級、技能管理、重命名
- **釋放機制**: 可釋放重複寵物獲得金幣和材料
- **圖鑑系統**: 記錄所有遭遇和擁有的寵物

## 5. 戰鬥系統需求 (怪物遭遇系統)

### 5.1 怪物遭遇機制
- **地圖生成**: 90+種怪物隨機出現在3個區域地圖上
- **稀有度分布**:
  - 普通怪物(70%): 經常出現，容易擊敗
  - 稀有怪物(20%): 較少出現，中等難度
  - 史詩怪物(8%): 稀少出現，需要策略
  - 神話怪物(2%): 極稀少，高難度挑戰
  - 傳說怪物(<1%): 極罕見，終極挑戰
- **區域分布**: 不同區域出現不同主題的怪物
- **刷新機制**: 怪物被擊敗後定時重新生成

### 5.2 戰鬥機制
- **回合制戰鬥**: 英雄和寵物輪流行動
- **技能系統**: 主動技能和被動效果
- **組合攻擊**: 英雄和寵物的配合技能
- **屬性克制**: 不同屬性間的相剋關係
- **戰鬥獎勵**: 擊敗怪物獲得金幣、經驗值、材料
- **圖鑑解鎖**: 首次遭遇怪物時解鎖圖鑑條目

### 5.3 戰鬥獎勵
- **經驗值**: 英雄和參戰寵物獲得
- **金幣**: 基礎戰鬥獎勵
- **裝備**: 機率掉落各品質裝備
- **材料**: 強化和進化材料

## 6. 區域系統需求

### 6.1 區域解鎖
- **解鎖條件**: 英雄等級 + 金幣消耗
- **漸進式開放**: 完成前一區域的主要任務
- **特殊區域**: 需要特定條件或道具

### 6.2 區域特色
- **環境主題**: 森林、沙漠、雪山、火山等
- **專屬怪物**: 每個區域獨特的怪物類型
- **特殊資源**: 區域限定的材料和道具
- **BOSS挑戰**: 每個區域的終極BOSS

## 7. 每日活動與任務系統

### 7.1 每日登入獎勵 (30天循環)
- **30天獎勵週期**: 完整的月度獎勵循環，第30天重置
- **獎勵遞增**: 每天獎勵價值逐漸提升
- **關鍵節點獎勵**: 第7、14、21、30天提供特殊大獎
- **獎勵類型**: 金幣、寶石、商城幣、稀有寵物、裝備
- **補簽功能**: 使用寶石補簽遺漏天數 (價格遞增)
- **VIP加成**: VIP玩家獲得額外獎勵倍數

### 7.2 每日任務系統
- **基礎任務**: 戰鬥怪物、收集資源、升級寵物等 (3-5個)
- **獎勵機制**: 完成獲得經驗值、金幣、任務積分
- **活躍度系統**: 累積活躍度解鎖寶箱獎勵
- **任務刷新**: 每日0點自動刷新，可花費寶石手動刷新

### 7.3 週任務系統
- **週度挑戰**: 更具挑戰性的長期目標 (5-7個)
- **任務類型**: 累積戰鬥次數、收集特定寵物、完成區域探索
- **豐厚獎勵**: 稀有裝備、高級扭蛋券、大量貨幣
- **進度保留**: 週任務進度在週內累積，週一重置

### 7.4 月任務系統
- **月度目標**: 最具挑戰性的長期成就 (3-5個)
- **任務內容**: 達到特定等級、收集傳說寵物、完成困難副本
- **頂級獎勵**: 限定寵物、傳說裝備、大量寶石
- **賽季概念**: 每月主題不同，提供獨特獎勵

## 8. VIP系統與付費機制

### 8.1 VIP等級系統 (10級)
- **等級劃分**: VIP 0-10，累積充值金額提升等級
- **升級門檻**: VIP1($5) → VIP2($15) → VIP3($30) → ... → VIP10($1000)
- **永久特權**:
  - 每日登入獎勵倍數 (VIP1: 1.2倍 → VIP10: 3倍)
  - 扭蛋折扣 (VIP1: 5%折扣 → VIP10: 30%折扣)
  - 每日任務額外獎勵
  - 專屬VIP商店和限定寵物
  - 戰鬥經驗加成 (VIP1: 10% → VIP10: 100%)

### 8.2 月費會員 (Premium Pass)
- **月費價格**: $4.99/月
- **會員特權**:
  - 每日額外寶石 (50顆)
  - 專屬每日任務 (獎勵更豐厚)
  - 月度專屬寵物扭蛋
  - 交易手續費減免
  - 優先客服支援
- **會員商店**: 專屬道具和限定寵物
- **累積獎勵**: 連續訂閱獲得額外獎勵

### 8.3 新手引導付費
- **新手歡迎禮**: 登入即送3選1普通寵物 (免費)
- **首次付費優惠**: 新玩家24小時內首購50%折扣
- **新手成長包**: 80 R幣超值新手包 (包含稀有寵物+資源)
- **進度卡點**: 在關鍵進度點推薦付費解決方案
- **挫折救援**: 檢測玩家挫折時推送幫助道具
- **限時優惠**: 新手7天內的階段性優惠包

## 9. 社交系統需求

### 9.1 玩家交易
- **物品交易**: 裝備、材料、道具交換
- **寵物交易**: 寵物買賣和交換
- **拍賣系統**: 公開競價稀有物品
- **交易稅收**: 防止經濟通脹的調節機制 (VIP減免)

### 9.2 社交互動
- **好友系統**: 添加好友、查看狀態
- **公會系統**: 創建或加入公會
- **聊天系統**: 世界、公會、私聊頻道
- **排行榜**: 等級、戰力、財富、VIP等級排行

## 10. 付費轉換優化策略

### 10.1 心理觸發機制
- **稀缺性營造**: 限時優惠、限量商品
- **社交壓力**: 好友對比、排行榜競爭
- **收集慾望**: 寵物圖鑑完成度展示
- **即時滿足**: 付費後立即獲得強力提升

### 10.2 付費點設計
- **能力提升**: 戰鬥力快速提升道具
- **時間節省**: 跳過等待時間、快速升級
- **稀有獲取**: 限定寵物、傳說裝備
- **便利功能**: 自動戰鬥、批量操作
- **社交地位**: VIP標識、專屬稱號

### 10.3 價格策略
- **階梯定價**: $0.99, $4.99, $9.99, $19.99, $49.99, $99.99
- **價值包裝**: 組合包比單買更優惠
- **首購優惠**: 新玩家特殊折扣
- **節日促銷**: 特殊節日限時優惠

## 11. 遊戲平衡需求

### 11.1 經濟平衡
- **通脹控制**: 貨幣產出和消耗平衡
- **稀有度控制**: 高級物品的稀缺性
- **交易限制**: 防止經濟操控的機制
- **VIP平衡**: VIP優勢不能過於壓制免費玩家

### 11.2 戰鬥平衡
- **等級差距**: 合理的等級壓制機制
- **寵物平衡**: 各稀有度寵物的使用價值
- **技能平衡**: 避免過於強勢的技能組合
- **付費平衡**: 付費玩家有優勢但不能完全碾壓

### 11.3 進度平衡 (韓式慢速成長)
- **升級曲線**: 參考韓國天堂的極慢升級體驗
  - 前期 (1-10級): 快速升級建立信心
  - 中期 (11-50級): 逐漸變慢，每級需要更多時間
  - 後期 (51-100級): 極慢升級，每級可能需要數天甚至數週
- **經驗值需求**: 指數級增長，營造長期目標感
- **內容節奏**: 新內容解鎖間隔較長，增加期待感
- **長期目標**: 保持玩家長期遊戲動機
- **付費節奏**: 合理的付費誘導頻率，不破壞慢節奏體驗

## 12. 數據分析與運營需求

### 12.1 關鍵指標追蹤
- **留存率**: 1日、3日、7日、30日留存
- **付費轉換**: 首次付費率、付費用戶比例
- **ARPU/ARPPU**: 平均每用戶收入
- **LTV**: 用戶生命週期價值
- **任務完成率**: 各類任務的完成情況

### 12.2 行為分析
- **遊戲流程**: 玩家在各階段的流失點
- **付費行為**: 付費觸發點和付費路徑
- **社交互動**: 好友、公會活躍度
- **內容消耗**: 各區域、任務的參與度

### 12.3 運營工具
- **活動配置**: 後台配置各種活動和獎勵
- **推送系統**: 個性化消息推送
- **客服系統**: 玩家問題處理和反饋
- **版本控制**: A/B測試和灰度發布

## 13. 技術需求

### 13.1 核心技術棧
- **開發語言**: Lua (Roblox 原生)
- **核心架構**: Matter (ECS 實體組件系統)
- **網路通信**: Knit (伺服器-客戶端通信框架)
- **數據持久化**: ProfileService (玩家和寵物資料)
- **表格處理**: TableUtil (表格操作工具)
- **UI 框架**: Fusion (寵物清單和公會介面、英雄介面、裝備)
- **區域管理**: ZonePlus (狩獵場和城堡區域)
- **事件處理**: Signal (PvP 和寵物召喚)
- **測試框架**: TestEZ + Matter 的除錯視圖

### 13.2 系統架構
- **ECS 架構 (Matter)**:
  - 實體 (Entities): 玩家、寵物、怪物、裝備等遊戲對象
  - 組件 (Components): 屬性、狀態、位置等數據組件
  - 系統 (Systems): 戰鬥、移動、升級等邏輯系統
- **網路層 (Knit)**:
  - 服務端服務: 遊戲邏輯和數據管理
  - 客戶端控制器: UI 和本地邏輯
  - 自動化的網路通信和同步
- **UI 層 (Fusion)**:
  - 響應式 UI 組件
  - 狀態驅動的界面更新
  - 高性能的 UI 渲染
- **區域系統 (ZonePlus)**:
  - 動態區域檢測和管理
  - 玩家進入/離開區域事件
  - 區域特定的遊戲邏輯

### 13.3 性能要求
- **流暢運行**: 支援低端設備60FPS
- **網路優化**: 最小化延遲和數據傳輸
- **記憶體管理**: 有效的資源載入和釋放
- **付費處理**: 安全可靠的支付系統整合

### 13.4 數據安全
- **防作弊**: 服務器端驗證所有關鍵操作
- **數據備份**: 定期備份玩家數據
- **異常檢測**: 自動檢測和處理異常數據
- **支付安全**: 防止支付欺詐和退款濫用

### 13.5 擴展性
- **模組化設計**: 便於添加新功能
- **配置驅動**: 通過配置文件調整遊戲參數
- **版本兼容**: 支援遊戲更新和數據遷移
- **多語言支持**: 支援國際化擴展
