-- 怪物服務
-- 整合怪物生成、遭遇檢測、扭蛋系統、寵物背包管理

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local Knit = require(ReplicatedStorage.Packages.Knit)
local Matter = require(ReplicatedStorage.Packages.Matter)

-- 導入系統
local MonsterSpawnSystem = require(script.Parent.Parent.Systems.MonsterSpawnSystem)
local EncounterSystem = require(script.Parent.Parent.Systems.EncounterSystem)
local GachaSystem = require(script.Parent.Parent.Systems.GachaSystem)
local PetInventorySystem = require(script.Parent.Parent.Systems.PetInventorySystem)

local MonsterService = Knit.CreateService({
    Name = "MonsterService",
    Client = {
        -- 扭蛋相關
        RollGacha = Knit.CreateSignal(),
        RollMultipleGacha = Knit.CreateSignal(),
        GetGachaInfo = Knit.CreateSignal(),
        
        -- 寵物背包相關
        EquipPet = Knit.CreateSignal(),
        UnequipPet = Knit.CreateSignal(),
        SetFollowingPet = Knit.CreateSignal(),
        StopFollowing = Knit.CreateSignal(),
        GetPlayerPets = Knit.CreateSignal(),
        
        -- 圖鑑相關
        GetPokedexStats = Knit.CreateSignal(),
        GetEncounterHistory = Knit.CreateSignal(),
        
        -- 事件
        OnPetObtained = Knit.CreateSignal(),
        OnFirstEncounter = Knit.CreateSignal(),
        OnMonsterDefeated = Knit.CreateSignal(),
        OnPetEquipped = Knit.CreateSignal()
    }
})

-- ECS世界
local world = nil

function MonsterService:KnitInit()
    -- 創建ECS世界
    world = Matter.World.new()
    
    -- 初始化系統
    MonsterSpawnSystem.initialize(world)
    EncounterSystem.initialize()
    GachaSystem.initialize()
    PetInventorySystem.initialize()
    
    print("✅ MonsterService 初始化完成")
end

function MonsterService:KnitStart()
    -- 連接系統事件到客戶端事件
    self:_connectSystemEvents()
    
    -- 啟動ECS循環
    self:_startECSLoop()
    
    print("✅ MonsterService 啟動完成")
end

-- 連接系統事件
function MonsterService:_connectSystemEvents()
    -- 扭蛋事件
    GachaSystem.onPetObtained:Connect(function(playerId, speciesId, rarity, gachaType)
        local player = Players:GetPlayerByUserId(playerId)
        if player then
            self.Client.OnPetObtained:Fire(player, {
                speciesId = speciesId,
                rarity = rarity,
                gachaType = gachaType
            })
        end
    end)
    
    -- 遭遇事件
    EncounterSystem.onFirstEncounter:Connect(function(player, monster, entryId)
        self.Client.OnFirstEncounter:Fire(player, {
            speciesId = monster.speciesId,
            rarity = monster.rarity,
            zone = monster.zone
        })
    end)
    
    EncounterSystem.onMonsterDefeated:Connect(function(player, monster, defeatCount)
        self.Client.OnMonsterDefeated:Fire(player, {
            speciesId = monster.speciesId,
            rarity = monster.rarity,
            defeatCount = defeatCount
        })
    end)
    
    -- 寵物背包事件
    PetInventorySystem.onPetEquipped:Connect(function(playerId, petId, slot)
        local player = Players:GetPlayerByUserId(playerId)
        if player then
            self.Client.OnPetEquipped:Fire(player, {
                petId = petId,
                slot = slot
            })
        end
    end)
end

-- 啟動ECS循環
function MonsterService:_startECSLoop()
    local RunService = game:GetService("RunService")
    
    RunService.Heartbeat:Connect(function()
        -- 運行所有系統
        MonsterSpawnSystem.step(world, {})
        EncounterSystem.step(world, {})
    end)
end

-- 客戶端方法：扭蛋
function MonsterService.Client:RollGacha(player, gachaType)
    local playerId = player.UserId
    local result, error = GachaSystem.rollGacha(world, playerId, gachaType)
    
    if result then
        -- 自動添加到背包
        PetInventorySystem.addPetToInventory(world, playerId, result.petId)
        return result
    else
        error("扭蛋失敗: " .. (error or "未知錯誤"))
    end
end

-- 客戶端方法：批量扭蛋
function MonsterService.Client:RollMultipleGacha(player, gachaType, count)
    local playerId = player.UserId
    local results, errors = GachaSystem.rollMultipleGacha(world, playerId, gachaType, count)
    
    -- 自動添加所有寵物到背包
    for _, result in ipairs(results) do
        PetInventorySystem.addPetToInventory(world, playerId, result.petId)
    end
    
    return {
        results = results,
        errors = errors
    }
end

-- 客戶端方法：獲取扭蛋信息
function MonsterService.Client:GetGachaInfo(player, gachaType)
    if gachaType then
        return GachaSystem.getGachaInfo(gachaType)
    else
        return GachaSystem.getAllGachaTypes()
    end
end

-- 客戶端方法：裝備寵物
function MonsterService.Client:EquipPet(player, petId, slot)
    local playerId = player.UserId
    local success, error = PetInventorySystem.equipPet(world, playerId, petId, slot)
    
    if not success then
        error("裝備寵物失敗: " .. (error or "未知錯誤"))
    end
    
    return success
end

-- 客戶端方法：卸下寵物
function MonsterService.Client:UnequipPet(player, slot)
    local playerId = player.UserId
    local success, error = PetInventorySystem.unequipPet(world, playerId, slot)
    
    if not success then
        error("卸下寵物失敗: " .. (error or "未知錯誤"))
    end
    
    return success
end

-- 客戶端方法：設置跟隨寵物
function MonsterService.Client:SetFollowingPet(player, petId)
    local playerId = player.UserId
    local success, error = PetInventorySystem.setFollowingPet(world, playerId, petId)
    
    if not success then
        error("設置跟隨寵物失敗: " .. (error or "未知錯誤"))
    end
    
    return success
end

-- 客戶端方法：停止跟隨
function MonsterService.Client:StopFollowing(player)
    local playerId = player.UserId
    local success, error = PetInventorySystem.stopFollowing(world, playerId)
    
    if not success then
        error("停止跟隨失敗: " .. (error or "未知錯誤"))
    end
    
    return success
end

-- 客戶端方法：獲取玩家寵物
function MonsterService.Client:GetPlayerPets(player)
    local playerId = player.UserId
    return PetInventorySystem.getPlayerPets(world, playerId)
end

-- 客戶端方法：獲取圖鑑統計
function MonsterService.Client:GetPokedexStats(player)
    local playerId = player.UserId
    return EncounterSystem.getPokedexStats(world, playerId)
end

-- 客戶端方法：獲取遭遇歷史
function MonsterService.Client:GetEncounterHistory(player, speciesId)
    local playerId = player.UserId
    
    -- 獲取特定物種的遭遇記錄
    for entityId, record in world:query(require(ReplicatedStorage.Components.MonsterComponents).EncounterRecord) do
        if record.playerId == playerId and (not speciesId or record.speciesId == speciesId) then
            return {
                speciesId = record.speciesId,
                firstEncounterTime = record.firstEncounterTime,
                totalEncounters = record.totalEncounters,
                totalDefeated = record.totalDefeated,
                lastEncounterTime = record.lastEncounterTime
            }
        end
    end
    
    return nil
end

-- 服務端方法：手動生成怪物（用於測試）
function MonsterService:SpawnTestMonster(zone, rarity)
    if not world then return nil end
    
    local spawnPoints = {
        FOREST = Vector3.new(100, 5, 100),
        ICE = Vector3.new(500, 5, 100),
        LAVA = Vector3.new(800, 5, 100)
    }
    
    local position = spawnPoints[zone] or Vector3.new(0, 5, 0)
    return MonsterSpawnSystem.spawnMonster(world, zone, position, rarity or "COMMON")
end

-- 服務端方法：獲取世界統計
function MonsterService:GetWorldStats()
    if not world then return {} end
    
    local stats = {
        totalMonsters = 0,
        monstersByZone = {},
        monstersByRarity = {}
    }
    
    for entityId, monster in world:query(require(ReplicatedStorage.Components.MonsterComponents).Monster) do
        stats.totalMonsters = stats.totalMonsters + 1
        
        -- 按區域統計
        stats.monstersByZone[monster.zone] = (stats.monstersByZone[monster.zone] or 0) + 1
        
        -- 按稀有度統計
        stats.monstersByRarity[monster.rarity] = (stats.monstersByRarity[monster.rarity] or 0) + 1
    end
    
    return stats
end

return MonsterService
