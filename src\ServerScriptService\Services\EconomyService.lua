-- 經濟服務 - 處理遊戲內經濟系統
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local EconomyService = Knit.CreateService({
    Name = "EconomyService",
    Client = {
        -- 客戶端可調用的方法
        GetCurrencies = Knit.CreateSignal(),
        AddCurrency = Knit.CreateSignal(),
        SpendCurrency = Knit.CreateSignal(),
        ExchangeCurrency = Knit.CreateSignal(),
        GetShopItems = Knit.CreateSignal(),
        PurchaseItem = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnCurrencyChanged = Knit.CreateSignal(),
        OnPurchaseCompleted = Knit.CreateSignal(),
        OnInsufficientFunds = Knit.CreateSignal(),
        OnDailyRewardClaimed = Knit.CreateSignal()
    }
})

function EconomyService:KnitInit()
    self._worldManager = _G.WorldManager
    self._world = _G.ECSWorld
    
    -- 商店物品配置
    self._shopItems = {
        -- 金幣商店
        coins = {
            {
                id = "health_potion",
                name = "血量藥水",
                description = "立即回復 50 血量",
                price = 100,
                currency = "coins",
                category = "consumable"
            },
            {
                id = "exp_boost",
                name = "經驗加成",
                description = "30分鐘內經驗值 +50%",
                price = 500,
                currency = "coins",
                category = "boost"
            }
        },
        -- 寶石商店
        gems = {
            {
                id = "premium_egg",
                name = "高級扭蛋",
                description = "保證稀有以上寵物",
                price = 50,
                currency = "gems",
                category = "gacha"
            },
            {
                id = "pet_slot",
                name = "寵物欄位",
                description = "增加 1 個寵物攜帶欄位",
                price = 100,
                currency = "gems",
                category = "upgrade"
            }
        },
        -- R幣商店
        robux = {
            {
                id = "vip_pass",
                name = "VIP 通行證",
                description = "30天 VIP 特權",
                price = 199,
                currency = "robux",
                category = "vip"
            }
        }
    }
    
    print("💰 EconomyService 初始化完成")
end

function EconomyService:KnitStart()
    print("🎮 EconomyService 啟動")
end

-- 客戶端方法：獲取貨幣
function EconomyService.Client:GetCurrencies(player)
    return self.Server:GetCurrencies(player)
end

function EconomyService:GetCurrencies(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if not currency then
        return { success = false, error = "貨幣數據未找到" }
    end
    
    return {
        success = true,
        currencies = currency
    }
end

-- 客戶端方法：添加貨幣 (管理員功能)
function EconomyService.Client:AddCurrency(player, currencyType, amount)
    return self.Server:AddCurrency(player, currencyType, amount)
end

function EconomyService:AddCurrency(player, currencyType, amount)
    -- 這裡應該添加權限檢查
    if not self:isAdmin(player) then
        return { success = false, error = "權限不足" }
    end
    
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if not currency then
        return { success = false, error = "貨幣數據未找到" }
    end
    
    if not currency[currencyType] then
        return { success = false, error = "無效的貨幣類型" }
    end
    
    local newCurrency = Utils.deepCopy(currency)
    newCurrency[currencyType] = newCurrency[currencyType] + amount
    
    self._world:insert(playerEntity, Components.CurrencyComponent(newCurrency))
    
    -- 通知客戶端
    self.Client.OnCurrencyChanged:Fire(player, {
        currencyType = currencyType,
        oldAmount = currency[currencyType],
        newAmount = newCurrency[currencyType],
        change = amount,
        reason = "admin_add",
        timestamp = tick()
    })
    
    print("💰 管理員給予貨幣:", player.Name, currencyType, amount)
    
    return {
        success = true,
        newAmount = newCurrency[currencyType]
    }
end

-- 客戶端方法：消費貨幣
function EconomyService.Client:SpendCurrency(player, currencyType, amount, reason)
    return self.Server:SpendCurrency(player, currencyType, amount, reason)
end

function EconomyService:SpendCurrency(player, currencyType, amount, reason)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if not currency then
        return { success = false, error = "貨幣數據未找到" }
    end
    
    if not currency[currencyType] then
        return { success = false, error = "無效的貨幣類型" }
    end
    
    if currency[currencyType] < amount then
        self.Client.OnInsufficientFunds:Fire(player, {
            currencyType = currencyType,
            required = amount,
            current = currency[currencyType],
            timestamp = tick()
        })
        
        return { 
            success = false, 
            error = "貨幣不足",
            required = amount,
            current = currency[currencyType]
        }
    end
    
    local newCurrency = Utils.deepCopy(currency)
    newCurrency[currencyType] = newCurrency[currencyType] - amount
    
    self._world:insert(playerEntity, Components.CurrencyComponent(newCurrency))
    
    -- 通知客戶端
    self.Client.OnCurrencyChanged:Fire(player, {
        currencyType = currencyType,
        oldAmount = currency[currencyType],
        newAmount = newCurrency[currencyType],
        change = -amount,
        reason = reason or "purchase",
        timestamp = tick()
    })
    
    return {
        success = true,
        newAmount = newCurrency[currencyType]
    }
end

-- 客戶端方法：貨幣兌換
function EconomyService.Client:ExchangeCurrency(player, fromCurrency, toCurrency, amount)
    return self.Server:ExchangeCurrency(player, fromCurrency, toCurrency, amount)
end

function EconomyService:ExchangeCurrency(player, fromCurrency, toCurrency, amount)
    -- 定義兌換率
    local exchangeRates = {
        coins_to_gems = 1000, -- 1000 金幣 = 1 寶石
        gems_to_coins = 800   -- 1 寶石 = 800 金幣 (有手續費)
    }
    
    local rateKey = fromCurrency .. "_to_" .. toCurrency
    local rate = exchangeRates[rateKey]
    
    if not rate then
        return { success = false, error = "不支援的兌換類型" }
    end
    
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if not currency then
        return { success = false, error = "貨幣數據未找到" }
    end
    
    local requiredAmount = amount * rate
    if currency[fromCurrency] < requiredAmount then
        return { 
            success = false, 
            error = "貨幣不足",
            required = requiredAmount,
            current = currency[fromCurrency]
        }
    end
    
    local newCurrency = Utils.deepCopy(currency)
    newCurrency[fromCurrency] = newCurrency[fromCurrency] - requiredAmount
    newCurrency[toCurrency] = newCurrency[toCurrency] + amount
    
    self._world:insert(playerEntity, Components.CurrencyComponent(newCurrency))
    
    -- 通知客戶端
    self.Client.OnCurrencyChanged:Fire(player, {
        currencyType = fromCurrency,
        oldAmount = currency[fromCurrency],
        newAmount = newCurrency[fromCurrency],
        change = -requiredAmount,
        reason = "exchange",
        timestamp = tick()
    })
    
    self.Client.OnCurrencyChanged:Fire(player, {
        currencyType = toCurrency,
        oldAmount = currency[toCurrency],
        newAmount = newCurrency[toCurrency],
        change = amount,
        reason = "exchange",
        timestamp = tick()
    })
    
    print("💱 貨幣兌換:", player.Name, requiredAmount, fromCurrency, "->", amount, toCurrency)
    
    return {
        success = true,
        exchanged = {
            from = { type = fromCurrency, amount = requiredAmount },
            to = { type = toCurrency, amount = amount }
        }
    }
end

-- 客戶端方法：獲取商店物品
function EconomyService.Client:GetShopItems(player, category)
    return self.Server:GetShopItems(player, category)
end

function EconomyService:GetShopItems(player, category)
    if category and self._shopItems[category] then
        return {
            success = true,
            items = self._shopItems[category]
        }
    else
        return {
            success = true,
            items = self._shopItems
        }
    end
end

-- 客戶端方法：購買物品
function EconomyService.Client:PurchaseItem(player, itemId, quantity)
    return self.Server:PurchaseItem(player, itemId, quantity)
end

function EconomyService:PurchaseItem(player, itemId, quantity)
    quantity = quantity or 1
    
    -- 查找物品
    local item = self:findShopItem(itemId)
    if not item then
        return { success = false, error = "物品不存在" }
    end
    
    local totalCost = item.price * quantity
    
    -- 檢查並扣除貨幣
    local spendResult = self:SpendCurrency(player, item.currency, totalCost, "shop_purchase")
    if not spendResult.success then
        return spendResult
    end
    
    -- 給予物品效果
    local effectResult = self:applyItemEffect(player, item, quantity)
    
    -- 通知客戶端購買完成
    self.Client.OnPurchaseCompleted:Fire(player, {
        itemId = itemId,
        itemName = item.name,
        quantity = quantity,
        totalCost = totalCost,
        currency = item.currency,
        effect = effectResult,
        timestamp = tick()
    })
    
    print("🛒 購買物品:", player.Name, item.name, "x" .. quantity, "花費:", totalCost, item.currency)
    
    return {
        success = true,
        item = item,
        quantity = quantity,
        totalCost = totalCost,
        effect = effectResult
    }
end

-- 查找商店物品
function EconomyService:findShopItem(itemId)
    for category, items in pairs(self._shopItems) do
        for _, item in ipairs(items) do
            if item.id == itemId then
                return item
            end
        end
    end
    return nil
end

-- 應用物品效果
function EconomyService:applyItemEffect(player, item, quantity)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    if item.id == "health_potion" then
        -- 血量藥水
        local health = self._world:get(playerEntity, Components.HealthComponent)
        local hero = self._world:get(playerEntity, Components.HeroComponent)
        
        if health and hero then
            local healAmount = 50 * quantity
            local newHealth = math.min(hero.maxHealth, health.current + healAmount)
            
            self._world:insert(playerEntity, Components.HealthComponent({
                current = newHealth,
                maximum = health.maximum,
                regeneration = health.regeneration,
                lastRegenTime = health.lastRegenTime
            }))
            
            return { success = true, effect = "healed", amount = newHealth - health.current }
        end
        
    elseif item.id == "exp_boost" then
        -- 經驗加成 (這裡可以添加狀態效果)
        return { success = true, effect = "exp_boost", duration = 1800 } -- 30分鐘
        
    end
    
    return { success = true, effect = "none" }
end

-- 檢查是否為管理員
function EconomyService:isAdmin(player)
    -- 這裡應該實現真正的管理員檢查邏輯
    -- 暫時返回 false
    return false
end

-- 每日獎勵系統
function EconomyService:claimDailyReward(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    -- 這裡可以添加每日獎勵邏輯
    local dailyReward = {
        coins = 500,
        gems = 5
    }
    
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if currency then
        local newCurrency = Utils.deepCopy(currency)
        newCurrency.coins = newCurrency.coins + dailyReward.coins
        newCurrency.gems = newCurrency.gems + dailyReward.gems
        
        self._world:insert(playerEntity, Components.CurrencyComponent(newCurrency))
        
        self.Client.OnDailyRewardClaimed:Fire(player, {
            rewards = dailyReward,
            timestamp = tick()
        })
        
        return { success = true, rewards = dailyReward }
    end
    
    return { success = false, error = "無法領取獎勵" }
end

return EconomyService
