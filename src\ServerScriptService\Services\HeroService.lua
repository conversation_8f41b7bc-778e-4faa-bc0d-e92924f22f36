-- 英雄服務 - 服務端網路處理
local HeroService = {}

-- 依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local HeroManager = require(game.ReplicatedStorage.Shared.HeroManager)
local HeroConfig = require(game.ReplicatedStorage.Configuration.HeroConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 創建 Knit 服務
HeroService = Knit.CreateService({
    Name = "HeroService",
    
    -- 客戶端可調用的方法
    Client = {
        -- 獲取英雄數據
        GetHeroData = Knit.CreateSignal(),
        
        -- 創建英雄
        CreateHero = Knit.CreateSignal(),
        
        -- 獲取英雄統計
        GetHeroStats = Knit.CreateSignal(),
        
        -- 使用技能
        UseSkill = Knit.CreateSignal(),
        
        -- 復活英雄
        ResurrectHero = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnHeroDataUpdated = Knit.CreateSignal(),
        OnHeroLevelUp = Knit.CreateSignal(),
        OnHeroDied = Knit.CreateSignal(),
        OnHeroResurrected = Knit.CreateSignal(),
        OnHeroGainedExp = Knit.CreateSignal(),
        OnHeroDamaged = Knit.CreateSignal(),
        OnHeroHealed = Knit.CreateSignal(),
        OnCriticalHit = Knit.CreateSignal()
    }
})

-- 服務初始化
function HeroService:KnitInit()
    print("🦸 初始化英雄服務...")
    
    -- 初始化英雄管理器
    HeroManager:Initialize()
    
    -- 設置事件監聽
    self:SetupEventListeners()
end

-- 服務啟動
function HeroService:KnitStart()
    print("✅ 英雄服務啟動完成")
end

-- 設置事件監聽
function HeroService:SetupEventListeners()
    -- 監聽英雄事件並轉發給客戶端
    EventManager:Connect(EventManager.EventTypes.HERO_LEVEL_UP, function(data)
        self.Client.OnHeroLevelUp:Fire(data.player, {
            oldLevel = data.oldLevel,
            newLevel = data.newLevel,
            heroData = self:SanitizeHeroData(data.heroData)
        })
    end)
    
    EventManager:Connect(EventManager.EventTypes.HERO_DIED, function(data)
        self.Client.OnHeroDied:Fire(data.player, {
            expLoss = data.expLoss
        })
    end)
    
    EventManager:Connect(EventManager.EventTypes.HERO_RESURRECTED, function(data)
        self.Client.OnHeroResurrected:Fire(data.player, {
            method = data.method
        })
    end)
    
    EventManager:Connect(EventManager.EventTypes.HERO_GAINED_EXP, function(data)
        self.Client.OnHeroGainedExp:Fire(data.player, {
            expGained = data.expGained,
            totalExp = data.totalExp,
            level = data.level
        })
    end)
    
    EventManager:Connect(EventManager.EventTypes.HERO_DAMAGED, function(data)
        self.Client.OnHeroDamaged:Fire(data.target, {
            attacker = data.attacker.Name,
            damage = data.damage,
            damageType = data.damageType,
            currentHealth = data.targetHealth
        })
        
        -- 也通知攻擊者
        if data.attacker ~= data.target then
            self.Client.OnHeroDamaged:Fire(data.attacker, {
                target = data.target.Name,
                damage = data.damage,
                damageType = data.damageType,
                targetHealth = data.targetHealth
            })
        end
    end)
    
    EventManager:Connect(EventManager.EventTypes.HERO_HEALED, function(data)
        self.Client.OnHeroHealed:Fire(data.target, {
            healer = data.healer.Name,
            healAmount = data.healAmount,
            healType = data.healType,
            currentHealth = data.targetHealth
        })
        
        -- 也通知治療者
        if data.healer ~= data.target then
            self.Client.OnHeroHealed:Fire(data.healer, {
                target = data.target.Name,
                healAmount = data.healAmount,
                healType = data.healType,
                targetHealth = data.targetHealth
            })
        end
    end)
    
    EventManager:Connect(EventManager.EventTypes.CRITICAL_HIT, function(data)
        self.Client.OnCriticalHit:Fire(data.attacker, {
            target = data.target.Name,
            damage = data.damage
        })
    end)
end

-- 客戶端方法：獲取英雄數據
function HeroService.Client:GetHeroData(player)
    local heroData = HeroManager:GetHeroData(player)
    if heroData then
        return self.Server:SanitizeHeroData(heroData)
    end
    return nil
end

-- 客戶端方法：創建英雄
function HeroService.Client:CreateHero(player, heroClass)
    -- 驗證職業
    if not HeroConfig.CLASSES[heroClass] then
        return {
            success = false,
            error = "無效的英雄職業"
        }
    end
    
    -- 檢查是否已有英雄
    local existingHero = HeroManager:GetHeroData(player)
    if existingHero then
        return {
            success = false,
            error = "玩家已有英雄"
        }
    end
    
    -- 創建英雄
    local heroData = HeroManager:CreateDefaultHero(player, heroClass)
    
    return {
        success = true,
        heroData = self.Server:SanitizeHeroData(heroData)
    }
end

-- 客戶端方法：獲取英雄統計
function HeroService.Client:GetHeroStats(player)
    local heroData = HeroManager:GetHeroData(player)
    if not heroData then
        return nil
    end
    
    local currentStats = HeroManager:GetCurrentStats(player)
    
    return {
        basicInfo = {
            class = heroData.class,
            level = heroData.level,
            experience = heroData.experience,
            nextLevelExp = HeroConfig.LEVEL_SYSTEM.getTotalExpForLevel(heroData.level + 1)
        },
        currentStats = currentStats,
        health = {
            current = heroData.currentHealth,
            max = currentStats.maxHealth
        },
        mana = {
            current = heroData.currentMana,
            max = currentStats.maxMana
        },
        status = {
            baseState = heroData.baseState,
            combatState = heroData.combatState
        },
        statistics = heroData.stats
    }
end

-- 客戶端方法：使用技能
function HeroService.Client:UseSkill(player, skillName, targetPlayer)
    local heroData = HeroManager:GetHeroData(player)
    if not heroData then
        return {
            success = false,
            error = "英雄數據不存在"
        }
    end
    
    -- 檢查英雄狀態
    if heroData.baseState ~= HeroConfig.STATUS_SYSTEM.baseStates.ALIVE then
        return {
            success = false,
            error = "英雄已死亡"
        }
    end
    
    -- 檢查技能是否存在
    local hasSkill = false
    for _, skill in ipairs(heroData.skills) do
        if skill == skillName then
            hasSkill = true
            break
        end
    end
    
    if not hasSkill then
        return {
            success = false,
            error = "英雄沒有此技能"
        }
    end
    
    -- 執行技能邏輯
    local result = self.Server:ExecuteSkill(player, skillName, targetPlayer)
    
    return result
end

-- 客戶端方法：復活英雄
function HeroService.Client:ResurrectHero(player, method)
    local heroData = HeroManager:GetHeroData(player)
    if not heroData then
        return {
            success = false,
            error = "英雄數據不存在"
        }
    end
    
    if heroData.baseState ~= HeroConfig.STATUS_SYSTEM.baseStates.DEAD then
        return {
            success = false,
            error = "英雄未死亡"
        }
    end
    
    method = method or "AUTO_REVIVE"
    local success = HeroManager:Resurrect(player, method)
    
    return {
        success = success,
        error = success and nil or "復活失敗"
    }
end

-- 服務端方法：執行技能
function HeroService:ExecuteSkill(player, skillName, targetPlayer)
    -- 這裡實現具體的技能邏輯
    -- 根據技能名稱執行不同的效果
    
    if skillName == "Heal" then
        return self:ExecuteHealSkill(player, targetPlayer)
    elseif skillName == "Fireball" then
        return self:ExecuteFireballSkill(player, targetPlayer)
    elseif skillName == "Shield Bash" then
        return self:ExecuteShieldBashSkill(player, targetPlayer)
    else
        return {
            success = false,
            error = "未實現的技能"
        }
    end
end

-- 執行治療技能
function HeroService:ExecuteHealSkill(player, targetPlayer)
    targetPlayer = targetPlayer or player -- 默認治療自己
    
    local healConfig = HeroConfig.HEALING_SYSTEM.healingSkills.MINOR_HEAL
    
    -- 檢查魔力
    local heroData = HeroManager:GetHeroData(player)
    if heroData.currentMana < healConfig.manaCost then
        return {
            success = false,
            error = "魔力不足"
        }
    end
    
    -- 消耗魔力
    heroData.currentMana = heroData.currentMana - healConfig.manaCost
    
    -- 執行治療
    local success = HeroManager:Heal(player, targetPlayer, healConfig.healAmount, healConfig.type)
    
    return {
        success = success,
        healAmount = healConfig.healAmount,
        manaCost = healConfig.manaCost
    }
end

-- 執行火球技能
function HeroService:ExecuteFireballSkill(player, targetPlayer)
    if not targetPlayer then
        return {
            success = false,
            error = "需要目標"
        }
    end
    
    local damage = 80 -- 基礎傷害
    local manaCost = 30
    
    -- 檢查魔力
    local heroData = HeroManager:GetHeroData(player)
    if heroData.currentMana < manaCost then
        return {
            success = false,
            error = "魔力不足"
        }
    end
    
    -- 消耗魔力
    heroData.currentMana = heroData.currentMana - manaCost
    
    -- 造成傷害
    local success = HeroManager:DealDamage(
        player, targetPlayer, damage, 
        HeroConfig.DAMAGE_SYSTEM.damageTypes.MAGICAL
    )
    
    return {
        success = success,
        damage = damage,
        manaCost = manaCost
    }
end

-- 執行盾擊技能
function HeroService:ExecuteShieldBashSkill(player, targetPlayer)
    if not targetPlayer then
        return {
            success = false,
            error = "需要目標"
        }
    end
    
    local damage = 60 -- 基礎傷害
    local manaCost = 20
    
    -- 檢查魔力
    local heroData = HeroManager:GetHeroData(player)
    if heroData.currentMana < manaCost then
        return {
            success = false,
            error = "魔力不足"
        }
    end
    
    -- 消耗魔力
    heroData.currentMana = heroData.currentMana - manaCost
    
    -- 造成傷害
    local success = HeroManager:DealDamage(
        player, targetPlayer, damage,
        HeroConfig.DAMAGE_SYSTEM.damageTypes.PHYSICAL
    )
    
    return {
        success = success,
        damage = damage,
        manaCost = manaCost
    }
end

-- 服務端方法：給予經驗值
function HeroService:GiveExperience(player, expAmount)
    return HeroManager:GainExperience(player, expAmount)
end

-- 服務端方法：造成傷害
function HeroService:DealDamage(attacker, target, damage, damageType)
    return HeroManager:DealDamage(attacker, target, damage, damageType)
end

-- 服務端方法：治療
function HeroService:Heal(healer, target, healAmount, healType)
    return HeroManager:Heal(healer, target, healAmount, healType)
end

-- 服務端方法：獲取英雄數據
function HeroService:GetHeroData(player)
    return HeroManager:GetHeroData(player)
end

-- 服務端方法：獲取當前屬性
function HeroService:GetCurrentStats(player)
    return HeroManager:GetCurrentStats(player)
end

-- 清理敏感數據
function HeroService:SanitizeHeroData(heroData)
    if not heroData then
        return nil
    end
    
    -- 創建副本並移除敏感信息
    local sanitized = {}
    
    -- 複製安全的字段
    local safeFields = {
        "playerId", "playerName", "class", "level", "experience",
        "currentHealth", "currentMana", "baseState", "combatState",
        "skills", "skillLevels", "stats"
    }
    
    for _, field in ipairs(safeFields) do
        sanitized[field] = heroData[field]
    end
    
    -- 複製基礎屬性
    sanitized.baseStats = {}
    for key, value in pairs(heroData.baseStats) do
        sanitized.baseStats[key] = value
    end
    
    return sanitized
end

-- 調試信息
function HeroService:Debug()
    print("🦸 英雄服務調試信息:")
    HeroManager:Debug()
end

return HeroService
