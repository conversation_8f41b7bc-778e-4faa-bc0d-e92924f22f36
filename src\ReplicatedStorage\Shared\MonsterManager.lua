-- 怪物-寵物統一管理系統
local MonsterManager = {}

-- 依賴
local MonsterConfig = require(game.ReplicatedStorage.Configuration.MonsterConfig)
-- 移除 Utils 依賴

-- 怪物數據庫 (基於實際97個模型)
MonsterManager.MONSTER_DATABASE = {}

-- 初始化怪物數據庫
function MonsterManager:Initialize()
    print("🐾 初始化怪物管理系統...")
    
    -- 載入現有模型數據
    self:LoadExistingModels()
    
    -- 分配稀有度和區域
    self:AssignRarityAndZones()
    
    print("✅ 怪物管理系統初始化完成")
    print("📊 總計", self:GetTotalMonsterCount(), "隻怪物")
end

-- 載入現有模型
function MonsterManager:LoadExistingModels()
    local petFolder = game:GetService("Workspace"):FindFirstChild("Pet")
    if not petFolder then
        warn("❌ 找不到 Pet 文件夾")
        return
    end
    
    for _, model in pairs(petFolder:GetChildren()) do
        if model:IsA("Model") then
            local monsterData = self:AnalyzeModel(model)
            self.MONSTER_DATABASE[model.Name] = monsterData
        end
    end
    
    print("📦 已載入", self:GetTotalMonsterCount(), "個模型")
end

-- 分析模型屬性
function MonsterManager:AnalyzeModel(model)
    local partCount = 0
    local hasSpecialEffects = false
    local materials = {}
    local boundingBox = Vector3.new(0, 0, 0)
    
    local function analyzeRecursive(obj)
        for _, child in pairs(obj:GetChildren()) do
            if child:IsA("BasePart") then
                partCount = partCount + 1
                
                -- 分析材質
                local material = child.Material.Name
                materials[material] = (materials[material] or 0) + 1
                
                -- 檢查特殊效果
                if child.Material == Enum.Material.Neon or 
                   child.Material == Enum.Material.Glass or
                   child.Transparency > 0 and child.Transparency < 1 then
                    hasSpecialEffects = true
                end
                
                -- 計算包圍盒
                local size = child.Size
                boundingBox = Vector3.new(
                    math.max(boundingBox.X, size.X),
                    math.max(boundingBox.Y, size.Y),
                    math.max(boundingBox.Z, size.Z)
                )
            elseif child:IsA("Model") then
                analyzeRecursive(child)
            end
        end
    end
    
    analyzeRecursive(model)
    
    return {
        name = model.Name,
        partCount = partCount,
        hasSpecialEffects = hasSpecialEffects,
        materials = materials,
        boundingBox = boundingBox,
        complexity = self:CalculateComplexity(partCount, hasSpecialEffects, materials)
    }
end

-- 計算模型複雜度
function MonsterManager:CalculateComplexity(partCount, hasSpecialEffects, materials)
    local complexity = partCount
    
    -- 特殊效果加成
    if hasSpecialEffects then
        complexity = complexity * 1.5
    end
    
    -- 材質多樣性加成
    local materialCount = 0
    for _ in pairs(materials) do
        materialCount = materialCount + 1
    end
    complexity = complexity + materialCount * 2
    
    return math.floor(complexity)
end

-- 分配稀有度和區域
function MonsterManager:AssignRarityAndZones()
    local rarityDistribution = {
        COMMON = {},
        UNCOMMON = {},
        RARE = {},
        EPIC = {},
        LEGENDARY = {}
    }
    
    local zoneDistribution = {
        FOREST = {},
        ICE = {},
        LAVA = {}
    }
    
    -- 根據複雜度和名稱分配稀有度
    for name, data in pairs(self.MONSTER_DATABASE) do
        local rarity = self:DetermineRarity(data)
        local zone = self:DetermineZone(name, data)
        
        data.rarity = rarity
        data.zone = zone
        data.stats = self:GenerateStats(rarity)
        
        table.insert(rarityDistribution[rarity], name)
        table.insert(zoneDistribution[zone], name)
    end
    
    -- 平衡分配 (確保每個區域有97隻)
    self:BalanceDistribution(zoneDistribution)
    
    -- 輸出統計
    self:PrintDistributionStats(rarityDistribution, zoneDistribution)
end

-- 確定稀有度
function MonsterManager:DetermineRarity(data)
    local complexity = data.complexity
    
    if complexity >= 50 then
        return "LEGENDARY"
    elseif complexity >= 30 then
        return "EPIC"
    elseif complexity >= 20 then
        return "RARE"
    elseif complexity >= 10 then
        return "UNCOMMON"
    else
        return "COMMON"
    end
end

-- 確定區域
function MonsterManager:DetermineZone(name, data)
    local lowerName = string.lower(name)
    
    -- 火系/惡魔系 -> LAVA
    if string.find(lowerName, "demon") or string.find(lowerName, "lava") or 
       string.find(lowerName, "hellish") or string.find(lowerName, "light") or
       string.find(lowerName, "fire") or string.find(lowerName, "flame") then
        return "LAVA"
    end
    
    -- 冰系 -> ICE
    if string.find(lowerName, "ice") or string.find(lowerName, "snow") or
       string.find(lowerName, "frost") or string.find(lowerName, "crystal") then
        return "ICE"
    end
    
    -- 動物系 -> FOREST
    if string.find(lowerName, "bear") or string.find(lowerName, "fox") or 
       string.find(lowerName, "cat") or string.find(lowerName, "dog") or
       string.find(lowerName, "bunny") or string.find(lowerName, "mouse") or
       string.find(lowerName, "bee") or string.find(lowerName, "cow") or
       string.find(lowerName, "pig") or string.find(lowerName, "duck") then
        return "FOREST"
    end
    
    -- 默認分配到森林
    return "FOREST"
end

-- 生成戰鬥屬性
function MonsterManager:GenerateStats(rarity)
    local baseStats = MonsterConfig.COMBAT_STATS.BASE_STATS[rarity]
    if not baseStats then
        baseStats = MonsterConfig.COMBAT_STATS.BASE_STATS.COMMON
    end
    
    -- 添加隨機變化 (±10%)
    local variation = 0.1
    return {
        health = math.floor(baseStats.health * (1 + (math.random() - 0.5) * variation)),
        attack = math.floor(baseStats.attack * (1 + (math.random() - 0.5) * variation)),
        defense = math.floor(baseStats.defense * (1 + (math.random() - 0.5) * variation)),
        speed = math.floor(baseStats.speed * (1 + (math.random() - 0.5) * variation)),
        critRate = baseStats.critRate,
        critDamage = baseStats.critDamage
    }
end

-- 平衡分配 (確保每個區域97隻)
function MonsterManager:BalanceDistribution(zoneDistribution)
    local targetPerZone = 32 -- 97/3 ≈ 32 (剩餘的分配給森林)
    
    -- 統計當前分配
    local currentCounts = {}
    for zone, monsters in pairs(zoneDistribution) do
        currentCounts[zone] = #monsters
    end
    
    print("🗺️ 區域分配統計:")
    for zone, count in pairs(currentCounts) do
        print(string.format("  %s: %d 隻", zone, count))
    end
    
    -- 如果分配不均，進行調整
    -- 這裡可以添加重新分配邏輯
end

-- 獲取怪物數據
function MonsterManager:GetMonsterData(monsterName)
    return self.MONSTER_DATABASE[monsterName]
end

-- 獲取指定稀有度的怪物
function MonsterManager:GetMonstersByRarity(rarity)
    local monsters = {}
    for name, data in pairs(self.MONSTER_DATABASE) do
        if data.rarity == rarity then
            table.insert(monsters, name)
        end
    end
    return monsters
end

-- 獲取指定區域的怪物
function MonsterManager:GetMonstersByZone(zone)
    local monsters = {}
    for name, data in pairs(self.MONSTER_DATABASE) do
        if data.zone == zone then
            table.insert(monsters, name)
        end
    end
    return monsters
end

-- 隨機選擇怪物 (基於稀有度權重)
function MonsterManager:GetRandomMonster(zone)
    local zoneMonsters = self:GetMonstersByZone(zone)
    if #zoneMonsters == 0 then
        return nil
    end
    
    -- 創建權重表
    local weightedList = {}
    for _, monsterName in ipairs(zoneMonsters) do
        local data = self.MONSTER_DATABASE[monsterName]
        local rarity = data.rarity
        local weight = MonsterConfig.RARITY[rarity].weight
        
        for i = 1, weight do
            table.insert(weightedList, monsterName)
        end
    end
    
    if #weightedList == 0 then
        return zoneMonsters[math.random(#zoneMonsters)]
    end
    
    return weightedList[math.random(#weightedList)]
end

-- 扭蛋系統
function MonsterManager:RollGacha(gachaType)
    local gachaConfig = MonsterConfig.GACHA[gachaType]
    if not gachaConfig then
        return nil
    end
    
    -- 根據機率選擇稀有度
    local roll = math.random(100)
    local cumulative = 0
    local selectedRarity = "COMMON"
    
    for rarity, rate in pairs(gachaConfig.rates) do
        cumulative = cumulative + rate
        if roll <= cumulative then
            selectedRarity = rarity
            break
        end
    end
    
    -- 從該稀有度中隨機選擇怪物
    local monsters = self:GetMonstersByRarity(selectedRarity)
    if #monsters == 0 then
        monsters = self:GetMonstersByRarity("COMMON")
    end
    
    if #monsters > 0 then
        local selectedMonster = monsters[math.random(#monsters)]
        return {
            name = selectedMonster,
            rarity = selectedRarity,
            data = self.MONSTER_DATABASE[selectedMonster],
            cost = gachaConfig.cost,
            currency = gachaConfig.currency
        }
    end
    
    return nil
end

-- 獲取總怪物數量
function MonsterManager:GetTotalMonsterCount()
    local count = 0
    for _ in pairs(self.MONSTER_DATABASE) do
        count = count + 1
    end
    return count
end

-- 輸出分配統計
function MonsterManager:PrintDistributionStats(rarityDistribution, zoneDistribution)
    print("\n💎 稀有度分配統計:")
    for rarity, monsters in pairs(rarityDistribution) do
        print(string.format("  %s: %d 隻", MonsterConfig.RARITY[rarity].name, #monsters))
    end
    
    print("\n🗺️ 區域分配統計:")
    for zone, monsters in pairs(zoneDistribution) do
        print(string.format("  %s: %d 隻", MonsterConfig.ZONES[zone].name, #monsters))
    end
end

-- 調試信息
function MonsterManager:Debug()
    print("🐾 怪物管理系統調試信息:")
    print("  總怪物數:", self:GetTotalMonsterCount())
    
    -- 顯示部分怪物信息
    local count = 0
    for name, data in pairs(self.MONSTER_DATABASE) do
        if count < 10 then
            print(string.format("  %s: %s級 %s區域 複雜度%d", 
                  name, data.rarity, data.zone, data.complexity))
            count = count + 1
        else
            break
        end
    end
    
    if self:GetTotalMonsterCount() > 10 then
        print("  ... 還有", self:GetTotalMonsterCount() - 10, "隻怪物")
    end
end

return MonsterManager
