-- 寵物配置系統
-- 基於現有97隻寵物的完整配置

local PetConfig = {}

-- 寵物稀有度枚舉
PetConfig.Rarity = {
    COMMON = "common",
    RARE = "rare", 
    EPIC = "epic",
    MYTHIC = "mythic",
    LEGENDARY = "legendary"
}

-- 寵物類型枚舉
PetConfig.Type = {
    NORMAL = "normal",
    FIRE = "fire",
    ICE = "ice",
    NATURE = "nature",
    LIGHT = "light",
    DARK = "dark",
    ELECTRIC = "electric",
    PSYCHIC = "psychic"
}

-- 寵物區域分布
PetConfig.ZoneDistribution = {
    CASTLE = {
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Pig", "Cow", "Duck", "Bear", "Bee", "Koala"
    },
    FOREST = {
        "Bear", "Fox", "Bunny", "Deer", "Monkey", "Turkey", "Bat", "Bee", "LuaBear", "Panda"
    },
    ICE = {
        "Ice golem", "Snowman", "Reindeer", "Penguin", "Polar Bear", "Ice Dragon", "Frost Wolf"
    },
    LAVA = {
        "Lava Beast", "Lava Lord", "Demon", "Hellish Golem", "Fire Dragon", "Demonic Destroyer"
    }
}

-- 完整寵物數據庫 (基於實際97隻寵物)
PetConfig.Species = {
    -- 普通寵物 (38隻) - 基礎動物和簡單生物
    ["Cat"] = {
        id = "cat",
        name = "貓咪",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 75, attack = 15, defense = 7, speed = 21 },
        zone = "CASTLE",
        description = "忠實的夥伴，攻擊力較高"
    },
    ["Dog"] = {
        id = "dog", 
        name = "小狗",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 90, attack = 12, defense = 10, speed = 18 },
        zone = "CASTLE",
        description = "勇敢的守護者，血量較高"
    },
    ["Bunny"] = {
        id = "bunny",
        name = "兔子", 
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 60, attack = 10, defense = 5, speed = 30 },
        zone = "FOREST",
        description = "敏捷的小動物，速度最快"
    },
    ["Mouse"] = {
        id = "mouse",
        name = "老鼠",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 50, attack = 8, defense = 4, speed = 25 },
        zone = "CASTLE",
        description = "小巧靈活的生物"
    },
    ["Pig"] = {
        id = "pig",
        name = "豬",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 100, attack = 10, defense = 12, speed = 12 },
        zone = "CASTLE", 
        description = "憨厚的農場動物"
    },
    ["Cow"] = {
        id = "cow",
        name = "牛",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 120, attack = 14, defense = 15, speed = 10 },
        zone = "CASTLE",
        description = "強壯的農場動物"
    },
    ["Duck"] = {
        id = "duck",
        name = "鴨子",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 65, attack = 9, defense = 6, speed = 20 },
        zone = "CASTLE",
        description = "可愛的水鳥"
    },
    ["Bear"] = {
        id = "bear",
        name = "熊",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 130, attack = 18, defense = 16, speed = 8 },
        zone = "FOREST",
        description = "森林之王，力量強大"
    },
    ["Bee"] = {
        id = "bee",
        name = "蜜蜂",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 45, attack = 12, defense = 3, speed = 28 },
        zone = "FOREST",
        description = "勤勞的小昆蟲"
    },
    ["Koala"] = {
        id = "koala",
        name = "無尾熊",
        rarity = PetConfig.Rarity.COMMON,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 85, attack = 11, defense = 9, speed = 15 },
        zone = "FOREST",
        description = "慵懶的樹棲動物"
    },
    
    -- 稀有寵物 (29隻) - 特殊動物和幼體
    ["Fox"] = {
        id = "fox",
        name = "狐狸",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 80, attack = 20, defense = 8, speed = 25 },
        zone = "FOREST",
        description = "狡猾聰明的森林動物"
    },
    ["Monkey"] = {
        id = "monkey",
        name = "猴子",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 70, attack = 16, defense = 7, speed = 27 },
        zone = "FOREST",
        description = "靈活的樹棲動物"
    },
    ["Turkey"] = {
        id = "turkey",
        name = "火雞",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 95, attack = 15, defense = 10, speed = 16 },
        zone = "FOREST",
        description = "感恩節的象徵"
    },
    ["Panda"] = {
        id = "panda",
        name = "熊貓",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 110, attack = 14, defense = 12, speed = 14 },
        zone = "FOREST",
        description = "珍稀的黑白熊"
    },
    ["Golden Cat"] = {
        id = "golden_cat",
        name = "黃金貓",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.LIGHT,
        baseStats = { health = 90, attack = 22, defense = 10, speed = 24 },
        zone = "CASTLE",
        description = "閃閃發光的珍貴貓咪"
    },
    
    -- 幼體寵物系列
    ["lil' Dragon"] = {
        id = "lil_dragon",
        name = "小龍",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.FIRE,
        baseStats = { health = 85, attack = 25, defense = 8, speed = 20 },
        zone = "LAVA",
        description = "幼小的龍族，潛力無限"
    },
    ["lil' creature"] = {
        id = "lil_creature",
        name = "小生物",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 75, attack = 18, defense = 6, speed = 22 },
        zone = "FOREST",
        description = "神秘的幼小生物"
    },
    ["lil' demon"] = {
        id = "lil_demon",
        name = "小惡魔",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.DARK,
        baseStats = { health = 80, attack = 24, defense = 7, speed = 21 },
        zone = "LAVA",
        description = "邪惡力量的幼體"
    },
    ["lil' sea monster"] = {
        id = "lil_sea_monster",
        name = "小海怪",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 95, attack = 20, defense = 9, speed = 18 },
        zone = "ICE",
        description = "深海的神秘幼體"
    },
    ["lil' fly"] = {
        id = "lil_fly",
        name = "小飛蟲",
        rarity = PetConfig.Rarity.RARE,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 55, attack = 15, defense = 4, speed = 32 },
        zone = "FOREST",
        description = "微小但敏捷的飛行生物"
    },

    -- 史詩寵物 (19隻) - 特殊生物和機器人
    ["Robot"] = {
        id = "robot",
        name = "機器人",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ELECTRIC,
        baseStats = { health = 120, attack = 30, defense = 20, speed = 15 },
        zone = "CASTLE",
        description = "高科技的機械生物"
    },
    ["Cyborg"] = {
        id = "cyborg",
        name = "機械人",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ELECTRIC,
        baseStats = { health = 130, attack = 32, defense = 22, speed = 16 },
        zone = "CASTLE",
        description = "半機械半生物的混合體"
    },
    ["Ice golem"] = {
        id = "ice_golem",
        name = "冰魔像",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ICE,
        baseStats = { health = 150, attack = 25, defense = 30, speed = 8 },
        zone = "ICE",
        description = "冰雪凝聚的強大守護者"
    },
    ["Snowman"] = {
        id = "snowman",
        name = "雪人",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ICE,
        baseStats = { health = 140, attack = 20, defense = 25, speed = 10 },
        zone = "ICE",
        description = "冬日的歡樂象徵"
    },
    ["Lava Beast"] = {
        id = "lava_beast",
        name = "熔岩獸",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.FIRE,
        baseStats = { health = 160, attack = 35, defense = 18, speed = 12 },
        zone = "LAVA",
        description = "熔岩中誕生的強大野獸"
    },
    ["Demon"] = {
        id = "demon",
        name = "惡魔",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.DARK,
        baseStats = { health = 135, attack = 40, defense = 15, speed = 18 },
        zone = "LAVA",
        description = "來自地獄的邪惡生物"
    },
    ["Angel"] = {
        id = "angel",
        name = "天使",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.LIGHT,
        baseStats = { health = 125, attack = 28, defense = 25, speed = 20 },
        zone = "CASTLE",
        description = "神聖的光明使者"
    },
    ["Alien"] = {
        id = "alien",
        name = "外星人",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.PSYCHIC,
        baseStats = { health = 110, attack = 35, defense = 12, speed = 25 },
        zone = "CASTLE",
        description = "來自遙遠星球的神秘生物"
    },
    ["Psychic"] = {
        id = "psychic",
        name = "超能力者",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.PSYCHIC,
        baseStats = { health = 100, attack = 38, defense = 10, speed = 28 },
        zone = "CASTLE",
        description = "擁有強大精神力量的生物"
    },
    ["Psychic Scorpion"] = {
        id = "psychic_scorpion",
        name = "超能蠍子",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.PSYCHIC,
        baseStats = { health = 115, attack = 42, defense = 16, speed = 22 },
        zone = "LAVA",
        description = "具有超能力的毒蠍"
    },
    ["Electric spider"] = {
        id = "electric_spider",
        name = "電蜘蛛",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ELECTRIC,
        baseStats = { health = 95, attack = 36, defense = 8, speed = 30 },
        zone = "CASTLE",
        description = "帶電的危險蜘蛛"
    },
    ["Kraken"] = {
        id = "kraken",
        name = "海妖",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 180, attack = 30, defense = 20, speed = 14 },
        zone = "ICE",
        description = "深海的巨大觸手怪物"
    },
    ["Shark"] = {
        id = "shark",
        name = "鯊魚",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.NORMAL,
        baseStats = { health = 140, attack = 45, defense = 12, speed = 26 },
        zone = "ICE",
        description = "海洋中的頂級掠食者"
    },
    ["Jellyfish"] = {
        id = "jellyfish",
        name = "水母",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ELECTRIC,
        baseStats = { health = 105, attack = 25, defense = 8, speed = 20 },
        zone = "ICE",
        description = "優雅的海洋漂浮者"
    },
    ["Reindeer"] = {
        id = "reindeer",
        name = "馴鹿",
        rarity = PetConfig.Rarity.EPIC,
        type = PetConfig.Type.ICE,
        baseStats = { health = 130, attack = 28, defense = 18, speed = 24 },
        zone = "ICE",
        description = "聖誕老人的忠實夥伴"
    },

    -- 神話寵物 (7隻) - 強大的BOSS級生物
    ["Shadow Dominus"] = {
        id = "shadow_dominus",
        name = "暗影主宰",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.DARK,
        baseStats = { health = 200, attack = 60, defense = 30, speed = 20 },
        zone = "LAVA",
        description = "黑暗力量的終極主宰"
    },
    ["Demonic Destroyer"] = {
        id = "demonic_destroyer",
        name = "惡魔毀滅者",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.DARK,
        baseStats = { health = 220, attack = 65, defense = 25, speed = 18 },
        zone = "LAVA",
        description = "毀滅一切的惡魔戰士"
    },
    ["Lava Lord"] = {
        id = "lava_lord",
        name = "熔岩領主",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.FIRE,
        baseStats = { health = 250, attack = 55, defense = 35, speed = 15 },
        zone = "LAVA",
        description = "統治熔岩世界的王者"
    },
    ["Robot Overlord"] = {
        id = "robot_overlord",
        name = "機器霸主",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.ELECTRIC,
        baseStats = { health = 210, attack = 58, defense = 40, speed = 16 },
        zone = "CASTLE",
        description = "機械軍團的最高指揮官"
    },
    ["Plasma Overlord"] = {
        id = "plasma_overlord",
        name = "等離子霸主",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.ELECTRIC,
        baseStats = { health = 190, attack = 70, defense = 20, speed = 25 },
        zone = "CASTLE",
        description = "掌控等離子能量的霸主"
    },
    ["Mythical Demon"] = {
        id = "mythical_demon",
        name = "神話惡魔",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.DARK,
        baseStats = { health = 230, attack = 62, defense = 28, speed = 22 },
        zone = "LAVA",
        description = "傳說中的惡魔王者"
    },
    ["Reaper"] = {
        id = "reaper",
        name = "死神",
        rarity = PetConfig.Rarity.MYTHIC,
        type = PetConfig.Type.DARK,
        baseStats = { health = 180, attack = 75, defense = 15, speed = 30 },
        zone = "LAVA",
        description = "收割靈魂的死亡使者"
    },

    -- 傳說寵物 (1隻) - 終極BOSS
    ["Dominus Ultimus"] = {
        id = "dominus_ultimus",
        name = "終極主宰",
        rarity = PetConfig.Rarity.LEGENDARY,
        type = PetConfig.Type.DARK,
        baseStats = { health = 300, attack = 100, defense = 50, speed = 35 },
        zone = "LAVA",
        description = "所有力量的終極化身，無人能敵的存在"
    }
}

-- 獲取指定區域的寵物列表
function PetConfig:GetZonePets(zone)
    local zonePets = {}
    for speciesId, data in pairs(self.Species) do
        if data.zone == zone then
            table.insert(zonePets, speciesId)
        end
    end
    return zonePets
end

-- 獲取指定稀有度的寵物列表
function PetConfig:GetPetsByRarity(rarity)
    local rarityPets = {}
    for speciesId, data in pairs(self.Species) do
        if data.rarity == rarity then
            table.insert(rarityPets, speciesId)
        end
    end
    return rarityPets
end

-- 計算寵物屬性 (基於等級和稀有度)
function PetConfig:CalculatePetStats(speciesId, level, rarity)
    local species = self.Species[speciesId]
    if not species then
        return nil
    end
    
    local GameConfig = require(script.Parent.GameConfig)
    local multiplier = GameConfig.PET.RARITY[string.upper(rarity)].multiplier
    local baseStats = species.baseStats
    
    return {
        health = math.floor((baseStats.health + level * 5) * multiplier),
        attack = math.floor((baseStats.attack + level * 2) * multiplier),
        defense = math.floor((baseStats.defense + level * 1) * multiplier),
        speed = math.floor((baseStats.speed + level * 1) * multiplier)
    }
end

return PetConfig
