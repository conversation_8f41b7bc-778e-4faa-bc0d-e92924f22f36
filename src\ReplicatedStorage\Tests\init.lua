-- 測試系統初始化腳本
local TestRunner = require(script.TestRunner)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

local TestSystem = {}

-- 初始化測試系統
function TestSystem:Initialize()
    print("🧪 初始化測試系統...")
    
    -- 初始化事件管理器
    EventManager:Initialize()
    
    -- 初始化測試運行器
    TestRunner:Initialize()
    
    -- 檢查測試環境
    local envCheck = TestRunner:CheckEnvironment()
    if not envCheck then
        warn("❌ 測試環境檢查失敗")
        return false
    end
    
    print("✅ 測試系統初始化完成")
    return true
end

-- 運行所有測試
function TestSystem:RunAllTests()
    print("🚀 開始運行所有測試...")
    
    local success = self:Initialize()
    if not success then
        return false
    end
    
    -- 運行測試
    local allPassed = TestRunner:RunAllTests()
    
    -- 顯示結果
    local stats = TestRunner:GetStats()
    print("\n📊 測試完成統計:")
    print("總測試數:", stats.totalTests)
    print("通過:", stats.passedTests)
    print("失敗:", stats.failedTests)
    print("成功率:", string.format("%.1f%%", stats.passedTests / stats.totalTests * 100))
    
    return allPassed
end

-- 運行特定測試套件
function TestSystem:RunTestSuite(suiteName)
    print("🧪 運行測試套件:", suiteName)
    
    local success = self:Initialize()
    if not success then
        return false
    end
    
    return TestRunner:RunTestSuite(suiteName)
end

-- 快速測試（只運行核心測試）
function TestSystem:RunQuickTests()
    print("⚡ 運行快速測試...")
    
    local success = self:Initialize()
    if not success then
        return false
    end
    
    -- 設置快速測試配置
    TestRunner:SetConfig({
        verbose = false,
        stopOnFirstFailure = true,
        timeout = 10
    })
    
    -- 只運行核心測試套件
    local coreTests = {
        "Configuration & Utils",
        "Event System"
    }
    
    local allPassed = true
    for _, suiteName in ipairs(coreTests) do
        local result = TestRunner:RunTestSuite(suiteName)
        if not result then
            allPassed = false
            break
        end
    end
    
    return allPassed
end

-- 運行性能測試
function TestSystem:RunPerformanceTests()
    print("⚡ 運行性能測試...")
    
    local startTime = tick()
    
    -- 測試事件系統性能
    local eventCount = 1000
    for i = 1, eventCount do
        EventManager:Fire(EventManager.EventTypes.PLAYER_LEVEL_UP, {
            player = "TestPlayer",
            level = i
        })
    end
    
    local eventTime = tick() - startTime
    print("📊 事件系統性能:")
    print("  觸發", eventCount, "個事件耗時:", string.format("%.3f秒", eventTime))
    print("  平均每個事件:", string.format("%.6f秒", eventTime / eventCount))
    
    -- 測試ECS性能
    local Matter = require(game.ReplicatedStorage.Packages.Matter)
    local Components = require(game.ReplicatedStorage.Components)
    
    local world = Matter.World.new()
    local entityCount = 1000
    
    startTime = tick()
    
    -- 創建大量實體
    for i = 1, entityCount do
        world:spawn(
            Components.HeroComponent({
                level = i,
                experience = i * 100,
                skillPoints = i
            }),
            Components.HealthComponent({
                current = 100 + i,
                maximum = 100 + i,
                regeneration = 1,
                lastRegenTime = tick()
            })
        )
    end
    
    local createTime = tick() - startTime
    
    -- 查詢實體
    startTime = tick()
    local queryCount = 0
    for entityId, hero, health in world:query(Components.HeroComponent, Components.HealthComponent) do
        queryCount = queryCount + 1
    end
    local queryTime = tick() - startTime
    
    print("📊 ECS 性能:")
    print("  創建", entityCount, "個實體耗時:", string.format("%.3f秒", createTime))
    print("  查詢", queryCount, "個實體耗時:", string.format("%.3f秒", queryTime))
    
    -- 清理
    world:clear()
    
    return true
end

-- 運行壓力測試
function TestSystem:RunStressTests()
    print("💪 運行壓力測試...")
    
    local success = true
    
    -- 事件系統壓力測試
    print("測試事件系統壓力...")
    local eventStressTest = function()
        local connections = {}
        
        -- 創建大量監聽器
        for i = 1, 100 do
            local connection = EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
                -- 模擬處理
            end)
            table.insert(connections, connection)
        end
        
        -- 觸發大量事件
        for i = 1, 1000 do
            EventManager:Fire(EventManager.EventTypes.COMBAT_STARTED, {
                attacker = "Player" .. i,
                target = "Monster" .. i
            })
        end
        
        -- 清理連接
        for _, connection in ipairs(connections) do
            connection:Disconnect()
        end
    end
    
    local eventSuccess, eventError = pcall(eventStressTest)
    if not eventSuccess then
        print("❌ 事件系統壓力測試失敗:", eventError)
        success = false
    else
        print("✅ 事件系統壓力測試通過")
    end
    
    -- 內存壓力測試
    print("測試內存使用...")
    local memoryBefore = collectgarbage("count")
    
    -- 創建大量臨時對象
    local tempObjects = {}
    for i = 1, 10000 do
        tempObjects[i] = {
            id = i,
            data = string.rep("test", 100),
            timestamp = tick()
        }
    end
    
    local memoryAfter = collectgarbage("count")
    local memoryUsed = memoryAfter - memoryBefore
    
    -- 清理
    tempObjects = nil
    collectgarbage("collect")
    
    local memoryAfterCleanup = collectgarbage("count")
    local memoryRecovered = memoryAfter - memoryAfterCleanup
    
    print("📊 內存使用:")
    print("  使用內存:", string.format("%.2f KB", memoryUsed))
    print("  回收內存:", string.format("%.2f KB", memoryRecovered))
    print("  回收率:", string.format("%.1f%%", memoryRecovered / memoryUsed * 100))
    
    return success
end

-- 生成測試報告
function TestSystem:GenerateReport()
    print("📋 生成測試報告...")
    
    local stats = TestRunner:GetStats()
    local eventStats = EventManager:GetStats()
    
    local report = {
        timestamp = os.date("%Y-%m-%d %H:%M:%S"),
        testStats = stats,
        eventStats = eventStats,
        environment = {
            studio = game:GetService("RunService"):IsStudio(),
            server = game:GetService("RunService"):IsServer(),
            client = game:GetService("RunService"):IsClient()
        }
    }
    
    print("📊 測試報告:")
    print("  生成時間:", report.timestamp)
    print("  總測試數:", report.testStats.totalTests)
    print("  通過率:", string.format("%.1f%%", report.testStats.passedTests / report.testStats.totalTests * 100))
    print("  總事件數:", report.eventStats.totalEvents)
    print("  運行環境:", report.environment.studio and "Studio" or "Game")
    
    return report
end

-- 清理測試系統
function TestSystem:Cleanup()
    print("🧹 清理測試系統...")
    
    TestRunner:Cleanup()
    EventManager:Cleanup()
    
    print("✅ 測試系統清理完成")
end

-- 導出測試系統
return TestSystem
