-- 遭遇檢測系統
-- 檢測玩家與怪物的遭遇，記錄圖鑑數據

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local Matter = require(ReplicatedStorage.Packages.Matter)
local MonsterComponents = require(ReplicatedStorage.Components.MonsterComponents)
local Signal = require(ReplicatedStorage.Packages.Signal)

local EncounterSystem = {}

-- 事件信號
EncounterSystem.onFirstEncounter = Signal.new()
EncounterSystem.onEncounter = Signal.new()
EncounterSystem.onMonsterDefeated = Signal.new()

-- 系統狀態
local encounterDistance = 30 -- 遭遇距離
local lastCheckTime = 0
local CHECK_INTERVAL = 1 -- 檢查間隔(秒)

-- 獲取玩家位置
function EncounterSystem.getPlayerPosition(player)
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        return player.Character.HumanoidRootPart.Position
    end
    return nil
end

-- 計算距離
function EncounterSystem.calculateDistance(pos1, pos2)
    return (pos1 - pos2).Magnitude
end

-- 檢查玩家是否已遭遇過該物種
function EncounterSystem.hasEncountered(world, playerId, speciesId)
    for entityId, record in world:query(MonsterComponents.EncounterRecord) do
        if record.playerId == playerId and record.speciesId == speciesId then
            return true, entityId, record
        end
    end
    return false, nil, nil
end

-- 創建遭遇記錄
function EncounterSystem.createEncounterRecord(world, playerId, speciesId)
    local currentTime = tick()
    
    return world:spawn(
        MonsterComponents.EncounterRecord({
            playerId = playerId,
            speciesId = speciesId,
            firstEncounterTime = currentTime,
            totalEncounters = 1,
            totalDefeated = 0,
            lastEncounterTime = currentTime,
            isUnlocked = true
        })
    )
end

-- 更新遭遇記錄
function EncounterSystem.updateEncounterRecord(world, recordId, record)
    local currentTime = tick()
    
    record.totalEncounters = record.totalEncounters + 1
    record.lastEncounterTime = currentTime
    
    world:insert(recordId, MonsterComponents.EncounterRecord(record))
end

-- 檢查圖鑑條目是否存在
function EncounterSystem.hasPokedexEntry(world, playerId, speciesId)
    for entityId, entry in world:query(MonsterComponents.PokedexEntry) do
        if entry.playerId == playerId and entry.speciesId == speciesId then
            return true, entityId, entry
        end
    end
    return false, nil, nil
end

-- 創建圖鑑條目
function EncounterSystem.createPokedexEntry(world, playerId, speciesId)
    local currentTime = tick()
    
    return world:spawn(
        MonsterComponents.PokedexEntry({
            playerId = playerId,
            speciesId = speciesId,
            isDiscovered = true,
            isOwned = false,
            discoveryTime = currentTime,
            ownedTime = 0,
            encounterCount = 1,
            defeatCount = 0,
            notes = string.format("首次發現於 %s", os.date("%Y-%m-%d %H:%M:%S", currentTime))
        })
    )
end

-- 更新圖鑑條目
function EncounterSystem.updatePokedexEntry(world, entryId, entry)
    entry.encounterCount = entry.encounterCount + 1
    world:insert(entryId, MonsterComponents.PokedexEntry(entry))
end

-- 處理首次遭遇
function EncounterSystem.handleFirstEncounter(world, player, monster)
    local playerId = player.UserId
    local speciesId = monster.speciesId
    
    -- 創建遭遇記錄
    local recordId = EncounterSystem.createEncounterRecord(world, playerId, speciesId)
    
    -- 創建或更新圖鑑條目
    local hasEntry, entryId, entry = EncounterSystem.hasPokedexEntry(world, playerId, speciesId)
    
    if not hasEntry then
        entryId = EncounterSystem.createPokedexEntry(world, playerId, speciesId)
        print(string.format("📖 %s 首次發現 %s！圖鑑已解鎖", player.Name, speciesId))
        
        -- 觸發首次遭遇事件
        EncounterSystem.onFirstEncounter:Fire(player, monster, entryId)
    else
        EncounterSystem.updatePokedexEntry(world, entryId, entry)
    end
    
    return recordId, entryId
end

-- 處理重複遭遇
function EncounterSystem.handleRepeatEncounter(world, player, monster, recordId, record)
    local playerId = player.UserId
    local speciesId = monster.speciesId
    
    -- 更新遭遇記錄
    EncounterSystem.updateEncounterRecord(world, recordId, record)
    
    -- 更新圖鑑條目
    local hasEntry, entryId, entry = EncounterSystem.hasPokedexEntry(world, playerId, speciesId)
    if hasEntry then
        EncounterSystem.updatePokedexEntry(world, entryId, entry)
    end
    
    -- 觸發遭遇事件
    EncounterSystem.onEncounter:Fire(player, monster, record.totalEncounters)
    
    print(string.format("🔄 %s 再次遭遇 %s (第%d次)", player.Name, speciesId, record.totalEncounters))
end

-- 處理怪物被擊敗
function EncounterSystem.handleMonsterDefeated(world, player, monster)
    local playerId = player.UserId
    local speciesId = monster.speciesId
    
    -- 更新遭遇記錄中的擊敗次數
    local hasRecord, recordId, record = EncounterSystem.hasEncountered(world, playerId, speciesId)
    if hasRecord then
        record.totalDefeated = record.totalDefeated + 1
        world:insert(recordId, MonsterComponents.EncounterRecord(record))
    end
    
    -- 更新圖鑑條目中的擊敗次數
    local hasEntry, entryId, entry = EncounterSystem.hasPokedexEntry(world, playerId, speciesId)
    if hasEntry then
        entry.defeatCount = entry.defeatCount + 1
        world:insert(entryId, MonsterComponents.PokedexEntry(entry))
    end
    
    -- 觸發擊敗事件
    EncounterSystem.onMonsterDefeated:Fire(player, monster, record and record.totalDefeated or 1)
    
    print(string.format("⚔️ %s 擊敗了 %s！", player.Name, speciesId))
end

-- 檢查玩家與怪物的遭遇
function EncounterSystem.checkEncounters(world)
    for _, player in pairs(Players:GetPlayers()) do
        local playerPos = EncounterSystem.getPlayerPosition(player)
        if not playerPos then continue end
        
        local playerId = player.UserId
        
        -- 檢查附近的怪物
        for entityId, monster, position in world:query(
            MonsterComponents.Monster, 
            MonsterComponents.MonsterPosition
        ) do
            if not monster.isWild then continue end
            
            local monsterPos = Vector3.new(position.x, position.y, position.z)
            local distance = EncounterSystem.calculateDistance(playerPos, monsterPos)
            
            -- 如果在遭遇距離內
            if distance <= encounterDistance then
                local hasRecord, recordId, record = EncounterSystem.hasEncountered(world, playerId, monster.speciesId)
                
                if not hasRecord then
                    -- 首次遭遇
                    EncounterSystem.handleFirstEncounter(world, player, monster)
                else
                    -- 檢查是否需要更新遭遇時間（避免頻繁觸發）
                    local timeSinceLastEncounter = tick() - record.lastEncounterTime
                    if timeSinceLastEncounter > 10 then -- 10秒冷卻
                        EncounterSystem.handleRepeatEncounter(world, player, monster, recordId, record)
                    end
                end
                
                -- 更新怪物的最後被看見時間
                monster.lastSeenTime = tick()
                world:insert(entityId, MonsterComponents.Monster(monster))
            end
        end
    end
end

-- 主要系統步驟
function EncounterSystem.step(world, state)
    local currentTime = tick()
    
    -- 定期檢查遭遇
    if currentTime - lastCheckTime >= CHECK_INTERVAL then
        lastCheckTime = currentTime
        EncounterSystem.checkEncounters(world)
    end
end

-- 獲取玩家的圖鑑統計
function EncounterSystem.getPokedexStats(world, playerId)
    local discovered = 0
    local owned = 0
    local totalEncounters = 0
    local totalDefeats = 0
    
    for entityId, entry in world:query(MonsterComponents.PokedexEntry) do
        if entry.playerId == playerId then
            if entry.isDiscovered then
                discovered = discovered + 1
            end
            if entry.isOwned then
                owned = owned + 1
            end
            totalEncounters = totalEncounters + entry.encounterCount
            totalDefeats = totalDefeats + entry.defeatCount
        end
    end
    
    return {
        discovered = discovered,
        owned = owned,
        totalEncounters = totalEncounters,
        totalDefeats = totalDefeats,
        completionRate = owned / 97 -- 基於97隻寵物
    }
end

-- 初始化系統
function EncounterSystem.initialize()
    print("✅ 遭遇檢測系統初始化完成")
end

return EncounterSystem
