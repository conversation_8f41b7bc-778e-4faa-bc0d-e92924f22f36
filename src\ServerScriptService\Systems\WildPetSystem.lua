-- 野生寵物系統 - 管理野生寵物生成和捕捉
local Matter = require(game.ReplicatedStorage.Packages.Matter)
local Components = require(game.ReplicatedStorage.Components)
local ZoneConfig = require(game.ReplicatedStorage.Configuration.ZoneConfig)
local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local WildPetSystem = {}

function WildPetSystem.new()
    local self = {
        name = "WildPetSystem",
        priority = 4,
        
        -- 野生寵物生成狀態
        _zoneSpawning = {},
        _lastSpawnTime = {},
        _wildPetCounts = {}
    }
    
    return setmetatable(self, { __index = WildPetSystem })
end

function WildPetSystem:step(world, state)
    -- 處理野生寵物生成
    self:handleWildPetSpawning(world, state)
    
    -- 更新野生寵物行為
    self:updateWildPetBehavior(world, state)
    
    -- 處理寵物捕捉
    self:handlePetCatching(world, state)
    
    -- 清理過期的野生寵物
    self:cleanupExpiredWildPets(world, state)
end

-- 處理野生寵物生成
function WildPetSystem:handleWildPetSpawning(world, state)
    local currentTime = tick()
    
    for zoneId, isSpawning in pairs(self._zoneSpawning) do
        if isSpawning then
            local lastSpawn = self._lastSpawnTime[zoneId] or 0
            local zoneConfig = ZoneConfig:GetZone(zoneId)
            
            if zoneConfig and zoneConfig.pets then
                local spawnRate = zoneConfig.pets.spawnRate
                local maxCount = zoneConfig.pets.maxCount
                local currentCount = self._wildPetCounts[zoneId] or 0
                
                -- 檢查是否需要生成野生寵物
                if currentCount < maxCount and currentTime - lastSpawn >= (1 / spawnRate) then
                    self:spawnWildPetInZone(world, zoneId, zoneConfig)
                    self._lastSpawnTime[zoneId] = currentTime
                end
            end
        end
    end
end

-- 在區域中生成野生寵物
function WildPetSystem:spawnWildPetInZone(world, zoneId, zoneConfig)
    local petSpecies = zoneConfig.pets.species
    if not petSpecies or #petSpecies == 0 then
        return
    end
    
    -- 根據權重選擇寵物種類
    local selectedSpecies = Utils.weightedRandom(petSpecies)
    if not selectedSpecies then
        return
    end
    
    -- 尋找生成位置
    local spawnPosition = self:findWildPetSpawnPosition(zoneId)
    if not spawnPosition then
        return
    end
    
    -- 檢查是否為閃光寵物 (1% 機率)
    local isShiny = math.random() < 0.01
    
    -- 創建野生寵物實體
    local wildPetId = world:spawn(
        Components.WildPetComponent({
            speciesId = selectedSpecies.id,
            rarity = selectedSpecies.rarity,
            spawnZone = zoneId,
            spawnTime = tick(),
            catchable = true
        }),
        
        Components.PetComponent({
            speciesId = selectedSpecies.id,
            name = PetConfig.Species[selectedSpecies.id].name .. (isShiny and " ✨" or ""),
            rarity = selectedSpecies.rarity,
            level = 1,
            experience = 0,
            isShiny = isShiny,
            obtainedAt = tick(),
            ownerId = nil -- 野生寵物沒有主人
        }),
        
        Components.TransformComponent({
            position = spawnPosition,
            rotation = Vector3.new(0, math.random(0, 360), 0),
            scale = Vector3.new(1, 1, 1)
        })
    )
    
    -- 更新野生寵物計數
    self._wildPetCounts[zoneId] = (self._wildPetCounts[zoneId] or 0) + 1
    
    print("🐾 生成野生寵物:", selectedSpecies.id, "稀有度:", selectedSpecies.rarity, "在", zoneId)
    
    return wildPetId
end

-- 尋找野生寵物生成位置
function WildPetSystem:findWildPetSpawnPosition(zoneId)
    local workspace = game:GetService("Workspace")
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    
    if zoneConfig and zoneConfig.boundaries.partName then
        local zonePart = workspace:FindFirstChild(zoneConfig.boundaries.partName)
        if zonePart then
            -- 在區域範圍內隨機生成，但避開中心區域
            local size = zonePart.Size
            local position = zonePart.Position
            
            local randomX = position.X + math.random(-size.X/2, size.X/2)
            local randomZ = position.Z + math.random(-size.Z/2, size.Z/2)
            local y = position.Y + size.Y/2 + 3 -- 在地面上方
            
            return Vector3.new(randomX, y, randomZ)
        end
    end
    
    return nil
end

-- 更新野生寵物行為
function WildPetSystem:updateWildPetBehavior(world, state)
    for entityId, wildPet, transform in world:query(
        Components.WildPetComponent,
        Components.TransformComponent
    ) do
        -- 檢查是否有玩家接近
        local nearestPlayer = self:findNearestPlayer(world, transform.position, 30)
        
        if nearestPlayer then
            -- 如果玩家太近，寵物會逃跑
            local distance = (transform.position - nearestPlayer.position).Magnitude
            if distance < 15 then
                self:makeWildPetFlee(world, entityId, transform, nearestPlayer.position)
            end
        else
            -- 隨機移動
            self:handleWildPetIdle(world, entityId, wildPet, transform)
        end
    end
end

-- 尋找最近的玩家
function WildPetSystem:findNearestPlayer(world, position, maxDistance)
    local nearestPlayer = nil
    local nearestDistance = maxDistance
    
    for entityId, player, transform in world:query(
        Components.PlayerComponent,
        Components.TransformComponent
    ) do
        local distance = (position - transform.position).Magnitude
        if distance < nearestDistance then
            nearestDistance = distance
            nearestPlayer = {
                entityId = entityId,
                position = transform.position,
                userId = player.userId
            }
        end
    end
    
    return nearestPlayer
end

-- 讓野生寵物逃跑
function WildPetSystem:makeWildPetFlee(world, petId, petTransform, playerPosition)
    -- 計算逃跑方向 (遠離玩家)
    local fleeDirection = (petTransform.position - playerPosition).Unit
    local fleeSpeed = 12 -- 逃跑速度
    local newPosition = petTransform.position + fleeDirection * fleeSpeed * (1/60)
    
    world:insert(petId, Components.TransformComponent({
        position = newPosition,
        rotation = petTransform.rotation,
        scale = petTransform.scale
    }))
end

-- 處理野生寵物待機
function WildPetSystem:handleWildPetIdle(world, petId, wildPet, transform)
    -- 隨機移動邏輯
    if math.random() < 0.005 then -- 0.5% 機率移動
        local randomDirection = Vector3.new(
            math.random(-1, 1),
            0,
            math.random(-1, 1)
        ).Unit
        
        local newPosition = transform.position + randomDirection * 3
        
        world:insert(petId, Components.TransformComponent({
            position = newPosition,
            rotation = transform.rotation,
            scale = transform.scale
        }))
    end
end

-- 處理寵物捕捉
function WildPetSystem:handlePetCatching(world, state)
    -- 這個方法會被其他系統調用，例如當玩家點擊野生寵物時
    -- 實際的捕捉邏輯會在 PetService 中處理
end

-- 嘗試捕捉野生寵物
function WildPetSystem:attemptCatch(world, playerId, wildPetId)
    local wildPet = world:get(wildPetId, Components.WildPetComponent)
    local pet = world:get(wildPetId, Components.PetComponent)
    
    if not wildPet or not pet or not wildPet.catchable then
        return { success = false, error = "寵物無法捕捉" }
    end
    
    -- 計算捕捉成功率
    local catchRate = self:calculateCatchRate(pet.rarity, pet.isShiny)
    local success = math.random() < catchRate
    
    if success then
        -- 捕捉成功
        self:handleSuccessfulCatch(world, playerId, wildPetId, pet)
        return { success = true, pet = pet }
    else
        -- 捕捉失敗，寵物逃跑
        self:handleFailedCatch(world, wildPetId, wildPet)
        return { success = false, error = "寵物逃跑了" }
    end
end

-- 計算捕捉成功率
function WildPetSystem:calculateCatchRate(rarity, isShiny)
    local baseRates = {
        common = 0.8,
        uncommon = 0.6,
        rare = 0.4,
        epic = 0.2,
        legendary = 0.1,
        mythic = 0.05
    }
    
    local rate = baseRates[rarity] or 0.5
    
    -- 閃光寵物更難捕捉
    if isShiny then
        rate = rate * 0.5
    end
    
    return rate
end

-- 處理成功捕捉
function WildPetSystem:handleSuccessfulCatch(world, playerId, wildPetId, pet)
    -- 將野生寵物轉換為玩家寵物
    world:insert(wildPetId, Components.PetComponent({
        speciesId = pet.speciesId,
        name = pet.name,
        rarity = pet.rarity,
        level = pet.level,
        experience = pet.experience,
        isShiny = pet.isShiny,
        obtainedAt = tick(),
        ownerId = playerId
    }))
    
    -- 移除野生寵物組件
    world:remove(wildPetId, Components.WildPetComponent)
    
    -- 更新野生寵物計數
    local wildPet = world:get(wildPetId, Components.WildPetComponent)
    if wildPet then
        local zoneId = wildPet.spawnZone
        self._wildPetCounts[zoneId] = math.max(0, (self._wildPetCounts[zoneId] or 0) - 1)
    end
    
    print("🎉 成功捕捉野生寵物:", pet.name)
end

-- 處理捕捉失敗
function WildPetSystem:handleFailedCatch(world, wildPetId, wildPet)
    -- 寵物逃跑 (移除實體)
    world:despawn(wildPetId)
    
    -- 更新野生寵物計數
    local zoneId = wildPet.spawnZone
    self._wildPetCounts[zoneId] = math.max(0, (self._wildPetCounts[zoneId] or 0) - 1)
    
    print("💨 野生寵物逃跑了")
end

-- 清理過期的野生寵物
function WildPetSystem:cleanupExpiredWildPets(world, state)
    local currentTime = tick()
    local maxLifetime = 300 -- 5分鐘後消失
    
    for entityId, wildPet in world:query(Components.WildPetComponent) do
        if currentTime - wildPet.spawnTime > maxLifetime then
            world:despawn(entityId)
            
            -- 更新計數
            local zoneId = wildPet.spawnZone
            self._wildPetCounts[zoneId] = math.max(0, (self._wildPetCounts[zoneId] or 0) - 1)
            
            print("⏰ 野生寵物過期消失:", wildPet.speciesId)
        end
    end
end

-- 開始在區域中生成野生寵物
function WildPetSystem:startWildPetSpawning(world, zoneId)
    self._zoneSpawning[zoneId] = true
    self._wildPetCounts[zoneId] = 0
    print("🎯 開始在區域生成野生寵物:", zoneId)
end

-- 停止在區域中生成野生寵物
function WildPetSystem:stopWildPetSpawning(world, zoneId)
    self._zoneSpawning[zoneId] = false
    print("🛑 停止在區域生成野生寵物:", zoneId)
end

-- 維護野生寵物數量
function WildPetSystem:maintainWildPets(world, zoneId)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig or not zoneConfig.pets then
        return
    end
    
    local currentCount = self._wildPetCounts[zoneId] or 0
    local maxCount = zoneConfig.pets.maxCount
    
    -- 如果野生寵物數量不足，可以調整生成邏輯
    if currentCount < maxCount * 0.3 then
        -- 可以在這裡增加生成頻率
    end
end

-- 獲取區域野生寵物數量
function WildPetSystem:getZoneWildPetCount(zoneId)
    return self._wildPetCounts[zoneId] or 0
end

-- 清除區域中的所有野生寵物
function WildPetSystem:clearZoneWildPets(world, zoneId)
    for entityId, wildPet in world:query(Components.WildPetComponent) do
        if wildPet.spawnZone == zoneId then
            world:despawn(entityId)
        end
    end
    
    self._wildPetCounts[zoneId] = 0
    print("🧹 清除區域野生寵物:", zoneId)
end

-- 獲取附近的野生寵物
function WildPetSystem:getNearbyWildPets(world, position, range)
    local nearbyPets = {}
    
    for entityId, wildPet, transform in world:query(
        Components.WildPetComponent,
        Components.TransformComponent
    ) do
        local distance = (position - transform.position).Magnitude
        if distance <= range then
            table.insert(nearbyPets, {
                entityId = entityId,
                wildPet = wildPet,
                distance = distance,
                position = transform.position
            })
        end
    end
    
    -- 按距離排序
    table.sort(nearbyPets, function(a, b)
        return a.distance < b.distance
    end)
    
    return nearbyPets
end

return WildPetSystem
