[package]
name = "petsim99lua/game"
version = "0.1.0"
registry = "https://github.com/UpliftGames/wally-index"
realm = "shared"

[dependencies]
# 現有依賴
Promise = "evaera/promise@4.0.0"
Signal = "sleitnick/signal@1.5.0"
TableUtil = "sleitnick/table-util@1.2.0"

# 新架構依賴
Matter = "matter-ecs/matter@0.8.1"
Knit = "sleitnick/knit@1.7.0"
ProfileService = "brittonfischer/profileservice@2.1.5"
Fusion = "elttob/fusion@0.3.0"
ZonePlus = "mattschrubb/zoneplus@3.2.0"
TestEZ = "roblox/testez@0.4.1"
