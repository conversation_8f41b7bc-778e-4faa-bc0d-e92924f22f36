-- UI 控制器 - 管理所有 Fusion UI 組件
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

-- Fusion 組件
local New = Fusion.New
local State = Fusion.State
local Computed = Fusion.Computed
local Children = Fusion.Children

local UIController = Knit.CreateController({
    Name = "UIController"
})

-- 響應式狀態
local playerData = State({})
local heroStats = State({
    level = 1,
    experience = 0,
    maxHealth = 150,
    currentHealth = 150,
    attack = 15,
    defense = 8
})
local currencies = State({
    coins = 1000,
    gems = 20,
    robux = 0
})
local uiState = State({
    currentScreen = "main",
    isLoading = false,
    notifications = {}
})

-- 計算屬性
local healthPercentage = Computed(function()
    local hero = heroStats:get()
    if hero.maxHealth and hero.maxHealth > 0 then
        return hero.currentHealth / hero.maxHealth
    end
    return 1
end)

local expPercentage = Computed(function()
    local hero = heroStats:get()
    if hero.level >= GameConfig.HERO.MAX_LEVEL then
        return 1
    end
    
    local requiredExp = Utils.calculateExpForLevel(hero.level + 1)
    return requiredExp > 0 and (hero.experience / requiredExp) or 0
end)

function UIController:KnitStart()
    print("🎨 UIController 啟動")
    
    -- 監聽服務端事件
    self:_connectToServices()
end

function UIController:_connectToServices()
    local PlayerService = Knit.GetService("PlayerService")
    local PetService = Knit.GetService("PetService")
    local CombatService = Knit.GetService("CombatService")
    local EconomyService = Knit.GetService("EconomyService")
    local QuestService = Knit.GetService("QuestService")

    -- 監聽玩家數據事件
    if PlayerService then
        PlayerService.OnDataLoaded:Connect(function(data)
            self:_updatePlayerData(data)
        end)

        PlayerService.OnDataUpdated:Connect(function(updateData)
            self:_updatePlayerData(updateData)
        end)

        PlayerService.OnLevelUp:Connect(function(levelUpData)
            self:_showLevelUpNotification(levelUpData)
        end)
    end

    -- 監聽寵物事件
    if PetService then
        PetService.OnPetObtained:Connect(function(data)
            self:_showPetObtainedNotification(data)
        end)

        PetService.OnPetLevelUp:Connect(function(data)
            self:_showPetLevelUpNotification(data)
        end)

        PetService.OnPokedexUpdated:Connect(function(data)
            self:_updatePokedexUI(data)
        end)
    end

    -- 監聽戰鬥事件
    if CombatService then
        CombatService.OnCombatStarted:Connect(function(data)
            self:_showCombatUI(true)
        end)

        CombatService.OnCombatEnded:Connect(function(data)
            self:_showCombatUI(false)
        end)

        CombatService.OnDamageDealt:Connect(function(data)
            self:_showDamageNumber(data.damage, "dealt")
        end)

        CombatService.OnDamageReceived:Connect(function(data)
            self:_showDamageNumber(data.damage, "received")
        end)
    end

    -- 監聽經濟事件
    if EconomyService then
        EconomyService.OnCurrencyChanged:Connect(function(data)
            self:_updateCurrencyDisplay(data)
        end)

        EconomyService.OnPurchaseCompleted:Connect(function(data)
            self:_showPurchaseNotification(data)
        end)
    end

    -- 監聽任務事件
    if QuestService then
        QuestService.OnQuestCompleted:Connect(function(data)
            self:_showQuestCompletedNotification(data)
        end)

        QuestService.OnQuestProgressUpdated:Connect(function(data)
            self:_updateQuestProgress(data)
        end)
    end
end

function UIController:_updatePlayerData(data)
    playerData:set(data)
    
    if data.hero then
        heroStats:set(data.hero)
    end
    
    if data.currencies then
        currencies:set(data.currencies)
    end
end

function UIController:_showLevelUpNotification(levelUpData)
    print("🎉 等級提升通知:", levelUpData.newLevel)
    self:ShowNotification("等級提升到 " .. levelUpData.newLevel .. "！", "success")

    -- 播放音效
    local AudioController = Knit.GetController("AudioController")
    if AudioController then
        AudioController:playUISound("level_up")
    end
end

function UIController:_showPetObtainedNotification(data)
    local message = "獲得新寵物：" .. data.petData.name
    if data.petData.isShiny then
        message = message .. " ✨"
    end

    self:ShowNotification(message, "success")
    print("🐾 寵物獲得通知:", data.petData.name)
end

function UIController:_showPetLevelUpNotification(data)
    local message = data.name .. " 升級到 Lv." .. data.newLevel .. "！"
    self:ShowNotification(message, "success")
    print("📈 寵物升級通知:", data.name, data.newLevel)
end

function UIController:_updatePokedexUI(data)
    print("📖 圖鑑更新:", data.speciesId, "完成度:", math.floor(data.completionRate * 100) .. "%")
end

function UIController:_showCombatUI(inCombat)
    uiState:set(Utils.mergeTables(uiState:get(), {
        inCombat = inCombat
    }))
    print("⚔️ 戰鬥UI", inCombat and "顯示" or "隱藏")
end

function UIController:_showDamageNumber(damage, type)
    print("💥 傷害數字:", damage, type)
    -- 這裡可以添加傷害數字動畫
end

function UIController:_updateCurrencyDisplay(data)
    local current = currencies:get()
    local newCurrencies = Utils.deepCopy(current)
    newCurrencies[data.currencyType] = data.newAmount

    currencies:set(newCurrencies)
    print("💰 貨幣更新:", data.currencyType, data.newAmount)
end

function UIController:_showPurchaseNotification(data)
    local message = "購買成功：" .. data.itemName .. " x" .. data.quantity
    self:ShowNotification(message, "success")
    print("🛒 購買通知:", data.itemName)
end

function UIController:_showQuestCompletedNotification(data)
    local message = "任務完成：" .. data.quest.name
    self:ShowNotification(message, "success")
    print("📋 任務完成:", data.quest.name)
end

function UIController:_updateQuestProgress(data)
    print("📊 任務進度更新:", data.progressType, data.amount)
end

function UIController:InitializeUI()
    local Players = game:GetService("Players")
    local player = Players.LocalPlayer
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- 創建主 UI
    local mainUI = self:_createMainUI()
    mainUI.Parent = playerGui
    
    print("🖼️ 主 UI 已創建")
end

function UIController:_createMainUI()
    return New "ScreenGui" {
        Name = "PetSim99UI",
        ResetOnSpawn = false,
        
        [Children] = {
            -- 頂部狀態欄
            self:_createStatusBar(),
            
            -- 底部導航欄
            self:_createNavigationBar(),
            
            -- 通知系統
            self:_createNotificationSystem()
        }
    }
end

function UIController:_createStatusBar()
    return New "Frame" {
        Name = "StatusBar",
        Size = UDim2.new(1, 0, 0, 80),
        Position = UDim2.new(0, 0, 0, 0),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        
        [Children] = {
            -- 血量條
            New "Frame" {
                Name = "HealthBar",
                Size = UDim2.new(0.25, -10, 0.3, 0),
                Position = UDim2.new(0.05, 0, 0.1, 0),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                
                [Children] = {
                    New "Frame" {
                        Name = "HealthFill",
                        Size = Computed(function()
                            local percentage = healthPercentage:get()
                            return UDim2.new(percentage, 0, 1, 0)
                        end),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Computed(function()
                            local percentage = healthPercentage:get()
                            if percentage > 0.6 then
                                return Color3.fromRGB(0, 255, 0) -- 綠色
                            elseif percentage > 0.3 then
                                return Color3.fromRGB(255, 255, 0) -- 黃色
                            else
                                return Color3.fromRGB(255, 0, 0) -- 紅色
                            end
                        end),
                        BorderSizePixel = 0
                    },
                    
                    New "TextLabel" {
                        Name = "HealthText",
                        Size = UDim2.new(1, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = Computed(function()
                            local hero = heroStats:get()
                            return string.format("%d/%d", hero.currentHealth or 0, hero.maxHealth or 0)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    }
                }
            },
            
            -- 經驗值條
            New "Frame" {
                Name = "ExpBar",
                Size = UDim2.new(0.25, -10, 0.2, 0),
                Position = UDim2.new(0.05, 0, 0.5, 0),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                
                [Children] = {
                    New "Frame" {
                        Name = "ExpFill",
                        Size = Computed(function()
                            local percentage = expPercentage:get()
                            return UDim2.new(percentage, 0, 1, 0)
                        end),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 150, 255),
                        BorderSizePixel = 0
                    },
                    
                    New "TextLabel" {
                        Name = "ExpText",
                        Size = UDim2.new(1, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = Computed(function()
                            local hero = heroStats:get()
                            return string.format("Lv.%d", hero.level or 1)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    }
                }
            },
            
            -- 貨幣顯示
            New "Frame" {
                Name = "CurrencyDisplay",
                Size = UDim2.new(0.4, 0, 0.8, 0),
                Position = UDim2.new(0.35, 0, 0.1, 0),
                BackgroundTransparency = 1,
                
                [Children] = {
                    -- 金幣
                    New "Frame" {
                        Name = "Coins",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(255, 215, 0),
                        BackgroundTransparency = 0.8,
                        BorderSizePixel = 0,
                        
                        [Children] = {
                            New "TextLabel" {
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = Computed(function()
                                    local curr = currencies:get()
                                    return "💰 " .. Utils.formatNumber(curr.coins or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }
                        }
                    },
                    
                    -- 寶石
                    New "Frame" {
                        Name = "Gems",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0.33, 5, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 255, 255),
                        BackgroundTransparency = 0.8,
                        BorderSizePixel = 0,
                        
                        [Children] = {
                            New "TextLabel" {
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = Computed(function()
                                    local curr = currencies:get()
                                    return "💎 " .. Utils.formatNumber(curr.gems or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }
                        }
                    },
                    
                    -- R幣
                    New "Frame" {
                        Name = "Robux",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0.66, 10, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 255, 0),
                        BackgroundTransparency = 0.8,
                        BorderSizePixel = 0,
                        
                        [Children] = {
                            New "TextLabel" {
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = Computed(function()
                                    local curr = currencies:get()
                                    return "R$ " .. Utils.formatNumber(curr.robux or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }
                        }
                    }
                }
            }
        }
    }
end

function UIController:_createNavigationBar()
    return New "Frame" {
        Name = "NavigationBar",
        Size = UDim2.new(1, 0, 0, 60),
        Position = UDim2.new(0, 0, 1, -60),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        
        [Children] = {
            New "TextLabel" {
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundTransparency = 1,
                Text = "🎮 導航欄 - 開發中",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.Gotham
            }
        }
    }
end

function UIController:_createNotificationSystem()
    return New "Frame" {
        Name = "NotificationSystem",
        Size = UDim2.new(0.3, 0, 0.5, 0),
        Position = UDim2.new(0.7, 0, 0.1, 0),
        BackgroundTransparency = 1,
        
        [Children] = {
            -- 通知將在這裡動態添加
        }
    }
end

-- 公共方法
function UIController:ShowNotification(message, type)
    print("📢 通知:", message, "類型:", type or "info")

    -- 播放通知音效
    local AudioController = Knit.GetController("AudioController")
    if AudioController then
        AudioController:playNotification(type or "info")
    end

    -- 這裡可以添加通知顯示邏輯
end

-- UI切換方法
function UIController:toggleInventoryUI(visible)
    print("🎒 背包UI", visible and "顯示" or "隱藏")
    -- 這裡可以添加背包UI邏輯
end

function UIController:togglePetMenuUI(visible)
    print("🐾 寵物選單UI", visible and "顯示" or "隱藏")
    -- 這裡可以添加寵物選單UI邏輯
end

function UIController:toggleQuestMenuUI(visible)
    print("📋 任務選單UI", visible and "顯示" or "隱藏")
    -- 這裡可以添加任務選單UI邏輯
end

function UIController:toggleMapUI(visible)
    print("🗺️ 地圖UI", visible and "顯示" or "隱藏")
    -- 這裡可以添加地圖UI邏輯
end

-- 數據獲取方法
function UIController:GetPlayerData()
    return playerData:get()
end

function UIController:GetHeroStats()
    return heroStats:get()
end

function UIController:GetCurrencies()
    return currencies:get()
end

function UIController:GetUIState()
    return uiState:get()
end

-- 更新方法
function UIController:UpdateHeroStats(newStats)
    heroStats:set(newStats)
end

function UIController:UpdateCurrencies(newCurrencies)
    currencies:set(newCurrencies)
end

function UIController:UpdateUIState(newState)
    uiState:set(Utils.mergeTables(uiState:get(), newState))
end

return UIController
