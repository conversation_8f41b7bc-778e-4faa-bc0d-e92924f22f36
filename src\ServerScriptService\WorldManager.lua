-- Matter 世界管理器 - 統一管理 ECS 世界和系統
local Matter = require(game.ReplicatedStorage.Packages.Matter)
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)

local WorldManager = {}
WorldManager.__index = WorldManager

-- 創建新的世界管理器
function WorldManager.new()
    local self = setmetatable({}, WorldManager)
    
    -- 創建 Matter 世界
    self.world = Matter.World.new()
    
    -- 載入所有系統
    self.systems = {}
    self:loadSystems()
    
    -- 性能監控
    self.performanceStats = {
        systemTimes = {},
        entityCount = 0,
        lastUpdate = tick()
    }
    
    -- 事件連接
    self.connections = {}
    
    print("🌍 WorldManager 已創建")
    return self
end

-- 載入所有 ECS 系統
function WorldManager:loadSystems()
    local systemsFolder = script.Parent.Systems
    
    -- 系統載入順序很重要
    local systemOrder = {
        "HeroSystem",
        "LevelingSystem",
        "MovementSystem",
        "ZoneSystem",
        "CombatSystem",
        "MonsterSystem",
        "WildPetSystem"
    }
    
    for _, systemName in ipairs(systemOrder) do
        local systemModule = systemsFolder:FindFirstChild(systemName)
        if systemModule then
            local success, system = pcall(require, systemModule)
            if success then
                self.systems[systemName] = system
                print("⚙️ 系統已載入:", systemName)
            else
                warn("❌ 系統載入失敗:", systemName, system)
            end
        else
            warn("❌ 找不到系統模組:", systemName)
        end
    end
    
    print("📦 總共載入", #self.systems, "個系統")
end

-- 啟動世界管理器
function WorldManager:start()
    local RunService = game:GetService("RunService")
    
    -- 連接到 Heartbeat 事件
    self.connections.heartbeat = RunService.Heartbeat:Connect(function(deltaTime)
        self:step(deltaTime)
    end)
    
    -- 連接到玩家事件
    self:connectPlayerEvents()
    
    print("🚀 WorldManager 已啟動")
end

-- 停止世界管理器
function WorldManager:stop()
    -- 斷開所有連接
    for _, connection in pairs(self.connections) do
        if connection then
            connection:Disconnect()
        end
    end
    self.connections = {}
    
    print("🛑 WorldManager 已停止")
end

-- 主要更新循環
function WorldManager:step(deltaTime)
    local currentTime = tick()
    
    -- 更新性能統計
    self.performanceStats.entityCount = self:getEntityCount()
    
    -- 運行所有系統
    for systemName, system in pairs(self.systems) do
        if system.step then
            local startTime = tick()
            
            local success, err = pcall(system.step, system, self.world, {
                deltaTime = deltaTime,
                currentTime = currentTime
            })
            
            local endTime = tick()
            self.performanceStats.systemTimes[systemName] = endTime - startTime
            
            if not success then
                warn("🚨 系統錯誤 [" .. systemName .. "]:", err)
            end
        end
    end
    
    -- 更新 Matter 除錯器 (僅在 Studio 中)
    if game:GetService("RunService"):IsStudio() then
        Matter.debugger(self.world)
    end
    
    self.performanceStats.lastUpdate = currentTime
end

-- 連接玩家事件
function WorldManager:connectPlayerEvents()
    local Players = game:GetService("Players")
    
    -- 玩家加入
    self.connections.playerAdded = Players.PlayerAdded:Connect(function(player)
        self:onPlayerAdded(player)
    end)
    
    -- 玩家離開
    self.connections.playerRemoving = Players.PlayerRemoving:Connect(function(player)
        self:onPlayerRemoving(player)
    end)
    
    -- 處理已經在遊戲中的玩家
    for _, player in pairs(Players:GetPlayers()) do
        self:onPlayerAdded(player)
    end
end

-- 玩家加入處理
function WorldManager:onPlayerAdded(player)
    print("👤 玩家加入 ECS 世界:", player.Name)
    
    -- 創建玩家實體
    local playerEntity = self.world:spawn(
        Components.PlayerComponent({
            userId = player.UserId,
            displayName = player.DisplayName,
            joinTime = tick(),
            isOnline = true
        }),
        Components.HeroComponent({
            level = 1,
            experience = 0,
            maxHealth = GameConfig.HERO.BASE_HEALTH,
            currentHealth = GameConfig.HERO.BASE_HEALTH,
            attack = GameConfig.HERO.BASE_ATTACK,
            defense = GameConfig.HERO.BASE_DEFENSE,
            critRate = GameConfig.HERO.BASE_CRIT_RATE,
            critDamage = GameConfig.HERO.BASE_CRIT_DAMAGE
        }),
        Components.HealthComponent({
            current = GameConfig.HERO.BASE_HEALTH,
            maximum = GameConfig.HERO.BASE_HEALTH,
            regeneration = 1,
            lastRegenTime = tick()
        }),
        Components.CurrencyComponent({
            coins = GameConfig.ECONOMY.STARTING_COINS,
            gems = GameConfig.ECONOMY.STARTING_GEMS,
            robux = GameConfig.ECONOMY.STARTING_ROBUX,
            experience = 0
        }),
        Components.TransformComponent({
            position = Vector3.new(0, 5, 0), -- 城堡中心
            rotation = Vector3.new(0, 0, 0),
            scale = Vector3.new(1, 1, 1)
        }),
        Components.ZoneComponent({
            currentZone = "castle",
            lastZoneChange = tick(),
            allowedZones = {"castle"}
        }),
        Components.MovementComponent({
            velocity = Vector3.new(0, 0, 0),
            speed = 16,
            isMoving = false,
            destination = nil,
            path = {}
        }),
        Components.CombatComponent({
            isInCombat = false,
            target = nil,
            lastAttackTime = 0,
            combatStartTime = 0
        }),
        Components.VIPComponent({
            level = 0,
            totalSpent = 0,
            premiumExpiry = 0,
            premiumActive = false,
            lastRewardClaim = 0
        }),
        Components.PokedexComponent({
            encounteredSpecies = {},
            ownedSpecies = {},
            completionRate = 0
        }),
        Components.SocialComponent({
            friends = {},
            guildId = nil,
            lastLogin = tick(),
            loginStreak = 1
        }),
        Components.SettingsComponent({
            soundEnabled = true,
            musicEnabled = true,
            notificationsEnabled = true,
            language = "zh-TW",
            graphics = "medium"
        })
    )
    
    -- 存儲玩家實體映射
    player:SetAttribute("EntityId", playerEntity)
    
    print("✅ 玩家實體已創建:", playerEntity)
end

-- 玩家離開處理
function WorldManager:onPlayerRemoving(player)
    print("👋 玩家離開 ECS 世界:", player.Name)
    
    -- 清理玩家實體
    local entityId = player:GetAttribute("EntityId")
    if entityId and self.world:contains(entityId) then
        -- 清理玩家的寵物
        self:cleanupPlayerPets(player.UserId)
        
        -- 移除玩家實體
        self.world:despawn(entityId)
        print("🗑️ 玩家實體已清理:", entityId)
    end
end

-- 清理玩家的寵物
function WorldManager:cleanupPlayerPets(userId)
    local petsToRemove = {}
    
    for entityId, pet in self.world:query(Components.PetComponent) do
        if pet.ownerId == userId then
            table.insert(petsToRemove, entityId)
        end
    end
    
    for _, petEntityId in ipairs(petsToRemove) do
        self.world:despawn(petEntityId)
    end
    
    if #petsToRemove > 0 then
        print("🐾 已清理", #petsToRemove, "隻寵物")
    end
end

-- 獲取世界實例
function WorldManager:getWorld()
    return self.world
end

-- 獲取系統實例
function WorldManager:getSystem(systemName)
    return self.systems[systemName]
end

-- 獲取實體數量
function WorldManager:getEntityCount()
    local count = 0
    for _ in self.world:query() do
        count = count + 1
    end
    return count
end

-- 獲取性能統計
function WorldManager:getPerformanceStats()
    return self.performanceStats
end

-- 查找玩家實體
function WorldManager:findPlayerEntity(userId)
    for entityId, player in self.world:query(Components.PlayerComponent) do
        if player.userId == userId then
            return entityId
        end
    end
    return nil
end

-- 查找玩家的寵物
function WorldManager:findPlayerPets(userId)
    local pets = {}
    for entityId, pet in self.world:query(Components.PetComponent) do
        if pet.ownerId == userId then
            table.insert(pets, {
                entityId = entityId,
                pet = pet
            })
        end
    end
    return pets
end

-- 創建寵物實體
function WorldManager:createPetEntity(petData, ownerId)
    local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)
    local speciesConfig = PetConfig.Species[petData.speciesId]
    
    if not speciesConfig then
        warn("找不到寵物配置:", petData.speciesId)
        return nil
    end
    
    -- 計算寵物屬性
    local stats = PetConfig:CalculatePetStats(petData.speciesId, petData.level or 1, petData.rarity)
    if not stats then
        warn("無法計算寵物屬性")
        return nil
    end
    
    -- 創建寵物實體
    local petEntity = self.world:spawn(
        Components.PetComponent({
            speciesId = petData.speciesId,
            name = petData.name or speciesConfig.name,
            rarity = petData.rarity or "common",
            level = petData.level or 1,
            experience = petData.experience or 0,
            isShiny = petData.isShiny or false,
            obtainedAt = petData.obtainedAt or tick(),
            ownerId = ownerId
        }),
        Components.PetStatsComponent({
            health = stats.health,
            attack = stats.attack,
            defense = stats.defense,
            speed = stats.speed,
            rarityMultiplier = GameConfig.PET.RARITY[string.upper(petData.rarity or "common")].multiplier
        }),
        Components.TransformComponent({
            position = Vector3.new(0, 5, 0),
            rotation = Vector3.new(0, 0, 0),
            scale = Vector3.new(1, 1, 1)
        }),
        Components.MovementComponent({
            velocity = Vector3.new(0, 0, 0),
            speed = stats.speed,
            isMoving = false,
            destination = nil,
            path = {}
        })
    )
    
    print("🐾 寵物實體已創建:", petData.name, "實體ID:", petEntity)
    return petEntity
end

-- 除錯信息
function WorldManager:printDebugInfo()
    print("🔍 WorldManager 除錯信息:")
    print("  實體數量:", self:getEntityCount())
    print("  系統數量:", #self.systems)
    print("  最後更新:", self.performanceStats.lastUpdate)
    
    print("  系統性能:")
    for systemName, time in pairs(self.performanceStats.systemTimes) do
        print("    " .. systemName .. ":", string.format("%.4f", time * 1000) .. "ms")
    end
end

return WorldManager
