-- 狀態欄組件 - 顯示血量、等級、貨幣等信息
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local StateManager = require(script.Parent.Parent.StateManager)

local StatusBar = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

function StatusBar.new()
    return scope:New "Frame" {
        Name = "StatusBar",
        Size = UDim2.new(1, 0, 0, 80),
        Position = UDim2.new(0, 0, 0, 0),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        
        [scope.Children] = {
            -- 血量條區域
            scope:New "Frame" {
                Name = "HealthSection",
                Size = UDim2.new(0.25, -10, 0.8, 0),
                Position = UDim2.new(0.05, 0, 0.1, 0),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    -- 血量條背景
                    scope:New "Frame" {
                        Name = "HealthBarBG",
                        Size = UDim2.new(1, 0, 0.4, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            -- 血量條填充
                            scope:New "Frame" {
                                Name = "HealthFill",
                                Size = scope:Computed(function(use)
                                    local percentage = use(StateManager.HealthPercentage)
                                    return UDim2.new(percentage, 0, 1, 0)
                                end),
                                Position = UDim2.new(0, 0, 0, 0),
                                BackgroundColor3 = scope:Computed(function(use)
                                    local percentage = use(StateManager.HealthPercentage)
                                    if percentage > 0.6 then
                                        return Color3.fromRGB(0, 255, 0) -- 綠色
                                    elseif percentage > 0.3 then
                                        return Color3.fromRGB(255, 255, 0) -- 黃色
                                    else
                                        return Color3.fromRGB(255, 0, 0) -- 紅色
                                    end
                                end),
                                BorderSizePixel = 0,
                                
                                [scope.Children] = {
                                    -- 血量條光澤效果
                                    scope:New "Frame" {
                                        Name = "HealthGlow",
                                        Size = UDim2.new(1, 0, 0.3, 0),
                                        Position = UDim2.new(0, 0, 0, 0),
                                        BackgroundColor3 = Color3.fromRGB(255, 255, 255),
                                        BackgroundTransparency = 0.7,
                                        BorderSizePixel = 0
                                    }
                                }
                            },
                            
                            -- 血量文字
                            scope:New "TextLabel" {
                                Name = "HealthText",
                                Size = UDim2.new(1, 0, 1, 0),
                                Position = UDim2.new(0, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local hero = use(StateManager.HeroStats)
                                    return string.format("%d/%d", hero.currentHealth or 0, hero.maxHealth or 0)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold,
                                TextStrokeTransparency = 0,
                                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                            }
                        }
                    },
                    
                    -- 經驗值條背景
                    scope:New "Frame" {
                        Name = "ExpBarBG",
                        Size = UDim2.new(1, 0, 0.25, 0),
                        Position = UDim2.new(0, 0, 0.5, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            -- 經驗值條填充
                            scope:New "Frame" {
                                Name = "ExpFill",
                                Size = scope:Computed(function(use)
                                    local percentage = use(StateManager.ExpPercentage)
                                    return UDim2.new(percentage, 0, 1, 0)
                                end),
                                Position = UDim2.new(0, 0, 0, 0),
                                BackgroundColor3 = Color3.fromRGB(0, 150, 255),
                                BorderSizePixel = 0,
                                
                                [scope.Children] = {
                                    -- 經驗值條光澤效果
                                    scope:New "Frame" {
                                        Name = "ExpGlow",
                                        Size = UDim2.new(1, 0, 0.4, 0),
                                        Position = UDim2.new(0, 0, 0, 0),
                                        BackgroundColor3 = Color3.fromRGB(255, 255, 255),
                                        BackgroundTransparency = 0.6,
                                        BorderSizePixel = 0
                                    }
                                }
                            }
                        }
                    },
                    
                    -- 等級文字
                    scope:New "TextLabel" {
                        Name = "LevelText",
                        Size = UDim2.new(1, 0, 0.25, 0),
                        Position = UDim2.new(0, 0, 0.75, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function(use)
                            local hero = use(StateManager.HeroStats)
                            return string.format("Lv.%d", hero.level or 1)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        TextStrokeTransparency = 0,
                        TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                    }
                }
            },
            
            -- 貨幣顯示區域
            scope:New "Frame" {
                Name = "CurrencySection",
                Size = UDim2.new(0.4, 0, 0.8, 0),
                Position = UDim2.new(0.35, 0, 0.1, 0),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    -- 金幣顯示
                    scope:New "Frame" {
                        Name = "CoinsFrame",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(255, 215, 0),
                        BackgroundTransparency = 0.1,
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            -- 金幣圖標
                            scope:New "ImageLabel" {
                                Name = "CoinIcon",
                                Size = UDim2.new(0.3, 0, 0.6, 0),
                                Position = UDim2.new(0.05, 0, 0.2, 0),
                                BackgroundTransparency = 1,
                                Image = "rbxassetid://6031075938", -- 金幣圖標
                                ScaleType = Enum.ScaleType.Fit
                            },
                            
                            -- 金幣數量
                            scope:New "TextLabel" {
                                Name = "CoinAmount",
                                Size = UDim2.new(0.65, 0, 1, 0),
                                Position = UDim2.new(0.35, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local formatted = use(StateManager.FormattedCurrencies)
                                    return formatted.coins
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold,
                                TextStrokeTransparency = 0,
                                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                            }
                        }
                    },
                    
                    -- 寶石顯示
                    scope:New "Frame" {
                        Name = "GemsFrame",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0.33, 5, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 255, 255),
                        BackgroundTransparency = 0.1,
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            -- 寶石圖標
                            scope:New "ImageLabel" {
                                Name = "GemIcon",
                                Size = UDim2.new(0.3, 0, 0.6, 0),
                                Position = UDim2.new(0.05, 0, 0.2, 0),
                                BackgroundTransparency = 1,
                                Image = "rbxassetid://6031097225", -- 寶石圖標
                                ScaleType = Enum.ScaleType.Fit
                            },
                            
                            -- 寶石數量
                            scope:New "TextLabel" {
                                Name = "GemAmount",
                                Size = UDim2.new(0.65, 0, 1, 0),
                                Position = UDim2.new(0.35, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local formatted = use(StateManager.FormattedCurrencies)
                                    return formatted.gems
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold,
                                TextStrokeTransparency = 0,
                                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                            }
                        }
                    },
                    
                    -- R幣顯示
                    scope:New "Frame" {
                        Name = "RobuxFrame",
                        Size = UDim2.new(0.33, -5, 1, 0),
                        Position = UDim2.new(0.66, 10, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 255, 0),
                        BackgroundTransparency = 0.1,
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            -- R幣圖標
                            scope:New "ImageLabel" {
                                Name = "RobuxIcon",
                                Size = UDim2.new(0.3, 0, 0.6, 0),
                                Position = UDim2.new(0.05, 0, 0.2, 0),
                                BackgroundTransparency = 1,
                                Image = "rbxassetid://6031094678", -- R幣圖標
                                ScaleType = Enum.ScaleType.Fit
                            },
                            
                            -- R幣數量
                            scope:New "TextLabel" {
                                Name = "RobuxAmount",
                                Size = UDim2.new(0.65, 0, 1, 0),
                                Position = UDim2.new(0.35, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function(use)
                                    local formatted = use(StateManager.FormattedCurrencies)
                                    return formatted.robux
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold,
                                TextStrokeTransparency = 0,
                                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                            }
                        }
                    }
                }
            },
            
            -- 戰鬥狀態指示器
            scope:New "Frame" {
                Name = "CombatIndicator",
                Size = UDim2.new(0.15, 0, 0.6, 0),
                Position = UDim2.new(0.8, 0, 0.2, 0),
                BackgroundColor3 = scope:Computed(function(use)
                    local combat = use(StateManager.Combat)
                    return combat.isInCombat and Color3.fromRGB(255, 0, 0) or Color3.fromRGB(0, 255, 0)
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Visible = scope:Computed(function(use)
                    local combat = use(StateManager.Combat)
                    return combat.isInCombat
                end),
                
                [scope.Children] = {
                    scope:New "TextLabel" {
                        Name = "CombatText",
                        Size = UDim2.new(1, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = "戰鬥中",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        TextStrokeTransparency = 0,
                        TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                    }
                }
            }
        }
    }
end

function StatusBar.cleanup()
    scope:doCleanup()
end

return StatusBar
