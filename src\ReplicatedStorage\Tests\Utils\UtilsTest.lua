-- 工具函數測試
return function()
    local Utils = require(game.ReplicatedStorage.Shared.Utils)
    
    describe("Utils", function()
        it("應該正確計算升級所需經驗", function()
            -- 測試經驗值計算
            local testCases = {
                { level = 1, expectedExp = 0 },
                { level = 2, expectedExp = 100 },
                { level = 3, expectedExp = 250 },
                { level = 4, expectedExp = 450 },
                { level = 5, expectedExp = 625 },
                { level = 10, expectedExp = 2500 }
            }
            
            for _, testCase in ipairs(testCases) do
                local calculatedExp = Utils.calculateExpForLevel(testCase.level)
                expect(calculatedExp).to.equal(testCase.expectedExp)
            end
        end)
        
        it("應該正確格式化數字", function()
            -- 測試數字格式化
            local testCases = {
                { number = 123, expected = "123" },
                { number = 1234, expected = "1,234" },
                { number = 12345, expected = "12,345" },
                { number = 123456, expected = "123,456" },
                { number = 1234567, expected = "1,234,567" },
                { number = 1000000, expected = "1,000,000" }
            }
            
            for _, testCase in ipairs(testCases) do
                local formatted = Utils.formatNumber(testCase.number)
                expect(formatted).to.equal(testCase.expected)
            end
        end)
        
        it("應該正確格式化大數字", function()
            -- 測試大數字簡化格式
            local testCases = {
                { number = 1000, expected = "1K" },
                { number = 1500, expected = "1.5K" },
                { number = 10000, expected = "10K" },
                { number = 100000, expected = "100K" },
                { number = 1000000, expected = "1M" },
                { number = 1500000, expected = "1.5M" },
                { number = 1000000000, expected = "1B" }
            }
            
            for _, testCase in ipairs(testCases) do
                local formatted = Utils.formatLargeNumber(testCase.number)
                expect(formatted).to.equal(testCase.expected)
            end
        end)
        
        it("應該正確計算距離", function()
            -- 測試2D距離計算
            local pos1 = Vector3.new(0, 0, 0)
            local pos2 = Vector3.new(3, 0, 4)
            local distance = Utils.calculateDistance(pos1, pos2)
            expect(distance).to.equal(5) -- 3-4-5 直角三角形
            
            -- 測試3D距離計算
            local pos3 = Vector3.new(1, 2, 3)
            local pos4 = Vector3.new(4, 6, 8)
            local distance3D = Utils.calculateDistance(pos3, pos4)
            local expectedDistance = math.sqrt((4-1)^2 + (6-2)^2 + (8-3)^2)
            expect(math.abs(distance3D - expectedDistance)).to.be.lessThan(0.001)
        end)
        
        it("應該正確執行權重隨機選擇", function()
            -- 測試權重隨機
            local items = {
                { id = "common", weight = 50 },
                { id = "rare", weight = 30 },
                { id = "epic", weight = 15 },
                { id = "legendary", weight = 5 }
            }
            
            -- 執行多次測試確保所有項目都可能被選中
            local results = {}
            for i = 1, 1000 do
                local selected = Utils.weightedRandom(items)
                expect(selected).to.be.ok()
                expect(selected.id).to.be.ok()
                
                results[selected.id] = (results[selected.id] or 0) + 1
            end
            
            -- 驗證所有項目都被選中過
            expect(results.common).to.be.ok()
            expect(results.rare).to.be.ok()
            expect(results.epic).to.be.ok()
            expect(results.legendary).to.be.ok()
            
            -- 驗證權重分佈大致正確（common應該最多）
            expect(results.common).to.be.greaterThan(results.rare)
            expect(results.rare).to.be.greaterThan(results.epic)
            expect(results.epic).to.be.greaterThan(results.legendary)
        end)
        
        it("應該正確深拷貝表格", function()
            -- 測試深拷貝
            local original = {
                name = "test",
                level = 5,
                stats = {
                    health = 100,
                    attack = 20
                },
                items = { "sword", "shield" }
            }
            
            local copy = Utils.deepCopy(original)
            
            -- 驗證拷貝正確
            expect(copy.name).to.equal(original.name)
            expect(copy.level).to.equal(original.level)
            expect(copy.stats.health).to.equal(original.stats.health)
            expect(copy.stats.attack).to.equal(original.stats.attack)
            expect(copy.items[1]).to.equal(original.items[1])
            expect(copy.items[2]).to.equal(original.items[2])
            
            -- 驗證是深拷貝（修改拷貝不影響原始）
            copy.stats.health = 200
            copy.items[1] = "axe"
            
            expect(original.stats.health).to.equal(100)
            expect(original.items[1]).to.equal("sword")
        end)
        
        it("應該正確合併表格", function()
            -- 測試表格合併
            local table1 = {
                name = "player1",
                level = 5,
                stats = { health = 100 }
            }
            
            local table2 = {
                level = 10,
                experience = 500,
                stats = { attack = 20 }
            }
            
            local merged = Utils.mergeTables(table1, table2)
            
            -- 驗證合併結果
            expect(merged.name).to.equal("player1") -- 保留table1的值
            expect(merged.level).to.equal(10) -- table2覆蓋table1
            expect(merged.experience).to.equal(500) -- table2的新值
            expect(merged.stats.health).to.equal(100) -- 嵌套表格合併
            expect(merged.stats.attack).to.equal(20) -- 嵌套表格合併
        end)
        
        it("應該正確檢查表格是否為空", function()
            -- 測試空表格檢查
            expect(Utils.isEmpty({})).to.equal(true)
            expect(Utils.isEmpty({ a = 1 })).to.equal(false)
            expect(Utils.isEmpty({ 1, 2, 3 })).to.equal(false)
            expect(Utils.isEmpty(nil)).to.equal(true)
        end)
        
        it("應該正確計算表格長度", function()
            -- 測試表格長度計算
            expect(Utils.tableLength({})).to.equal(0)
            expect(Utils.tableLength({ a = 1, b = 2, c = 3 })).to.equal(3)
            expect(Utils.tableLength({ 1, 2, 3, 4, 5 })).to.equal(5)
        end)
        
        it("應該正確檢查值是否在表格中", function()
            -- 測試值存在檢查
            local testTable = { "apple", "banana", "orange" }
            
            expect(Utils.contains(testTable, "apple")).to.equal(true)
            expect(Utils.contains(testTable, "banana")).to.equal(true)
            expect(Utils.contains(testTable, "grape")).to.equal(false)
            expect(Utils.contains(testTable, nil)).to.equal(false)
        end)
        
        it("應該正確限制數值範圍", function()
            -- 測試數值限制
            expect(Utils.clamp(5, 1, 10)).to.equal(5)
            expect(Utils.clamp(-5, 1, 10)).to.equal(1)
            expect(Utils.clamp(15, 1, 10)).to.equal(10)
            expect(Utils.clamp(0, 0, 1)).to.equal(0)
            expect(Utils.clamp(1, 0, 1)).to.equal(1)
        end)
        
        it("應該正確進行線性插值", function()
            -- 測試線性插值
            expect(Utils.lerp(0, 10, 0)).to.equal(0)
            expect(Utils.lerp(0, 10, 1)).to.equal(10)
            expect(Utils.lerp(0, 10, 0.5)).to.equal(5)
            expect(Utils.lerp(10, 20, 0.3)).to.equal(13)
        end)
        
        it("應該正確生成隨機數", function()
            -- 測試隨機數生成
            for i = 1, 100 do
                local randomInt = Utils.randomInt(1, 10)
                expect(randomInt).to.be.greaterThanOrEqualTo(1)
                expect(randomInt).to.be.lessThanOrEqualTo(10)
                expect(randomInt % 1).to.equal(0) -- 確保是整數
            end
            
            for i = 1, 100 do
                local randomFloat = Utils.randomFloat(0, 1)
                expect(randomFloat).to.be.greaterThanOrEqualTo(0)
                expect(randomFloat).to.be.lessThanOrEqualTo(1)
            end
        end)
        
        it("應該正確計算百分比", function()
            -- 測試百分比計算
            expect(Utils.percentage(25, 100)).to.equal(25)
            expect(Utils.percentage(1, 4)).to.equal(25)
            expect(Utils.percentage(3, 4)).to.equal(75)
            expect(Utils.percentage(0, 100)).to.equal(0)
            expect(Utils.percentage(100, 100)).to.equal(100)
        end)
        
        it("應該正確四捨五入數字", function()
            -- 測試四捨五入
            expect(Utils.round(3.14159, 2)).to.equal(3.14)
            expect(Utils.round(3.14159, 0)).to.equal(3)
            expect(Utils.round(3.6)).to.equal(4)
            expect(Utils.round(3.4)).to.equal(3)
            expect(Utils.round(-3.6)).to.equal(-4)
        end)
        
        it("應該正確轉換時間格式", function()
            -- 測試時間格式化
            expect(Utils.formatTime(0)).to.equal("00:00")
            expect(Utils.formatTime(60)).to.equal("01:00")
            expect(Utils.formatTime(90)).to.equal("01:30")
            expect(Utils.formatTime(3661)).to.equal("01:01:01")
        end)
        
        it("應該正確生成UUID", function()
            -- 測試UUID生成
            local uuid1 = Utils.generateUUID()
            local uuid2 = Utils.generateUUID()
            
            expect(uuid1).to.be.a("string")
            expect(uuid2).to.be.a("string")
            expect(uuid1).to.never.equal(uuid2) -- UUID應該是唯一的
            expect(#uuid1).to.be.greaterThan(10) -- UUID應該有合理的長度
        end)
        
        it("應該正確驗證輸入", function()
            -- 測試輸入驗證
            expect(Utils.isValidPlayerName("Player123")).to.equal(true)
            expect(Utils.isValidPlayerName("")).to.equal(false)
            expect(Utils.isValidPlayerName("A")).to.equal(false) -- 太短
            expect(Utils.isValidPlayerName(string.rep("A", 50))).to.equal(false) -- 太長
            
            expect(Utils.isValidEmail("<EMAIL>")).to.equal(true)
            expect(Utils.isValidEmail("invalid-email")).to.equal(false)
            expect(Utils.isValidEmail("")).to.equal(false)
        end)
    end)
end
