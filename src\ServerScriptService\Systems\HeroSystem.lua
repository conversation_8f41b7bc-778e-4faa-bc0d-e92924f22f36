-- 英雄系統 - 處理英雄升級、經驗值和屬性計算
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local HeroSystem = {}

function HeroSystem:step(world, state)
    -- 處理英雄經驗值和升級
    for entityId, hero, currency, player in world:query(
        Components.HeroComponent,
        Components.CurrencyComponent,
        Components.PlayerComponent
    ) do
        -- 檢查是否有足夠經驗值升級
        local requiredExp = Utils.calculateExpForLevel(hero.level + 1)
        
        if hero.experience >= requiredExp and hero.level < GameConfig.HERO.MAX_LEVEL then
            self:levelUpHero(world, entityId, hero, player)
        end
        
        -- 血量回復 (每秒回復1點，最多回復到最大血量)
        if hero.currentHealth < hero.maxHealth then
            local newHealth = math.min(hero.maxHealth, hero.currentHealth + state.deltaTime)
            
            world:insert(entityId, Components.HeroComponent({
                level = hero.level,
                experience = hero.experience,
                maxHealth = hero.maxHealth,
                currentHealth = newHealth,
                attack = hero.attack,
                defense = hero.defense,
                critRate = hero.critRate,
                critDamage = hero.critDamage
            }))
        end
    end
end

function HeroSystem:levelUpHero(world, entityId, hero, player)
    local newLevel = hero.level + 1
    
    -- 計算新屬性 (韓式慢速成長)
    local newMaxHealth = GameConfig.HERO.BASE_HEALTH + (newLevel * GameConfig.HERO.HEALTH_PER_LEVEL)
    local newAttack = GameConfig.HERO.BASE_ATTACK + (newLevel * GameConfig.HERO.ATTACK_PER_LEVEL)
    local newDefense = GameConfig.HERO.BASE_DEFENSE + (newLevel * GameConfig.HERO.DEFENSE_PER_LEVEL)
    
    -- 計算剩餘經驗值
    local requiredExp = Utils.calculateExpForLevel(newLevel)
    local remainingExp = hero.experience - requiredExp
    
    -- 更新英雄組件
    world:insert(entityId, Components.HeroComponent({
        level = newLevel,
        experience = remainingExp,
        maxHealth = newMaxHealth,
        currentHealth = newMaxHealth, -- 升級時完全回復血量
        attack = newAttack,
        defense = newDefense,
        critRate = hero.critRate,
        critDamage = hero.critDamage
    }))
    
    -- 通知升級事件
    self:notifyLevelUp(player, newLevel, {
        maxHealth = newMaxHealth,
        attack = newAttack,
        defense = newDefense
    })
    
    print("🎉 玩家升級:", player.displayName, "等級:", newLevel)
end

function HeroSystem:notifyLevelUp(player, newLevel, newStats)
    -- 獲取 PlayerService 來發送事件
    local Knit = require(game.ReplicatedStorage.Packages.Knit)
    local PlayerService = Knit.GetService("PlayerService")
    
    if PlayerService then
        -- 找到對應的 Roblox Player 對象
        local robloxPlayer = game.Players:GetPlayerByUserId(player.userId)
        if robloxPlayer then
            PlayerService.Client.OnLevelUp:Fire(robloxPlayer, {
                newLevel = newLevel,
                newStats = newStats,
                timestamp = tick()
            })
        end
    end
end

-- 給英雄增加經驗值
function HeroSystem:giveExperience(world, entityId, amount)
    local hero = world:get(entityId, Components.HeroComponent)
    if not hero then
        return false
    end
    
    world:insert(entityId, Components.HeroComponent({
        level = hero.level,
        experience = hero.experience + amount,
        maxHealth = hero.maxHealth,
        currentHealth = hero.currentHealth,
        attack = hero.attack,
        defense = hero.defense,
        critRate = hero.critRate,
        critDamage = hero.critDamage
    }))
    
    return true
end

-- 治療英雄
function HeroSystem:healHero(world, entityId, amount)
    local hero = world:get(entityId, Components.HeroComponent)
    if not hero then
        return false
    end
    
    local newHealth = math.min(hero.maxHealth, hero.currentHealth + amount)
    
    world:insert(entityId, Components.HeroComponent({
        level = hero.level,
        experience = hero.experience,
        maxHealth = hero.maxHealth,
        currentHealth = newHealth,
        attack = hero.attack,
        defense = hero.defense,
        critRate = hero.critRate,
        critDamage = hero.critDamage
    }))
    
    return true
end

-- 對英雄造成傷害
function HeroSystem:damageHero(world, entityId, amount)
    local hero = world:get(entityId, Components.HeroComponent)
    if not hero then
        return false
    end
    
    local newHealth = math.max(0, hero.currentHealth - amount)
    
    world:insert(entityId, Components.HeroComponent({
        level = hero.level,
        experience = hero.experience,
        maxHealth = hero.maxHealth,
        currentHealth = newHealth,
        attack = hero.attack,
        defense = hero.defense,
        critRate = hero.critRate,
        critDamage = hero.critDamage
    }))
    
    -- 檢查是否死亡
    if newHealth <= 0 then
        self:onHeroDeath(world, entityId)
    end
    
    return true
end

-- 英雄死亡處理
function HeroSystem:onHeroDeath(world, entityId)
    local player = world:get(entityId, Components.PlayerComponent)
    if player then
        print("💀 英雄死亡:", player.displayName)
        
        -- 可以在這裡添加死亡懲罰邏輯
        -- 例如：失去一些金幣、經驗值等
        
        -- 復活英雄 (回復到最大血量的50%)
        local hero = world:get(entityId, Components.HeroComponent)
        if hero then
            local reviveHealth = math.floor(hero.maxHealth * 0.5)
            
            world:insert(entityId, Components.HeroComponent({
                level = hero.level,
                experience = hero.experience,
                maxHealth = hero.maxHealth,
                currentHealth = reviveHealth,
                attack = hero.attack,
                defense = hero.defense,
                critRate = hero.critRate,
                critDamage = hero.critDamage
            }))
            
            print("🔄 英雄已復活:", player.displayName)
        end
    end
end

-- 獲取英雄總戰力
function HeroSystem:getHeroPower(hero)
    return hero.attack + hero.defense + (hero.maxHealth / 10) + (hero.critRate * 1000) + (hero.critDamage * 100)
end

-- 檢查英雄是否可以進入指定區域
function HeroSystem:canEnterZone(hero, zoneName)
    local zoneConfig = GameConfig.ZONES[string.upper(zoneName)]
    if not zoneConfig then
        return false
    end
    
    return hero.level >= zoneConfig.unlockLevel
end

return HeroSystem
