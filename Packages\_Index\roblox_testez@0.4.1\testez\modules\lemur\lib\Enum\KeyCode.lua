local createEnum = import("../createEnum")

return createEnum("KeyCode", {
	Unknown = 0,
	Backspace = 8,
	Tab = 9,
	Clear = 12,
	Return = 13,
	Pause = 19,
	Escape = 27,
	Space = 32,
	QuotedDouble = 34,
	Hash = 35,
	Dollar = 36,
	Percent = 37,
	Ampersand = 38,
	Quote = 39,
	LeftParenthesis = 40,
	RightParenthesis = 41,
	Asterisk = 42,
	Plus = 43,
	Comma = 44,
	Minus = 45,
	Period = 46,
	Slash = 47,
	<PERSON> = 48,
	One = 49,
	<PERSON> = 50,
	<PERSON> = 51,
	<PERSON> = 52,
	<PERSON> = 53,
	<PERSON> = 54,
	Seven = 55,
	Eight = 56,
	Nine = 57,
	Colon = 58,
	Semicolon = 59,
	LessThan = 60,
	Equals = 61,
	<PERSON><PERSON><PERSON> = 62,
	Question = 63,
	At = 64,
	LeftBracket = 91,
	BackSlash = 92,
	RightBracket = 93,
	Caret = 94,
	Underscore = 95,
	Backquote = 96,
	A = 97,
	B = 98,
	C = 99,
	D = 100,
	E = 101,
	F = 102,
	G = 103,
	H = 104,
	I = 105,
	J = 106,
	K = 107,
	<PERSON> = 108,
	<PERSON> = 109,
	N = 110,
	O = 111,
	P = 112,
	Q = 113,
	R = 114,
	S = 115,
	T = 116,
	U = 117,
	V = 118,
	W = 119,
	X = 120,
	Y = 121,
	Z = 122,
	LeftCurly = 123,
	<PERSON>pe = 124,
	RightCurly = 125,
	Tilde = 126,
	<PERSON>ete = 127,
	KeypadZero = 256,
	KeypadOne = 257,
	KeypadTwo = 258,
	KeypadThree = 259,
	KeypadFour = 260,
	KeypadFive = 261,
	KeypadSix = 262,
	KeypadSeven = 263,
	KeypadEight = 264,
	KeypadNine = 265,
	KeypadPeriod = 266,
	KeypadDivide = 267,
	KeypadMultiply = 268,
	KeypadMinus = 269,
	KeypadPlus = 270,
	KeypadEnter = 271,
	KeypadEquals = 272,
	Up = 273,
	Down = 274,
	Right = 275,
	Left = 276,
	Insert = 277,
	Home = 278,
	End = 279,
	PageUp = 280,
	PageDown = 281,
	LeftShift = 304,
	RightShift = 303,
	LeftMeta = 310,
	RightMeta = 309,
	LeftAlt = 308,
	RightAlt = 307,
	LeftControl = 306,
	RightControl = 305,
	CapsLock = 301,
	NumLock = 300,
	ScrollLock = 302,
	LeftSuper = 311,
	RightSuper = 312,
	Mode = 313,
	Compose = 314,
	Help = 315,
	Print = 316,
	SysReq = 317,
	Break = 318,
	Menu = 319,
	Power = 320,
	Euro = 321,
	Undo = 322,
	F1 = 282,
	F2 = 283,
	F3 = 284,
	F4 = 285,
	F5 = 286,
	F6 = 287,
	F7 = 288,
	F8 = 289,
	F9 = 290,
	F10 = 291,
	F11 = 292,
	F12 = 293,
	F13 = 294,
	F14 = 295,
	F15 = 296,
	World0 = 160,
	World1 = 161,
	World2 = 162,
	World3 = 163,
	World4 = 164,
	World5 = 165,
	World6 = 166,
	World7 = 167,
	World8 = 168,
	World9 = 169,
	World10 = 170,
	World11 = 171,
	World12 = 172,
	World13 = 173,
	World14 = 174,
	World15 = 175,
	World16 = 176,
	World17 = 177,
	World18 = 178,
	World19 = 179,
	World20 = 180,
	World21 = 181,
	World22 = 182,
	World23 = 183,
	World24 = 184,
	World25 = 185,
	World26 = 186,
	World27 = 187,
	World28 = 188,
	World29 = 189,
	World30 = 190,
	World31 = 191,
	World32 = 192,
	World33 = 193,
	World34 = 194,
	World35 = 195,
	World36 = 196,
	World37 = 197,
	World38 = 198,
	World39 = 199,
	World40 = 200,
	World41 = 201,
	World42 = 202,
	World43 = 203,
	World44 = 204,
	World45 = 205,
	World46 = 206,
	World47 = 207,
	World48 = 208,
	World49 = 209,
	World50 = 210,
	World51 = 211,
	World52 = 212,
	World53 = 213,
	World54 = 214,
	World55 = 215,
	World56 = 216,
	World57 = 217,
	World58 = 218,
	World59 = 219,
	World60 = 220,
	World61 = 221,
	World62 = 222,
	World63 = 223,
	World64 = 224,
	World65 = 225,
	World66 = 226,
	World67 = 227,
	World68 = 228,
	World69 = 229,
	World70 = 230,
	World71 = 231,
	World72 = 232,
	World73 = 233,
	World74 = 234,
	World75 = 235,
	World76 = 236,
	World77 = 237,
	World78 = 238,
	World79 = 239,
	World80 = 240,
	World81 = 241,
	World82 = 242,
	World83 = 243,
	World84 = 244,
	World85 = 245,
	World86 = 246,
	World87 = 247,
	World88 = 248,
	World89 = 249,
	World90 = 250,
	World91 = 251,
	World92 = 252,
	World93 = 253,
	World94 = 254,
	World95 = 255,
	ButtonX = 1000,
	ButtonY = 1001,
	ButtonA = 1002,
	ButtonB = 1003,
	ButtonR1 = 1004,
	ButtonL1 = 1005,
	ButtonR2 = 1006,
	ButtonL2 = 1007,
	ButtonR3 = 1008,
	ButtonL3 = 1009,
	ButtonStart = 1010,
	ButtonSelect = 1011,
	DPadLeft = 1012,
	DPadRight = 1013,
	DPadUp = 1014,
	DPadDown = 1015,
	Thumbstick1 = 1016,
	Thumbstick2 = 1017,
})