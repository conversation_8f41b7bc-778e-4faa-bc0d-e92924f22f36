# This file was @generated by generate-roblox-std at 2021-11-16 22:40:35.345060800 -07:00
[selene]
base = "lua51"
name = "roblox"
[selene.structs.BasePart."*"]
struct = "Instance"

[selene.structs.BasePart.AncestryChanged]
struct = "Event"

[selene.structs.BasePart.Anchored]
property = true
writable = "overridden"

[selene.structs.BasePart.ApplyAngularImpulse]
method = true

[[selene.structs.BasePart.ApplyAngularImpulse.args]]
required = false
type = "any"

[selene.structs.BasePart.ApplyImpulse]
method = true

[[selene.structs.BasePart.ApplyImpulse.args]]
required = false
type = "any"

[selene.structs.BasePart.ApplyImpulseAtPosition]
method = true

[[selene.structs.BasePart.ApplyImpulseAtPosition.args]]
required = false
type = "any"

[[selene.structs.BasePart.ApplyImpulseAtPosition.args]]
required = false
type = "any"

[selene.structs.BasePart.Archivable]
property = true
writable = "overridden"

[selene.structs.BasePart.AssemblyAngularVelocity]
any = true

[selene.structs.BasePart.AssemblyCenterOfMass]
any = true

[selene.structs.BasePart.AssemblyLinearVelocity]
any = true

[selene.structs.BasePart.AssemblyMass]
property = true

[selene.structs.BasePart.AssemblyRootPart]
struct = "BasePart"

[selene.structs.BasePart.AttributeChanged]
struct = "Event"

[selene.structs.BasePart.BackSurface]
property = true
writable = "overridden"

[selene.structs.BasePart.BottomSurface]
property = true
writable = "overridden"

[selene.structs.BasePart.BreakJoints]
method = true
args = []

[selene.structs.BasePart.BrickColor]
property = true
writable = "overridden"

[selene.structs.BasePart.CFrame]
any = true

[selene.structs.BasePart.CanCollide]
property = true
writable = "overridden"

[selene.structs.BasePart.CanCollideWith]
method = true

[[selene.structs.BasePart.CanCollideWith.args]]
required = false
type = "any"

[selene.structs.BasePart.CanQuery]
property = true
writable = "overridden"

[selene.structs.BasePart.CanSetNetworkOwnership]
method = true
args = []

[selene.structs.BasePart.CanTouch]
property = true
writable = "overridden"

[selene.structs.BasePart.CastShadow]
property = true
writable = "overridden"

[selene.structs.BasePart.CenterOfMass]
any = true

[selene.structs.BasePart.Changed]
struct = "Event"

[selene.structs.BasePart.ChildAdded]
struct = "Event"

[selene.structs.BasePart.ChildRemoved]
struct = "Event"

[selene.structs.BasePart.ClassName]
property = true

[selene.structs.BasePart.ClearAllChildren]
method = true
args = []

[selene.structs.BasePart.Clone]
method = true
args = []

[selene.structs.BasePart.CollisionGroupId]
property = true
writable = "overridden"

[selene.structs.BasePart.Color]
property = true
writable = "overridden"

[selene.structs.BasePart.CustomPhysicalProperties]
property = true
writable = "overridden"

[selene.structs.BasePart.DescendantAdded]
struct = "Event"

[selene.structs.BasePart.DescendantRemoving]
struct = "Event"

[selene.structs.BasePart.Destroy]
method = true
args = []

[selene.structs.BasePart.Destroying]
struct = "Event"

[selene.structs.BasePart.FindFirstAncestor]
method = true

[[selene.structs.BasePart.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.BasePart.FindFirstAncestorOfClass]
method = true

[[selene.structs.BasePart.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.BasePart.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.BasePart.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.BasePart.FindFirstChild]
method = true

[[selene.structs.BasePart.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.BasePart.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.BasePart.FindFirstChildOfClass]
method = true

[[selene.structs.BasePart.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.BasePart.FindFirstChildWhichIsA]
method = true

[[selene.structs.BasePart.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.BasePart.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.BasePart.FindFirstDescendant]
method = true

[[selene.structs.BasePart.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.BasePart.FrontSurface]
property = true
writable = "overridden"

[selene.structs.BasePart.GetActor]
method = true
args = []

[selene.structs.BasePart.GetAttribute]
method = true

[[selene.structs.BasePart.GetAttribute.args]]
required = false
type = "any"

[selene.structs.BasePart.GetAttributeChangedSignal]
method = true

[[selene.structs.BasePart.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.BasePart.GetAttributes]
method = true
args = []

[selene.structs.BasePart.GetChildren]
method = true
args = []

[selene.structs.BasePart.GetConnectedParts]
method = true

[[selene.structs.BasePart.GetConnectedParts.args]]
required = false
type = "any"

[selene.structs.BasePart.GetDebugId]
method = true

[[selene.structs.BasePart.GetDebugId.args]]
required = false
type = "any"

[selene.structs.BasePart.GetDescendants]
method = true
args = []

[selene.structs.BasePart.GetFullName]
method = true
args = []

[selene.structs.BasePart.GetJoints]
method = true
args = []

[selene.structs.BasePart.GetMass]
method = true
args = []

[selene.structs.BasePart.GetNetworkOwner]
method = true
args = []

[selene.structs.BasePart.GetNetworkOwnershipAuto]
method = true
args = []

[selene.structs.BasePart.GetPivot]
method = true
args = []

[selene.structs.BasePart.GetPropertyChangedSignal]
method = true

[[selene.structs.BasePart.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.BasePart.GetRootPart]
method = true
args = []

[selene.structs.BasePart.GetTouchingParts]
method = true
args = []

[selene.structs.BasePart.GetVelocityAtPosition]
method = true

[[selene.structs.BasePart.GetVelocityAtPosition.args]]
required = false
type = "any"

[selene.structs.BasePart.IsA]
method = true

[[selene.structs.BasePart.IsA.args]]
required = false
type = "any"

[selene.structs.BasePart.IsAncestorOf]
method = true

[[selene.structs.BasePart.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.BasePart.IsDescendantOf]
method = true

[[selene.structs.BasePart.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.BasePart.IsGrounded]
method = true
args = []

[selene.structs.BasePart.LeftSurface]
property = true
writable = "overridden"

[selene.structs.BasePart.LocalTransparencyModifier]
property = true
writable = "overridden"

[selene.structs.BasePart.Locked]
property = true
writable = "overridden"

[selene.structs.BasePart.MakeJoints]
method = true
args = []

[selene.structs.BasePart.Mass]
property = true

[selene.structs.BasePart.Massless]
property = true
writable = "overridden"

[selene.structs.BasePart.Material]
property = true
writable = "overridden"

[selene.structs.BasePart.Name]
property = true
writable = "overridden"

[selene.structs.BasePart.Orientation]
any = true

[selene.structs.BasePart."Origin Orientation"]
any = true

[selene.structs.BasePart."Origin Position"]
any = true

[selene.structs.BasePart.Parent]
struct = "Instance"

[selene.structs.BasePart."Pivot Offset Orientation"]
any = true

[selene.structs.BasePart."Pivot Offset Position"]
any = true

[selene.structs.BasePart.PivotOffset]
any = true

[selene.structs.BasePart.PivotTo]
method = true

[[selene.structs.BasePart.PivotTo.args]]
required = false
type = "any"

[selene.structs.BasePart.Position]
any = true

[selene.structs.BasePart.ReceiveAge]
property = true

[selene.structs.BasePart.Reflectance]
property = true
writable = "overridden"

[selene.structs.BasePart.Resize]
method = true

[[selene.structs.BasePart.Resize.args]]
required = false
type = "any"

[[selene.structs.BasePart.Resize.args]]
required = false
type = "any"

[selene.structs.BasePart.ResizeIncrement]
property = true

[selene.structs.BasePart.ResizeableFaces]
property = true

[selene.structs.BasePart.RightSurface]
property = true
writable = "overridden"

[selene.structs.BasePart.RootPriority]
property = true
writable = "overridden"

[selene.structs.BasePart.Rotation]
any = true

[selene.structs.BasePart.SetAttribute]
method = true

[[selene.structs.BasePart.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.BasePart.SetAttribute.args]]
required = false
type = "any"

[selene.structs.BasePart.SetNetworkOwner]
method = true

[[selene.structs.BasePart.SetNetworkOwner.args]]
required = false
type = "any"

[selene.structs.BasePart.SetNetworkOwnershipAuto]
method = true
args = []

[selene.structs.BasePart.Size]
any = true

[selene.structs.BasePart.SubtractAsync]
method = true

[[selene.structs.BasePart.SubtractAsync.args]]
required = false
type = "any"

[[selene.structs.BasePart.SubtractAsync.args]]
required = false
type = "any"

[[selene.structs.BasePart.SubtractAsync.args]]
required = false
type = "any"

[selene.structs.BasePart.TopSurface]
property = true
writable = "overridden"

[selene.structs.BasePart.TouchEnded]
struct = "Event"

[selene.structs.BasePart.Touched]
struct = "Event"

[selene.structs.BasePart.Transparency]
property = true
writable = "overridden"

[selene.structs.BasePart.UnionAsync]
method = true

[[selene.structs.BasePart.UnionAsync.args]]
required = false
type = "any"

[[selene.structs.BasePart.UnionAsync.args]]
required = false
type = "any"

[[selene.structs.BasePart.UnionAsync.args]]
required = false
type = "any"

[selene.structs.BasePart.WaitForChild]
method = true

[[selene.structs.BasePart.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.BasePart.WaitForChild.args]]
required = false
type = "any"
[selene.structs.Camera."*"]
struct = "Instance"

[selene.structs.Camera.AncestryChanged]
struct = "Event"

[selene.structs.Camera.Archivable]
property = true
writable = "overridden"

[selene.structs.Camera.AttributeChanged]
struct = "Event"

[selene.structs.Camera.CFrame]
any = true

[selene.structs.Camera.CameraSubject]
struct = "Instance"

[selene.structs.Camera.CameraType]
property = true
writable = "overridden"

[selene.structs.Camera.Changed]
struct = "Event"

[selene.structs.Camera.ChildAdded]
struct = "Event"

[selene.structs.Camera.ChildRemoved]
struct = "Event"

[selene.structs.Camera.ClassName]
property = true

[selene.structs.Camera.ClearAllChildren]
method = true
args = []

[selene.structs.Camera.Clone]
method = true
args = []

[selene.structs.Camera.DescendantAdded]
struct = "Event"

[selene.structs.Camera.DescendantRemoving]
struct = "Event"

[selene.structs.Camera.Destroy]
method = true
args = []

[selene.structs.Camera.Destroying]
struct = "Event"

[selene.structs.Camera.DiagonalFieldOfView]
property = true
writable = "overridden"

[selene.structs.Camera.FieldOfView]
property = true
writable = "overridden"

[selene.structs.Camera.FieldOfViewMode]
property = true
writable = "overridden"

[selene.structs.Camera.FindFirstAncestor]
method = true

[[selene.structs.Camera.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.Camera.FindFirstAncestorOfClass]
method = true

[[selene.structs.Camera.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.Camera.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.Camera.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.Camera.FindFirstChild]
method = true

[[selene.structs.Camera.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.Camera.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.Camera.FindFirstChildOfClass]
method = true

[[selene.structs.Camera.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.Camera.FindFirstChildWhichIsA]
method = true

[[selene.structs.Camera.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.Camera.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.Camera.FindFirstDescendant]
method = true

[[selene.structs.Camera.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.Camera.FirstPersonTransition]
struct = "Event"

[selene.structs.Camera.Focus]
any = true

[selene.structs.Camera.GetActor]
method = true
args = []

[selene.structs.Camera.GetAttribute]
method = true

[[selene.structs.Camera.GetAttribute.args]]
required = false
type = "any"

[selene.structs.Camera.GetAttributeChangedSignal]
method = true

[[selene.structs.Camera.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.Camera.GetAttributes]
method = true
args = []

[selene.structs.Camera.GetChildren]
method = true
args = []

[selene.structs.Camera.GetDebugId]
method = true

[[selene.structs.Camera.GetDebugId.args]]
required = false
type = "any"

[selene.structs.Camera.GetDescendants]
method = true
args = []

[selene.structs.Camera.GetFullName]
method = true
args = []

[selene.structs.Camera.GetPanSpeed]
method = true
args = []

[selene.structs.Camera.GetPartsObscuringTarget]
method = true

[[selene.structs.Camera.GetPartsObscuringTarget.args]]
required = false
type = "any"

[[selene.structs.Camera.GetPartsObscuringTarget.args]]
required = false
type = "any"

[selene.structs.Camera.GetPropertyChangedSignal]
method = true

[[selene.structs.Camera.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.Camera.GetRenderCFrame]
method = true
args = []

[selene.structs.Camera.GetRoll]
method = true
args = []

[selene.structs.Camera.GetTiltSpeed]
method = true
args = []

[selene.structs.Camera.HeadLocked]
property = true
writable = "overridden"

[selene.structs.Camera.HeadScale]
property = true
writable = "overridden"

[selene.structs.Camera.InterpolationFinished]
struct = "Event"

[selene.structs.Camera.IsA]
method = true

[[selene.structs.Camera.IsA.args]]
required = false
type = "any"

[selene.structs.Camera.IsAncestorOf]
method = true

[[selene.structs.Camera.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.Camera.IsDescendantOf]
method = true

[[selene.structs.Camera.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.Camera.MaxAxisFieldOfView]
property = true
writable = "overridden"

[selene.structs.Camera.Name]
property = true
writable = "overridden"

[selene.structs.Camera.NearPlaneZ]
property = true

[selene.structs.Camera.Parent]
struct = "Instance"

[selene.structs.Camera.ScreenPointToRay]
method = true

[[selene.structs.Camera.ScreenPointToRay.args]]
required = false
type = "any"

[[selene.structs.Camera.ScreenPointToRay.args]]
required = false
type = "any"

[[selene.structs.Camera.ScreenPointToRay.args]]
required = false
type = "any"

[selene.structs.Camera.SetAttribute]
method = true

[[selene.structs.Camera.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.Camera.SetAttribute.args]]
required = false
type = "any"

[selene.structs.Camera.SetCameraPanMode]
method = true

[[selene.structs.Camera.SetCameraPanMode.args]]
required = false
type = "any"

[selene.structs.Camera.SetImageServerView]
method = true

[[selene.structs.Camera.SetImageServerView.args]]
required = false
type = "any"

[selene.structs.Camera.SetRoll]
method = true

[[selene.structs.Camera.SetRoll.args]]
required = false
type = "any"

[selene.structs.Camera.ViewportPointToRay]
method = true

[[selene.structs.Camera.ViewportPointToRay.args]]
required = false
type = "any"

[[selene.structs.Camera.ViewportPointToRay.args]]
required = false
type = "any"

[[selene.structs.Camera.ViewportPointToRay.args]]
required = false
type = "any"

[selene.structs.Camera.ViewportSize]
any = true

[selene.structs.Camera.WaitForChild]
method = true

[[selene.structs.Camera.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.Camera.WaitForChild.args]]
required = false
type = "any"

[selene.structs.Camera.WorldToScreenPoint]
method = true

[[selene.structs.Camera.WorldToScreenPoint.args]]
required = false
type = "any"

[selene.structs.Camera.WorldToViewportPoint]
method = true

[[selene.structs.Camera.WorldToViewportPoint.args]]
required = false
type = "any"

[selene.structs.Camera.Zoom]
method = true

[[selene.structs.Camera.Zoom.args]]
required = false
type = "any"
[selene.structs.DataModel."*"]
struct = "Instance"

[selene.structs.DataModel.AncestryChanged]
struct = "Event"

[selene.structs.DataModel.Archivable]
property = true
writable = "overridden"

[selene.structs.DataModel.AttributeChanged]
struct = "Event"

[selene.structs.DataModel.BindToClose]
method = true

[[selene.structs.DataModel.BindToClose.args]]
required = false
type = "any"

[selene.structs.DataModel.Changed]
struct = "Event"

[selene.structs.DataModel.ChildAdded]
struct = "Event"

[selene.structs.DataModel.ChildRemoved]
struct = "Event"

[selene.structs.DataModel.ClassName]
property = true

[selene.structs.DataModel.ClearAllChildren]
method = true
args = []

[selene.structs.DataModel.Clone]
method = true
args = []

[selene.structs.DataModel.Close]
struct = "Event"

[selene.structs.DataModel.CloseLate]
struct = "Event"

[selene.structs.DataModel.CreatorId]
property = true

[selene.structs.DataModel.CreatorType]
property = true

[selene.structs.DataModel.DefineFastFlag]
method = true

[[selene.structs.DataModel.DefineFastFlag.args]]
required = false
type = "any"

[[selene.structs.DataModel.DefineFastFlag.args]]
required = false
type = "any"

[selene.structs.DataModel.DefineFastInt]
method = true

[[selene.structs.DataModel.DefineFastInt.args]]
required = false
type = "any"

[[selene.structs.DataModel.DefineFastInt.args]]
required = false
type = "any"

[selene.structs.DataModel.DefineFastString]
method = true

[[selene.structs.DataModel.DefineFastString.args]]
required = false
type = "any"

[[selene.structs.DataModel.DefineFastString.args]]
required = false
type = "any"

[selene.structs.DataModel.DescendantAdded]
struct = "Event"

[selene.structs.DataModel.DescendantRemoving]
struct = "Event"

[selene.structs.DataModel.Destroy]
method = true
args = []

[selene.structs.DataModel.Destroying]
struct = "Event"

[selene.structs.DataModel.FindFirstAncestor]
method = true

[[selene.structs.DataModel.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.DataModel.FindFirstAncestorOfClass]
method = true

[[selene.structs.DataModel.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.DataModel.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.DataModel.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.DataModel.FindFirstChild]
method = true

[[selene.structs.DataModel.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.DataModel.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.DataModel.FindFirstChildOfClass]
method = true

[[selene.structs.DataModel.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.DataModel.FindFirstChildWhichIsA]
method = true

[[selene.structs.DataModel.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.DataModel.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.DataModel.FindFirstDescendant]
method = true

[[selene.structs.DataModel.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.DataModel.FindService]
method = true

[[selene.structs.DataModel.FindService.args]]
required = false
type = "any"

[selene.structs.DataModel.GameId]
property = true

[selene.structs.DataModel.Genre]
property = true

[selene.structs.DataModel.GetActor]
method = true
args = []

[selene.structs.DataModel.GetAttribute]
method = true

[[selene.structs.DataModel.GetAttribute.args]]
required = false
type = "any"

[selene.structs.DataModel.GetAttributeChangedSignal]
method = true

[[selene.structs.DataModel.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.DataModel.GetAttributes]
method = true
args = []

[selene.structs.DataModel.GetChildren]
method = true
args = []

[selene.structs.DataModel.GetDebugId]
method = true

[[selene.structs.DataModel.GetDebugId.args]]
required = false
type = "any"

[selene.structs.DataModel.GetDescendants]
method = true
args = []

[selene.structs.DataModel.GetEngineFeature]
method = true

[[selene.structs.DataModel.GetEngineFeature.args]]
required = false
type = "any"

[selene.structs.DataModel.GetFastFlag]
method = true

[[selene.structs.DataModel.GetFastFlag.args]]
required = false
type = "any"

[selene.structs.DataModel.GetFastInt]
method = true

[[selene.structs.DataModel.GetFastInt.args]]
required = false
type = "any"

[selene.structs.DataModel.GetFastString]
method = true

[[selene.structs.DataModel.GetFastString.args]]
required = false
type = "any"

[selene.structs.DataModel.GetFullName]
method = true
args = []

[selene.structs.DataModel.GetJobsInfo]
method = true
args = []

[selene.structs.DataModel.GetObjects]
method = true

[[selene.structs.DataModel.GetObjects.args]]
required = false
type = "any"

[selene.structs.DataModel.GetObjectsAsync]
method = true

[[selene.structs.DataModel.GetObjectsAsync.args]]
required = false
type = "any"

[selene.structs.DataModel.GetObjectsList]
method = true

[[selene.structs.DataModel.GetObjectsList.args]]
required = false
type = "any"

[selene.structs.DataModel.GetPropertyChangedSignal]
method = true

[[selene.structs.DataModel.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.DataModel.GetService]
method = true

[[selene.structs.DataModel.GetService.args]]
type = ["ABTestService", "AdService", "AnalyticsService", "AnimationClipProvider", "AppUpdateService", "AssetCounterService", "AssetDeliveryProxy", "AssetImportService", "AssetManagerService", "AssetService", "AvatarEditorService", "AvatarImportService", "BadgeService", "CoreGui", "StarterGui", "BreakpointManager", "BrowserService", "BulkImportService", "CSGCacheService", "CacheableContentProvider", "MeshContentProvider", "SolidModelContentProvider", "CalloutService", "ChangeHistoryService", "Chat", "ClusterPacketCache", "CollectionService", "CommandService", "ConfigureServerService", "ContentProvider", "ContextActionService", "ControllerService", "CookiesService", "CorePackages", "CoreScriptSyncService", "DataModelPatchService", "DataStoreService", "Debris", "DebuggerConnectionManager", "DebuggerManager", "DebuggerUIService", "DraftsService", "DraggerService", "EventIngestService", "FlagStandService", "FlyweightService", "CSGDictionaryService", "NonReplicatedCSGDictionaryService", "FriendService", "GamePassService", "GamepadService", "Geometry", "GoogleAnalyticsConfiguration", "GroupService", "GuiService", "GuidRegistryService", "HapticService", "HeightmapImporterService", "Hopper", "HttpRbxApiService", "HttpService", "ILegacyStudioBridge", "LegacyStudioBridge", "IXPService", "IncrementalPatchBuilder", "InsertService", "InternalContainer", "JointsService", "KeyboardService", "KeyframeSequenceProvider", "LanguageService", "Lighting", "LocalStorageService", "AppStorageService", "UserStorageService", "LocalizationService", "LodDataService", "LogService", "LoginService", "LuaWebService", "LuauScriptAnalyzerService", "MarketplaceService", "MaterialService", "MemStorageService", "MemoryStoreService", "MessageBusService", "MessagingService", "MouseService", "NetworkClient", "NetworkServer", "NetworkSettings", "NotificationService", "Workspace", "PackageService", "PackageUIService", "PathfindingService", "PermissionsService", "PhysicsService", "PlayerEmulatorService", "Players", "PluginDebugService", "PluginGuiService", "PluginPolicyService", "PointsService", "PolicyService", "ProcessInstancePhysicsService", "ProximityPromptService", "PublishService", "RbxAnalyticsService", "RemoteDebuggerServer", "RenderSettings", "ReplicatedFirst", "ReplicatedScriptService", "ReplicatedStorage", "RobloxPluginGuiService", "RobloxReplicatedStorage", "RunService", "RuntimeScriptService", "ScriptChangeService", "ScriptContext", "ScriptService", "Selection", "ServerScriptService", "ServerStorage", "SessionService", "SocialService", "SoundService", "SpawnerService", "StarterPack", "StarterPlayer", "Stats", "StopWatchReporter", "Studio", "StudioAssetService", "StudioData", "StudioDeviceEmulatorService", "StudioScriptDebugEventListener", "StudioService", "TaskScheduler", "Teams", "TeleportService", "TemporaryCageMeshProvider", "TemporaryScriptService", "TestService", "TextChatService", "TextService", "ThirdPartyUserService", "TimerService", "ToastNotificationService", "ToolboxService", "TouchInputService", "TracerService", "TweenService", "UGCValidationService", "UnvalidatedAssetService", "UserInputService", "UserService", "VRService", "VersionControlService", "VirtualInputManager", "VirtualUser", "Visit", "VoiceChatInternal", "VoiceChatService"]

[selene.structs.DataModel.GraphicsQualityChangeRequest]
struct = "Event"

[selene.structs.DataModel.HttpGetAsync]
method = true

[[selene.structs.DataModel.HttpGetAsync.args]]
required = false
type = "any"

[[selene.structs.DataModel.HttpGetAsync.args]]
required = false
type = "any"

[selene.structs.DataModel.HttpPostAsync]
method = true

[[selene.structs.DataModel.HttpPostAsync.args]]
required = false
type = "any"

[[selene.structs.DataModel.HttpPostAsync.args]]
required = false
type = "any"

[[selene.structs.DataModel.HttpPostAsync.args]]
required = false
type = "any"

[[selene.structs.DataModel.HttpPostAsync.args]]
required = false
type = "any"

[selene.structs.DataModel.InsertObjectsAndJoinIfLegacyAsync]
method = true

[[selene.structs.DataModel.InsertObjectsAndJoinIfLegacyAsync.args]]
required = false
type = "any"

[selene.structs.DataModel.IsA]
method = true

[[selene.structs.DataModel.IsA.args]]
required = false
type = "any"

[selene.structs.DataModel.IsAncestorOf]
method = true

[[selene.structs.DataModel.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.DataModel.IsDescendantOf]
method = true

[[selene.structs.DataModel.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.DataModel.IsLoaded]
method = true
args = []

[selene.structs.DataModel.JobId]
property = true

[selene.structs.DataModel.Load]
method = true

[[selene.structs.DataModel.Load.args]]
required = false
type = "any"

[selene.structs.DataModel.Loaded]
struct = "Event"

[selene.structs.DataModel.Name]
property = true
writable = "overridden"

[selene.structs.DataModel.OpenScreenshotsFolder]
method = true
args = []

[selene.structs.DataModel.OpenVideosFolder]
method = true
args = []

[selene.structs.DataModel.Parent]
struct = "Instance"

[selene.structs.DataModel.PlaceId]
property = true

[selene.structs.DataModel.PlaceVersion]
property = true

[selene.structs.DataModel.PrivateServerId]
property = true

[selene.structs.DataModel.PrivateServerOwnerId]
property = true

[selene.structs.DataModel.ReportInGoogleAnalytics]
method = true

[[selene.structs.DataModel.ReportInGoogleAnalytics.args]]
required = false
type = "any"

[[selene.structs.DataModel.ReportInGoogleAnalytics.args]]
required = false
type = "any"

[[selene.structs.DataModel.ReportInGoogleAnalytics.args]]
required = false
type = "any"

[[selene.structs.DataModel.ReportInGoogleAnalytics.args]]
required = false
type = "any"

[selene.structs.DataModel.ScreenshotReady]
struct = "Event"

[selene.structs.DataModel.ScreenshotSavedToAlbum]
struct = "Event"

[selene.structs.DataModel.ServiceAdded]
struct = "Event"

[selene.structs.DataModel.ServiceRemoving]
struct = "Event"

[selene.structs.DataModel.SetAttribute]
method = true

[[selene.structs.DataModel.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.DataModel.SetAttribute.args]]
required = false
type = "any"

[selene.structs.DataModel.SetFastFlagForTesting]
method = true

[[selene.structs.DataModel.SetFastFlagForTesting.args]]
required = false
type = "any"

[[selene.structs.DataModel.SetFastFlagForTesting.args]]
required = false
type = "any"

[selene.structs.DataModel.SetFastIntForTesting]
method = true

[[selene.structs.DataModel.SetFastIntForTesting.args]]
required = false
type = "any"

[[selene.structs.DataModel.SetFastIntForTesting.args]]
required = false
type = "any"

[selene.structs.DataModel.SetFastStringForTesting]
method = true

[[selene.structs.DataModel.SetFastStringForTesting.args]]
required = false
type = "any"

[[selene.structs.DataModel.SetFastStringForTesting.args]]
required = false
type = "any"

[selene.structs.DataModel.SetPlaceId]
method = true

[[selene.structs.DataModel.SetPlaceId.args]]
required = false
type = "any"

[selene.structs.DataModel.SetUniverseId]
method = true

[[selene.structs.DataModel.SetUniverseId.args]]
required = false
type = "any"

[selene.structs.DataModel.Shutdown]
method = true
args = []

[selene.structs.DataModel.WaitForChild]
method = true

[[selene.structs.DataModel.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.DataModel.WaitForChild.args]]
required = false
type = "any"

[selene.structs.DataModel.Workspace]
struct = "Workspace"
[selene.structs.EnumItem.Name]
property = true

[selene.structs.EnumItem.Value]
property = true
[selene.structs.Event.Connect]
method = true

[[selene.structs.Event.Connect.args]]
type = "function"

[selene.structs.Event.Wait]
method = true
args = []
[selene.structs.Instance."*"]
any = true
[selene.structs.Plugin."*"]
struct = "Instance"

[selene.structs.Plugin.Activate]
method = true

[[selene.structs.Plugin.Activate.args]]
required = false
type = "any"

[selene.structs.Plugin.AncestryChanged]
struct = "Event"

[selene.structs.Plugin.Archivable]
property = true
writable = "overridden"

[selene.structs.Plugin.AttributeChanged]
struct = "Event"

[selene.structs.Plugin.Changed]
struct = "Event"

[selene.structs.Plugin.ChildAdded]
struct = "Event"

[selene.structs.Plugin.ChildRemoved]
struct = "Event"

[selene.structs.Plugin.ClassName]
property = true

[selene.structs.Plugin.ClearAllChildren]
method = true
args = []

[selene.structs.Plugin.Clone]
method = true
args = []

[selene.structs.Plugin.CollisionEnabled]
property = true

[selene.structs.Plugin.CreateDockWidgetPluginGui]
method = true

[[selene.structs.Plugin.CreateDockWidgetPluginGui.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreateDockWidgetPluginGui.args]]
required = false
type = "any"

[selene.structs.Plugin.CreatePluginAction]
method = true

[[selene.structs.Plugin.CreatePluginAction.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreatePluginAction.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreatePluginAction.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreatePluginAction.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreatePluginAction.args]]
required = false
type = "any"

[selene.structs.Plugin.CreatePluginMenu]
method = true

[[selene.structs.Plugin.CreatePluginMenu.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreatePluginMenu.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreatePluginMenu.args]]
required = false
type = "any"

[selene.structs.Plugin.CreateQWidgetPluginGui]
method = true

[[selene.structs.Plugin.CreateQWidgetPluginGui.args]]
required = false
type = "any"

[[selene.structs.Plugin.CreateQWidgetPluginGui.args]]
required = false
type = "any"

[selene.structs.Plugin.CreateToolbar]
method = true

[[selene.structs.Plugin.CreateToolbar.args]]
required = false
type = "any"

[selene.structs.Plugin.Deactivate]
method = true
args = []

[selene.structs.Plugin.Deactivation]
struct = "Event"

[selene.structs.Plugin.DescendantAdded]
struct = "Event"

[selene.structs.Plugin.DescendantRemoving]
struct = "Event"

[selene.structs.Plugin.Destroy]
method = true
args = []

[selene.structs.Plugin.Destroying]
struct = "Event"

[selene.structs.Plugin.FindFirstAncestor]
method = true

[[selene.structs.Plugin.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.Plugin.FindFirstAncestorOfClass]
method = true

[[selene.structs.Plugin.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.Plugin.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.Plugin.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.Plugin.FindFirstChild]
method = true

[[selene.structs.Plugin.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.Plugin.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.Plugin.FindFirstChildOfClass]
method = true

[[selene.structs.Plugin.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.Plugin.FindFirstChildWhichIsA]
method = true

[[selene.structs.Plugin.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.Plugin.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.Plugin.FindFirstDescendant]
method = true

[[selene.structs.Plugin.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.Plugin.GetActor]
method = true
args = []

[selene.structs.Plugin.GetAttribute]
method = true

[[selene.structs.Plugin.GetAttribute.args]]
required = false
type = "any"

[selene.structs.Plugin.GetAttributeChangedSignal]
method = true

[[selene.structs.Plugin.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.Plugin.GetAttributes]
method = true
args = []

[selene.structs.Plugin.GetChildren]
method = true
args = []

[selene.structs.Plugin.GetDebugId]
method = true

[[selene.structs.Plugin.GetDebugId.args]]
required = false
type = "any"

[selene.structs.Plugin.GetDescendants]
method = true
args = []

[selene.structs.Plugin.GetFullName]
method = true
args = []

[selene.structs.Plugin.GetItem]
method = true

[[selene.structs.Plugin.GetItem.args]]
required = false
type = "any"

[[selene.structs.Plugin.GetItem.args]]
required = false
type = "any"

[selene.structs.Plugin.GetJoinMode]
method = true
args = []

[selene.structs.Plugin.GetMouse]
method = true
args = []

[selene.structs.Plugin.GetPropertyChangedSignal]
method = true

[[selene.structs.Plugin.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.Plugin.GetSelectedRibbonTool]
method = true
args = []

[selene.structs.Plugin.GetSetting]
method = true

[[selene.structs.Plugin.GetSetting.args]]
required = false
type = "any"

[selene.structs.Plugin.GridSize]
property = true

[selene.structs.Plugin.ImportFbxAnimation]
method = true

[[selene.structs.Plugin.ImportFbxAnimation.args]]
required = false
type = "any"

[[selene.structs.Plugin.ImportFbxAnimation.args]]
required = false
type = "any"

[selene.structs.Plugin.ImportFbxRig]
method = true

[[selene.structs.Plugin.ImportFbxRig.args]]
required = false
type = "any"

[selene.structs.Plugin.Invoke]
method = true

[[selene.structs.Plugin.Invoke.args]]
required = false
type = "any"

[[selene.structs.Plugin.Invoke.args]]
required = false
type = "any"

[selene.structs.Plugin.IsA]
method = true

[[selene.structs.Plugin.IsA.args]]
required = false
type = "any"

[selene.structs.Plugin.IsActivated]
method = true
args = []

[selene.structs.Plugin.IsActivatedWithExclusiveMouse]
method = true
args = []

[selene.structs.Plugin.IsAncestorOf]
method = true

[[selene.structs.Plugin.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.Plugin.IsDescendantOf]
method = true

[[selene.structs.Plugin.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.Plugin.Name]
property = true
writable = "overridden"

[selene.structs.Plugin.Negate]
method = true

[[selene.structs.Plugin.Negate.args]]
required = false
type = "any"

[selene.structs.Plugin.OnInvoke]
method = true

[[selene.structs.Plugin.OnInvoke.args]]
required = false
type = "any"

[[selene.structs.Plugin.OnInvoke.args]]
required = false
type = "any"

[selene.structs.Plugin.OnSetItem]
method = true

[[selene.structs.Plugin.OnSetItem.args]]
required = false
type = "any"

[[selene.structs.Plugin.OnSetItem.args]]
required = false
type = "any"

[selene.structs.Plugin.OpenScript]
method = true

[[selene.structs.Plugin.OpenScript.args]]
required = false
type = "any"

[[selene.structs.Plugin.OpenScript.args]]
required = false
type = "any"

[selene.structs.Plugin.OpenWikiPage]
method = true

[[selene.structs.Plugin.OpenWikiPage.args]]
required = false
type = "any"

[selene.structs.Plugin.Parent]
struct = "Instance"

[selene.structs.Plugin.PauseSound]
method = true

[[selene.structs.Plugin.PauseSound.args]]
required = false
type = "any"

[selene.structs.Plugin.PlaySound]
method = true

[[selene.structs.Plugin.PlaySound.args]]
required = false
type = "any"

[[selene.structs.Plugin.PlaySound.args]]
required = false
type = "any"

[selene.structs.Plugin.PromptForExistingAssetId]
method = true

[[selene.structs.Plugin.PromptForExistingAssetId.args]]
required = false
type = "any"

[selene.structs.Plugin.PromptSaveSelection]
method = true

[[selene.structs.Plugin.PromptSaveSelection.args]]
required = false
type = "any"

[selene.structs.Plugin.Ready]
struct = "Event"

[selene.structs.Plugin.ResumeSound]
method = true

[[selene.structs.Plugin.ResumeSound.args]]
required = false
type = "any"

[selene.structs.Plugin.SaveSelectedToRoblox]
method = true
args = []

[selene.structs.Plugin.SelectRibbonTool]
method = true

[[selene.structs.Plugin.SelectRibbonTool.args]]
required = false
type = "any"

[[selene.structs.Plugin.SelectRibbonTool.args]]
required = false
type = "any"

[selene.structs.Plugin.Separate]
method = true

[[selene.structs.Plugin.Separate.args]]
required = false
type = "any"

[selene.structs.Plugin.SetAttribute]
method = true

[[selene.structs.Plugin.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.Plugin.SetAttribute.args]]
required = false
type = "any"

[selene.structs.Plugin.SetItem]
method = true

[[selene.structs.Plugin.SetItem.args]]
required = false
type = "any"

[[selene.structs.Plugin.SetItem.args]]
required = false
type = "any"

[selene.structs.Plugin.SetReady]
method = true
args = []

[selene.structs.Plugin.SetSetting]
method = true

[[selene.structs.Plugin.SetSetting.args]]
required = false
type = "any"

[[selene.structs.Plugin.SetSetting.args]]
required = false
type = "any"

[selene.structs.Plugin.StartDecalDrag]
method = true

[[selene.structs.Plugin.StartDecalDrag.args]]
required = false
type = "any"

[selene.structs.Plugin.StartDrag]
method = true

[[selene.structs.Plugin.StartDrag.args]]
required = false
type = "any"

[selene.structs.Plugin.StopAllSounds]
method = true
args = []

[selene.structs.Plugin.Union]
method = true

[[selene.structs.Plugin.Union.args]]
required = false
type = "any"

[selene.structs.Plugin.Unloading]
struct = "Event"

[selene.structs.Plugin.WaitForChild]
method = true

[[selene.structs.Plugin.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.Plugin.WaitForChild.args]]
required = false
type = "any"
[selene.structs.Script."*"]
struct = "Instance"

[selene.structs.Script.AncestryChanged]
struct = "Event"

[selene.structs.Script.Archivable]
property = true
writable = "overridden"

[selene.structs.Script.AttributeChanged]
struct = "Event"

[selene.structs.Script.Changed]
struct = "Event"

[selene.structs.Script.ChildAdded]
struct = "Event"

[selene.structs.Script.ChildRemoved]
struct = "Event"

[selene.structs.Script.ClassName]
property = true

[selene.structs.Script.ClearAllChildren]
method = true
args = []

[selene.structs.Script.Clone]
method = true
args = []

[selene.structs.Script.CurrentEditor]
struct = "Instance"

[selene.structs.Script.DescendantAdded]
struct = "Event"

[selene.structs.Script.DescendantRemoving]
struct = "Event"

[selene.structs.Script.Destroy]
method = true
args = []

[selene.structs.Script.Destroying]
struct = "Event"

[selene.structs.Script.Disabled]
property = true
writable = "overridden"

[selene.structs.Script.FindFirstAncestor]
method = true

[[selene.structs.Script.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.Script.FindFirstAncestorOfClass]
method = true

[[selene.structs.Script.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.Script.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.Script.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.Script.FindFirstChild]
method = true

[[selene.structs.Script.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.Script.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.Script.FindFirstChildOfClass]
method = true

[[selene.structs.Script.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.Script.FindFirstChildWhichIsA]
method = true

[[selene.structs.Script.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.Script.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.Script.FindFirstDescendant]
method = true

[[selene.structs.Script.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.Script.GetActor]
method = true
args = []

[selene.structs.Script.GetAttribute]
method = true

[[selene.structs.Script.GetAttribute.args]]
required = false
type = "any"

[selene.structs.Script.GetAttributeChangedSignal]
method = true

[[selene.structs.Script.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.Script.GetAttributes]
method = true
args = []

[selene.structs.Script.GetChildren]
method = true
args = []

[selene.structs.Script.GetDebugId]
method = true

[[selene.structs.Script.GetDebugId.args]]
required = false
type = "any"

[selene.structs.Script.GetDescendants]
method = true
args = []

[selene.structs.Script.GetFullName]
method = true
args = []

[selene.structs.Script.GetHash]
method = true
args = []

[selene.structs.Script.GetPropertyChangedSignal]
method = true

[[selene.structs.Script.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.Script.IsA]
method = true

[[selene.structs.Script.IsA.args]]
required = false
type = "any"

[selene.structs.Script.IsAncestorOf]
method = true

[[selene.structs.Script.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.Script.IsDescendantOf]
method = true

[[selene.structs.Script.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.Script.LinkedSource]
property = true
writable = "overridden"

[selene.structs.Script.Name]
property = true
writable = "overridden"

[selene.structs.Script.Parent]
struct = "Instance"

[selene.structs.Script.SetAttribute]
method = true

[[selene.structs.Script.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.Script.SetAttribute.args]]
required = false
type = "any"

[selene.structs.Script.WaitForChild]
method = true

[[selene.structs.Script.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.Script.WaitForChild.args]]
required = false
type = "any"
[selene.structs.Terrain."*"]
struct = "Instance"

[selene.structs.Terrain.AncestryChanged]
struct = "Event"

[selene.structs.Terrain.Anchored]
property = true
writable = "overridden"

[selene.structs.Terrain.ApplyAngularImpulse]
method = true

[[selene.structs.Terrain.ApplyAngularImpulse.args]]
required = false
type = "any"

[selene.structs.Terrain.ApplyImpulse]
method = true

[[selene.structs.Terrain.ApplyImpulse.args]]
required = false
type = "any"

[selene.structs.Terrain.ApplyImpulseAtPosition]
method = true

[[selene.structs.Terrain.ApplyImpulseAtPosition.args]]
required = false
type = "any"

[[selene.structs.Terrain.ApplyImpulseAtPosition.args]]
required = false
type = "any"

[selene.structs.Terrain.Archivable]
property = true
writable = "overridden"

[selene.structs.Terrain.AssemblyAngularVelocity]
any = true

[selene.structs.Terrain.AssemblyCenterOfMass]
any = true

[selene.structs.Terrain.AssemblyLinearVelocity]
any = true

[selene.structs.Terrain.AssemblyMass]
property = true

[selene.structs.Terrain.AssemblyRootPart]
struct = "BasePart"

[selene.structs.Terrain.AttributeChanged]
struct = "Event"

[selene.structs.Terrain.BackSurface]
property = true
writable = "overridden"

[selene.structs.Terrain.BottomSurface]
property = true
writable = "overridden"

[selene.structs.Terrain.BreakJoints]
method = true
args = []

[selene.structs.Terrain.BrickColor]
property = true
writable = "overridden"

[selene.structs.Terrain.CFrame]
any = true

[selene.structs.Terrain.CanCollide]
property = true
writable = "overridden"

[selene.structs.Terrain.CanCollideWith]
method = true

[[selene.structs.Terrain.CanCollideWith.args]]
required = false
type = "any"

[selene.structs.Terrain.CanQuery]
property = true
writable = "overridden"

[selene.structs.Terrain.CanSetNetworkOwnership]
method = true
args = []

[selene.structs.Terrain.CanTouch]
property = true
writable = "overridden"

[selene.structs.Terrain.CastShadow]
property = true
writable = "overridden"

[selene.structs.Terrain.CellCenterToWorld]
method = true

[[selene.structs.Terrain.CellCenterToWorld.args]]
required = false
type = "any"

[[selene.structs.Terrain.CellCenterToWorld.args]]
required = false
type = "any"

[[selene.structs.Terrain.CellCenterToWorld.args]]
required = false
type = "any"

[selene.structs.Terrain.CellCornerToWorld]
method = true

[[selene.structs.Terrain.CellCornerToWorld.args]]
required = false
type = "any"

[[selene.structs.Terrain.CellCornerToWorld.args]]
required = false
type = "any"

[[selene.structs.Terrain.CellCornerToWorld.args]]
required = false
type = "any"

[selene.structs.Terrain.CenterOfMass]
any = true

[selene.structs.Terrain.Changed]
struct = "Event"

[selene.structs.Terrain.ChildAdded]
struct = "Event"

[selene.structs.Terrain.ChildRemoved]
struct = "Event"

[selene.structs.Terrain.ClassName]
property = true

[selene.structs.Terrain.Clear]
method = true
args = []

[selene.structs.Terrain.ClearAllChildren]
method = true
args = []

[selene.structs.Terrain.Clone]
method = true
args = []

[selene.structs.Terrain.CollisionGroupId]
property = true
writable = "overridden"

[selene.structs.Terrain.Color]
property = true
writable = "overridden"

[selene.structs.Terrain.CopyRegion]
method = true

[[selene.structs.Terrain.CopyRegion.args]]
required = false
type = "any"

[selene.structs.Terrain.CountCells]
method = true
args = []

[selene.structs.Terrain.CustomPhysicalProperties]
property = true
writable = "overridden"

[selene.structs.Terrain.Decoration]
property = true
writable = "overridden"

[selene.structs.Terrain.DescendantAdded]
struct = "Event"

[selene.structs.Terrain.DescendantRemoving]
struct = "Event"

[selene.structs.Terrain.Destroy]
method = true
args = []

[selene.structs.Terrain.Destroying]
struct = "Event"

[selene.structs.Terrain.FillBall]
method = true

[[selene.structs.Terrain.FillBall.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillBall.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillBall.args]]
required = false
type = "any"

[selene.structs.Terrain.FillBlock]
method = true

[[selene.structs.Terrain.FillBlock.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillBlock.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillBlock.args]]
required = false
type = "any"

[selene.structs.Terrain.FillCylinder]
method = true

[[selene.structs.Terrain.FillCylinder.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillCylinder.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillCylinder.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillCylinder.args]]
required = false
type = "any"

[selene.structs.Terrain.FillRegion]
method = true

[[selene.structs.Terrain.FillRegion.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillRegion.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillRegion.args]]
required = false
type = "any"

[selene.structs.Terrain.FillWedge]
method = true

[[selene.structs.Terrain.FillWedge.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillWedge.args]]
required = false
type = "any"

[[selene.structs.Terrain.FillWedge.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstAncestor]
method = true

[[selene.structs.Terrain.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstAncestorOfClass]
method = true

[[selene.structs.Terrain.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.Terrain.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstChild]
method = true

[[selene.structs.Terrain.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.Terrain.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstChildOfClass]
method = true

[[selene.structs.Terrain.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstChildWhichIsA]
method = true

[[selene.structs.Terrain.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.Terrain.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.Terrain.FindFirstDescendant]
method = true

[[selene.structs.Terrain.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.Terrain.FrontSurface]
property = true
writable = "overridden"

[selene.structs.Terrain.GetActor]
method = true
args = []

[selene.structs.Terrain.GetAttribute]
method = true

[[selene.structs.Terrain.GetAttribute.args]]
required = false
type = "any"

[selene.structs.Terrain.GetAttributeChangedSignal]
method = true

[[selene.structs.Terrain.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.Terrain.GetAttributes]
method = true
args = []

[selene.structs.Terrain.GetChildren]
method = true
args = []

[selene.structs.Terrain.GetConnectedParts]
method = true

[[selene.structs.Terrain.GetConnectedParts.args]]
required = false
type = "any"

[selene.structs.Terrain.GetDebugId]
method = true

[[selene.structs.Terrain.GetDebugId.args]]
required = false
type = "any"

[selene.structs.Terrain.GetDescendants]
method = true
args = []

[selene.structs.Terrain.GetFullName]
method = true
args = []

[selene.structs.Terrain.GetJoints]
method = true
args = []

[selene.structs.Terrain.GetMass]
method = true
args = []

[selene.structs.Terrain.GetMaterialColor]
method = true

[[selene.structs.Terrain.GetMaterialColor.args]]
required = false
type = "any"

[selene.structs.Terrain.GetNetworkOwner]
method = true
args = []

[selene.structs.Terrain.GetNetworkOwnershipAuto]
method = true
args = []

[selene.structs.Terrain.GetPivot]
method = true
args = []

[selene.structs.Terrain.GetPropertyChangedSignal]
method = true

[[selene.structs.Terrain.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.Terrain.GetRootPart]
method = true
args = []

[selene.structs.Terrain.GetTouchingParts]
method = true
args = []

[selene.structs.Terrain.GetVelocityAtPosition]
method = true

[[selene.structs.Terrain.GetVelocityAtPosition.args]]
required = false
type = "any"

[selene.structs.Terrain.IsA]
method = true

[[selene.structs.Terrain.IsA.args]]
required = false
type = "any"

[selene.structs.Terrain.IsAncestorOf]
method = true

[[selene.structs.Terrain.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.Terrain.IsDescendantOf]
method = true

[[selene.structs.Terrain.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.Terrain.IsGrounded]
method = true
args = []

[selene.structs.Terrain.LeftSurface]
property = true
writable = "overridden"

[selene.structs.Terrain.LocalTransparencyModifier]
property = true
writable = "overridden"

[selene.structs.Terrain.Locked]
property = true
writable = "overridden"

[selene.structs.Terrain.MakeJoints]
method = true
args = []

[selene.structs.Terrain.Mass]
property = true

[selene.structs.Terrain.Massless]
property = true
writable = "overridden"

[selene.structs.Terrain.Material]
property = true
writable = "overridden"

[selene.structs.Terrain.MaterialColors]
property = true
writable = "overridden"

[selene.structs.Terrain.MaxExtents]
property = true

[selene.structs.Terrain.Name]
property = true
writable = "overridden"

[selene.structs.Terrain.Orientation]
any = true

[selene.structs.Terrain."Origin Orientation"]
any = true

[selene.structs.Terrain."Origin Position"]
any = true

[selene.structs.Terrain.Parent]
struct = "Instance"

[selene.structs.Terrain.PasteRegion]
method = true

[[selene.structs.Terrain.PasteRegion.args]]
required = false
type = "any"

[[selene.structs.Terrain.PasteRegion.args]]
required = false
type = "any"

[[selene.structs.Terrain.PasteRegion.args]]
required = false
type = "any"

[selene.structs.Terrain."Pivot Offset Orientation"]
any = true

[selene.structs.Terrain."Pivot Offset Position"]
any = true

[selene.structs.Terrain.PivotOffset]
any = true

[selene.structs.Terrain.PivotTo]
method = true

[[selene.structs.Terrain.PivotTo.args]]
required = false
type = "any"

[selene.structs.Terrain.Position]
any = true

[selene.structs.Terrain.ReadVoxels]
method = true

[[selene.structs.Terrain.ReadVoxels.args]]
required = false
type = "any"

[[selene.structs.Terrain.ReadVoxels.args]]
required = false
type = "any"

[selene.structs.Terrain.ReceiveAge]
property = true

[selene.structs.Terrain.Reflectance]
property = true
writable = "overridden"

[selene.structs.Terrain.ReplaceMaterial]
method = true

[[selene.structs.Terrain.ReplaceMaterial.args]]
required = false
type = "any"

[[selene.structs.Terrain.ReplaceMaterial.args]]
required = false
type = "any"

[[selene.structs.Terrain.ReplaceMaterial.args]]
required = false
type = "any"

[[selene.structs.Terrain.ReplaceMaterial.args]]
required = false
type = "any"

[selene.structs.Terrain.Resize]
method = true

[[selene.structs.Terrain.Resize.args]]
required = false
type = "any"

[[selene.structs.Terrain.Resize.args]]
required = false
type = "any"

[selene.structs.Terrain.ResizeIncrement]
property = true

[selene.structs.Terrain.ResizeableFaces]
property = true

[selene.structs.Terrain.RightSurface]
property = true
writable = "overridden"

[selene.structs.Terrain.RootPriority]
property = true
writable = "overridden"

[selene.structs.Terrain.Rotation]
any = true

[selene.structs.Terrain.SetAttribute]
method = true

[[selene.structs.Terrain.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.Terrain.SetAttribute.args]]
required = false
type = "any"

[selene.structs.Terrain.SetMaterialColor]
method = true

[[selene.structs.Terrain.SetMaterialColor.args]]
required = false
type = "any"

[[selene.structs.Terrain.SetMaterialColor.args]]
required = false
type = "any"

[selene.structs.Terrain.SetNetworkOwner]
method = true

[[selene.structs.Terrain.SetNetworkOwner.args]]
required = false
type = "any"

[selene.structs.Terrain.SetNetworkOwnershipAuto]
method = true
args = []

[selene.structs.Terrain.Size]
any = true

[selene.structs.Terrain.SubtractAsync]
method = true

[[selene.structs.Terrain.SubtractAsync.args]]
required = false
type = "any"

[[selene.structs.Terrain.SubtractAsync.args]]
required = false
type = "any"

[[selene.structs.Terrain.SubtractAsync.args]]
required = false
type = "any"

[selene.structs.Terrain.TopSurface]
property = true
writable = "overridden"

[selene.structs.Terrain.TouchEnded]
struct = "Event"

[selene.structs.Terrain.Touched]
struct = "Event"

[selene.structs.Terrain.Transparency]
property = true
writable = "overridden"

[selene.structs.Terrain.UnionAsync]
method = true

[[selene.structs.Terrain.UnionAsync.args]]
required = false
type = "any"

[[selene.structs.Terrain.UnionAsync.args]]
required = false
type = "any"

[[selene.structs.Terrain.UnionAsync.args]]
required = false
type = "any"

[selene.structs.Terrain.WaitForChild]
method = true

[[selene.structs.Terrain.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.Terrain.WaitForChild.args]]
required = false
type = "any"

[selene.structs.Terrain.WaterColor]
property = true
writable = "overridden"

[selene.structs.Terrain.WaterReflectance]
property = true
writable = "overridden"

[selene.structs.Terrain.WaterTransparency]
property = true
writable = "overridden"

[selene.structs.Terrain.WaterWaveSize]
property = true
writable = "overridden"

[selene.structs.Terrain.WaterWaveSpeed]
property = true
writable = "overridden"

[selene.structs.Terrain.WorldToCell]
method = true

[[selene.structs.Terrain.WorldToCell.args]]
required = false
type = "any"

[selene.structs.Terrain.WorldToCellPreferEmpty]
method = true

[[selene.structs.Terrain.WorldToCellPreferEmpty.args]]
required = false
type = "any"

[selene.structs.Terrain.WorldToCellPreferSolid]
method = true

[[selene.structs.Terrain.WorldToCellPreferSolid.args]]
required = false
type = "any"

[selene.structs.Terrain.WriteVoxels]
method = true

[[selene.structs.Terrain.WriteVoxels.args]]
required = false
type = "any"

[[selene.structs.Terrain.WriteVoxels.args]]
required = false
type = "any"

[[selene.structs.Terrain.WriteVoxels.args]]
required = false
type = "any"

[[selene.structs.Terrain.WriteVoxels.args]]
required = false
type = "any"
[selene.structs.Workspace."*"]
struct = "Instance"

[selene.structs.Workspace.AllowThirdPartySales]
property = true
writable = "overridden"

[selene.structs.Workspace.AncestryChanged]
struct = "Event"

[selene.structs.Workspace.AnimationWeightedBlendFix]
property = true
writable = "overridden"

[selene.structs.Workspace.Archivable]
property = true
writable = "overridden"

[selene.structs.Workspace.ArePartsTouchingOthers]
method = true

[[selene.structs.Workspace.ArePartsTouchingOthers.args]]
required = false
type = "any"

[[selene.structs.Workspace.ArePartsTouchingOthers.args]]
required = false
type = "any"

[selene.structs.Workspace.AttributeChanged]
struct = "Event"

[selene.structs.Workspace.BreakJoints]
method = true
args = []

[selene.structs.Workspace.BulkMoveTo]
method = true

[[selene.structs.Workspace.BulkMoveTo.args]]
required = false
type = "any"

[[selene.structs.Workspace.BulkMoveTo.args]]
required = false
type = "any"

[[selene.structs.Workspace.BulkMoveTo.args]]
required = false
type = "any"

[selene.structs.Workspace.CalculateJumpDistance]
method = true

[[selene.structs.Workspace.CalculateJumpDistance.args]]
required = false
type = "any"

[[selene.structs.Workspace.CalculateJumpDistance.args]]
required = false
type = "any"

[[selene.structs.Workspace.CalculateJumpDistance.args]]
required = false
type = "any"

[selene.structs.Workspace.CalculateJumpHeight]
method = true

[[selene.structs.Workspace.CalculateJumpHeight.args]]
required = false
type = "any"

[[selene.structs.Workspace.CalculateJumpHeight.args]]
required = false
type = "any"

[selene.structs.Workspace.CalculateJumpPower]
method = true

[[selene.structs.Workspace.CalculateJumpPower.args]]
required = false
type = "any"

[[selene.structs.Workspace.CalculateJumpPower.args]]
required = false
type = "any"

[selene.structs.Workspace.Changed]
struct = "Event"

[selene.structs.Workspace.ChildAdded]
struct = "Event"

[selene.structs.Workspace.ChildRemoved]
struct = "Event"

[selene.structs.Workspace.ClassName]
property = true

[selene.structs.Workspace.ClearAllChildren]
method = true
args = []

[selene.structs.Workspace.ClientAnimatorThrottling]
property = true
writable = "overridden"

[selene.structs.Workspace.Clone]
method = true
args = []

[selene.structs.Workspace.CurrentCamera]
struct = "Camera"

[selene.structs.Workspace.DescendantAdded]
struct = "Event"

[selene.structs.Workspace.DescendantRemoving]
struct = "Event"

[selene.structs.Workspace.Destroy]
method = true
args = []

[selene.structs.Workspace.Destroying]
struct = "Event"

[selene.structs.Workspace.DistributedGameTime]
property = true
writable = "overridden"

[selene.structs.Workspace.ExperimentalSolverIsEnabled]
method = true
args = []

[selene.structs.Workspace.FindFirstAncestor]
method = true

[[selene.structs.Workspace.FindFirstAncestor.args]]
required = false
type = "any"

[selene.structs.Workspace.FindFirstAncestorOfClass]
method = true

[[selene.structs.Workspace.FindFirstAncestorOfClass.args]]
required = false
type = "any"

[selene.structs.Workspace.FindFirstAncestorWhichIsA]
method = true

[[selene.structs.Workspace.FindFirstAncestorWhichIsA.args]]
required = false
type = "any"

[selene.structs.Workspace.FindFirstChild]
method = true

[[selene.structs.Workspace.FindFirstChild.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindFirstChild.args]]
required = false
type = "any"

[selene.structs.Workspace.FindFirstChildOfClass]
method = true

[[selene.structs.Workspace.FindFirstChildOfClass.args]]
required = false
type = "any"

[selene.structs.Workspace.FindFirstChildWhichIsA]
method = true

[[selene.structs.Workspace.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindFirstChildWhichIsA.args]]
required = false
type = "any"

[selene.structs.Workspace.FindFirstDescendant]
method = true

[[selene.structs.Workspace.FindFirstDescendant.args]]
required = false
type = "any"

[selene.structs.Workspace.FindPartsInRegion3]
method = true

[[selene.structs.Workspace.FindPartsInRegion3.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindPartsInRegion3.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindPartsInRegion3.args]]
required = false
type = "any"

[selene.structs.Workspace.FindPartsInRegion3WithIgnoreList]
method = true

[[selene.structs.Workspace.FindPartsInRegion3WithIgnoreList.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindPartsInRegion3WithIgnoreList.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindPartsInRegion3WithIgnoreList.args]]
required = false
type = "any"

[selene.structs.Workspace.FindPartsInRegion3WithWhiteList]
method = true

[[selene.structs.Workspace.FindPartsInRegion3WithWhiteList.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindPartsInRegion3WithWhiteList.args]]
required = false
type = "any"

[[selene.structs.Workspace.FindPartsInRegion3WithWhiteList.args]]
required = false
type = "any"

[selene.structs.Workspace.GetActor]
method = true
args = []

[selene.structs.Workspace.GetAttribute]
method = true

[[selene.structs.Workspace.GetAttribute.args]]
required = false
type = "any"

[selene.structs.Workspace.GetAttributeChangedSignal]
method = true

[[selene.structs.Workspace.GetAttributeChangedSignal.args]]
required = false
type = "any"

[selene.structs.Workspace.GetAttributes]
method = true
args = []

[selene.structs.Workspace.GetBoundingBox]
method = true
args = []

[selene.structs.Workspace.GetChildren]
method = true
args = []

[selene.structs.Workspace.GetDebugId]
method = true

[[selene.structs.Workspace.GetDebugId.args]]
required = false
type = "any"

[selene.structs.Workspace.GetDescendants]
method = true
args = []

[selene.structs.Workspace.GetExtentsSize]
method = true
args = []

[selene.structs.Workspace.GetFullName]
method = true
args = []

[selene.structs.Workspace.GetNumAwakeParts]
method = true
args = []

[selene.structs.Workspace.GetPartBoundsInBox]
method = true

[[selene.structs.Workspace.GetPartBoundsInBox.args]]
required = false
type = "any"

[[selene.structs.Workspace.GetPartBoundsInBox.args]]
required = false
type = "any"

[[selene.structs.Workspace.GetPartBoundsInBox.args]]
required = false
type = "any"

[selene.structs.Workspace.GetPartBoundsInRadius]
method = true

[[selene.structs.Workspace.GetPartBoundsInRadius.args]]
required = false
type = "any"

[[selene.structs.Workspace.GetPartBoundsInRadius.args]]
required = false
type = "any"

[[selene.structs.Workspace.GetPartBoundsInRadius.args]]
required = false
type = "any"

[selene.structs.Workspace.GetPartsInPart]
method = true

[[selene.structs.Workspace.GetPartsInPart.args]]
required = false
type = "any"

[[selene.structs.Workspace.GetPartsInPart.args]]
required = false
type = "any"

[selene.structs.Workspace.GetPhysicsThrottling]
method = true
args = []

[selene.structs.Workspace.GetPivot]
method = true
args = []

[selene.structs.Workspace.GetPrimaryPartCFrame]
method = true
args = []

[selene.structs.Workspace.GetPropertyChangedSignal]
method = true

[[selene.structs.Workspace.GetPropertyChangedSignal.args]]
required = false
type = "any"

[selene.structs.Workspace.GetRealPhysicsFPS]
method = true
args = []

[selene.structs.Workspace.GetServerTimeNow]
method = true
args = []

[selene.structs.Workspace.Gravity]
property = true
writable = "overridden"

[selene.structs.Workspace.HumanoidOnlySetCollisionsOnStateChange]
property = true
writable = "overridden"

[selene.structs.Workspace.IKMoveTo]
method = true

[[selene.structs.Workspace.IKMoveTo.args]]
required = false
type = "any"

[[selene.structs.Workspace.IKMoveTo.args]]
required = false
type = "any"

[[selene.structs.Workspace.IKMoveTo.args]]
required = false
type = "any"

[[selene.structs.Workspace.IKMoveTo.args]]
required = false
type = "any"

[[selene.structs.Workspace.IKMoveTo.args]]
required = false
type = "any"

[selene.structs.Workspace.IsA]
method = true

[[selene.structs.Workspace.IsA.args]]
required = false
type = "any"

[selene.structs.Workspace.IsAncestorOf]
method = true

[[selene.structs.Workspace.IsAncestorOf.args]]
required = false
type = "any"

[selene.structs.Workspace.IsDescendantOf]
method = true

[[selene.structs.Workspace.IsDescendantOf.args]]
required = false
type = "any"

[selene.structs.Workspace.IsRegion3Empty]
method = true

[[selene.structs.Workspace.IsRegion3Empty.args]]
required = false
type = "any"

[[selene.structs.Workspace.IsRegion3Empty.args]]
required = false
type = "any"

[selene.structs.Workspace.IsRegion3EmptyWithIgnoreList]
method = true

[[selene.structs.Workspace.IsRegion3EmptyWithIgnoreList.args]]
required = false
type = "any"

[[selene.structs.Workspace.IsRegion3EmptyWithIgnoreList.args]]
required = false
type = "any"

[selene.structs.Workspace.JoinToOutsiders]
method = true

[[selene.structs.Workspace.JoinToOutsiders.args]]
required = false
type = "any"

[[selene.structs.Workspace.JoinToOutsiders.args]]
required = false
type = "any"

[selene.structs.Workspace.MakeJoints]
method = true
args = []

[selene.structs.Workspace.MeshPartHeadsAndAccessories]
property = true
writable = "overridden"

[selene.structs.Workspace.MoveTo]
method = true

[[selene.structs.Workspace.MoveTo.args]]
required = false
type = "any"

[selene.structs.Workspace.Name]
property = true
writable = "overridden"

[selene.structs.Workspace."Origin Orientation"]
any = true

[selene.structs.Workspace."Origin Position"]
any = true

[selene.structs.Workspace.PGSIsEnabled]
method = true
args = []

[selene.structs.Workspace.Parent]
struct = "Instance"

[selene.structs.Workspace.PhysicsSimulationRate]
property = true
writable = "overridden"

[selene.structs.Workspace.PhysicsSteppingMethod]
property = true
writable = "overridden"

[selene.structs.Workspace."Pivot Offset Orientation"]
any = true

[selene.structs.Workspace."Pivot Offset Position"]
any = true

[selene.structs.Workspace.PivotTo]
method = true

[[selene.structs.Workspace.PivotTo.args]]
required = false
type = "any"

[selene.structs.Workspace.PrimaryPart]
struct = "BasePart"

[selene.structs.Workspace.Raycast]
method = true

[[selene.structs.Workspace.Raycast.args]]
required = false
type = "any"

[[selene.structs.Workspace.Raycast.args]]
required = false
type = "any"

[[selene.structs.Workspace.Raycast.args]]
required = false
type = "any"

[selene.structs.Workspace.Retargeting]
property = true
writable = "overridden"

[selene.structs.Workspace.SetAttribute]
method = true

[[selene.structs.Workspace.SetAttribute.args]]
required = false
type = "any"

[[selene.structs.Workspace.SetAttribute.args]]
required = false
type = "any"

[selene.structs.Workspace.SetInsertPoint]
method = true

[[selene.structs.Workspace.SetInsertPoint.args]]
required = false
type = "any"

[[selene.structs.Workspace.SetInsertPoint.args]]
required = false
type = "any"

[selene.structs.Workspace.SetMeshPartHeadsAndAccessories]
method = true

[[selene.structs.Workspace.SetMeshPartHeadsAndAccessories.args]]
required = false
type = "any"

[selene.structs.Workspace.SetPhysicsThrottleEnabled]
method = true

[[selene.structs.Workspace.SetPhysicsThrottleEnabled.args]]
required = false
type = "any"

[selene.structs.Workspace.SetPrimaryPartCFrame]
method = true

[[selene.structs.Workspace.SetPrimaryPartCFrame.args]]
required = false
type = "any"

[selene.structs.Workspace.SignalBehavior]
property = true
writable = "overridden"

[selene.structs.Workspace.StreamOutBehavior]
property = true
writable = "overridden"

[selene.structs.Workspace.StreamingMinRadius]
property = true
writable = "overridden"

[selene.structs.Workspace.StreamingPauseMode]
property = true
writable = "overridden"

[selene.structs.Workspace.StreamingTargetRadius]
property = true
writable = "overridden"

[selene.structs.Workspace.Terrain]
struct = "Terrain"

[selene.structs.Workspace.TouchesUseCollisionGroups]
property = true
writable = "overridden"

[selene.structs.Workspace.TranslateBy]
method = true

[[selene.structs.Workspace.TranslateBy.args]]
required = false
type = "any"

[selene.structs.Workspace.UnjoinFromOutsiders]
method = true

[[selene.structs.Workspace.UnjoinFromOutsiders.args]]
required = false
type = "any"

[selene.structs.Workspace.WaitForChild]
method = true

[[selene.structs.Workspace.WaitForChild.args]]
required = false
type = "any"

[[selene.structs.Workspace.WaitForChild.args]]
required = false
type = "any"

[selene.structs.Workspace."World Pivot Orientation"]
any = true

[selene.structs.Workspace."World Pivot Position"]
any = true

[selene.structs.Workspace.WorldPivot]
any = true

[selene.structs.Workspace.ZoomToExtents]
method = true
args = []
[[Axes.new.args]]
type = "..."
[BrickColor.Black]
args = []

[BrickColor.Blue]
args = []

[BrickColor.DarkGray]
args = []

[BrickColor.Gray]
args = []

[BrickColor.Green]
args = []

[BrickColor.Red]
args = []

[BrickColor.White]
args = []

[BrickColor.Yellow]
args = []
[[BrickColor.new.args]]
type = "any"

[[BrickColor.new.args]]
required = false
type = "number"

[[BrickColor.new.args]]
required = false
type = "number"
[[BrickColor.palette.args]]
type = "number"

[BrickColor.random]
args = []
[[CFrame.Angles.args]]
required = false
type = "number"

[[CFrame.Angles.args]]
required = false
type = "number"

[[CFrame.Angles.args]]
required = false
type = "number"
[[CFrame.fromAxisAngle.args]]
[CFrame.fromAxisAngle.args.type]
display = "Vector3"

[[CFrame.fromAxisAngle.args]]
type = "number"
[[CFrame.fromEulerAnglesXYZ.args]]
type = "number"

[[CFrame.fromEulerAnglesXYZ.args]]
type = "number"

[[CFrame.fromEulerAnglesXYZ.args]]
type = "number"
[[CFrame.fromEulerAnglesYXZ.args]]
type = "number"

[[CFrame.fromEulerAnglesYXZ.args]]
type = "number"

[[CFrame.fromEulerAnglesYXZ.args]]
type = "number"
[[CFrame.fromMatrix.args]]
[CFrame.fromMatrix.args.type]
display = "Vector3"

[[CFrame.fromMatrix.args]]
[CFrame.fromMatrix.args.type]
display = "Vector3"

[[CFrame.fromMatrix.args]]
[CFrame.fromMatrix.args.type]
display = "Vector3"

[[CFrame.fromMatrix.args]]
required = false

[CFrame.fromMatrix.args.type]
display = "Vector3"
[[CFrame.fromOrientation.args]]
type = "number"

[[CFrame.fromOrientation.args]]
type = "number"

[[CFrame.fromOrientation.args]]
type = "number"
[[CFrame.lookAt.args]]
[CFrame.lookAt.args.type]
display = "Vector3"

[[CFrame.lookAt.args]]
[CFrame.lookAt.args.type]
display = "Vector3"

[[CFrame.lookAt.args]]
required = false

[CFrame.lookAt.args.type]
display = "Vector3"
[[CFrame.new.args]]
required = false
type = "any"

[[CFrame.new.args]]
required = false
type = "any"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"

[[CFrame.new.args]]
required = false
type = "number"
[[Color3.fromHSV.args]]
type = "number"

[[Color3.fromHSV.args]]
type = "number"

[[Color3.fromHSV.args]]
type = "number"
[[Color3.fromRGB.args]]
type = "number"

[[Color3.fromRGB.args]]
type = "number"

[[Color3.fromRGB.args]]
type = "number"
[[Color3.new.args]]
required = false
type = "number"

[[Color3.new.args]]
required = false
type = "number"

[[Color3.new.args]]
required = false
type = "number"
[[Color3.toHSV.args]]
[Color3.toHSV.args.type]
display = "Color3"
[[ColorSequence.new.args]]
type = "any"

[[ColorSequence.new.args]]
required = false

[ColorSequence.new.args.type]
display = "Color3"
[[ColorSequenceKeypoint.new.args]]
type = "number"

[[ColorSequenceKeypoint.new.args]]
[ColorSequenceKeypoint.new.args.type]
display = "Color3"
[[DateTime.fromIsoDate.args]]
type = "string"
[[DateTime.fromLocalTime.args]]
required = false
type = "number"

[[DateTime.fromLocalTime.args]]
required = false
type = "number"

[[DateTime.fromLocalTime.args]]
required = false
type = "number"

[[DateTime.fromLocalTime.args]]
required = false
type = "number"

[[DateTime.fromLocalTime.args]]
required = false
type = "number"

[[DateTime.fromLocalTime.args]]
required = false
type = "number"

[[DateTime.fromLocalTime.args]]
required = false
type = "number"
[[DateTime.fromUniversalTime.args]]
required = false
type = "number"

[[DateTime.fromUniversalTime.args]]
required = false
type = "number"

[[DateTime.fromUniversalTime.args]]
required = false
type = "number"

[[DateTime.fromUniversalTime.args]]
required = false
type = "number"

[[DateTime.fromUniversalTime.args]]
required = false
type = "number"

[[DateTime.fromUniversalTime.args]]
required = false
type = "number"

[[DateTime.fromUniversalTime.args]]
required = false
type = "number"
[[DateTime.fromUnixTimestamp.args]]
type = "number"
[[DateTime.fromUnixTimestampMillis.args]]
type = "number"

[DateTime.now]
args = []

[DebuggerManager]
args = []
[[DockWidgetPluginGuiInfo.new.args]]
required = false

[DockWidgetPluginGuiInfo.new.args.type]
display = "InitialDockState"

[[DockWidgetPluginGuiInfo.new.args]]
required = false
type = "bool"

[[DockWidgetPluginGuiInfo.new.args]]
required = false
type = "bool"

[[DockWidgetPluginGuiInfo.new.args]]
required = false
type = "number"

[[DockWidgetPluginGuiInfo.new.args]]
required = false
type = "number"

[[DockWidgetPluginGuiInfo.new.args]]
required = false
type = "number"

[[DockWidgetPluginGuiInfo.new.args]]
required = false
type = "number"
[Enum.ABTestLoadingStatus.Error]
struct = "EnumItem"

[Enum.ABTestLoadingStatus.GetEnumItems]
method = true
args = []

[Enum.ABTestLoadingStatus.Initialized]
struct = "EnumItem"

[Enum.ABTestLoadingStatus.None]
struct = "EnumItem"

[Enum.ABTestLoadingStatus.Pending]
struct = "EnumItem"

[Enum.ABTestLoadingStatus.ShutOff]
struct = "EnumItem"

[Enum.ABTestLoadingStatus.TimedOut]
struct = "EnumItem"
[Enum.AccessoryType.Back]
struct = "EnumItem"

[Enum.AccessoryType.DressSkirt]
struct = "EnumItem"

[Enum.AccessoryType.Face]
struct = "EnumItem"

[Enum.AccessoryType.Front]
struct = "EnumItem"

[Enum.AccessoryType.GetEnumItems]
method = true
args = []

[Enum.AccessoryType.Hair]
struct = "EnumItem"

[Enum.AccessoryType.Hat]
struct = "EnumItem"

[Enum.AccessoryType.Jacket]
struct = "EnumItem"

[Enum.AccessoryType.LeftShoe]
struct = "EnumItem"

[Enum.AccessoryType.Neck]
struct = "EnumItem"

[Enum.AccessoryType.Pants]
struct = "EnumItem"

[Enum.AccessoryType.RightShoe]
struct = "EnumItem"

[Enum.AccessoryType.Shirt]
struct = "EnumItem"

[Enum.AccessoryType.Shorts]
struct = "EnumItem"

[Enum.AccessoryType.Shoulder]
struct = "EnumItem"

[Enum.AccessoryType.Sweater]
struct = "EnumItem"

[Enum.AccessoryType.TShirt]
struct = "EnumItem"

[Enum.AccessoryType.Unknown]
struct = "EnumItem"

[Enum.AccessoryType.Waist]
struct = "EnumItem"
[Enum.ActionType.Draw]
struct = "EnumItem"

[Enum.ActionType.GetEnumItems]
method = true
args = []

[Enum.ActionType.Lose]
struct = "EnumItem"

[Enum.ActionType.Nothing]
struct = "EnumItem"

[Enum.ActionType.Pause]
struct = "EnumItem"

[Enum.ActionType.Win]
struct = "EnumItem"
[Enum.ActuatorRelativeTo.Attachment0]
struct = "EnumItem"

[Enum.ActuatorRelativeTo.Attachment1]
struct = "EnumItem"

[Enum.ActuatorRelativeTo.GetEnumItems]
method = true
args = []

[Enum.ActuatorRelativeTo.World]
struct = "EnumItem"
[Enum.ActuatorType.GetEnumItems]
method = true
args = []

[Enum.ActuatorType.Motor]
struct = "EnumItem"

[Enum.ActuatorType.None]
struct = "EnumItem"

[Enum.ActuatorType.Servo]
struct = "EnumItem"
[Enum.AdornCullingMode.Automatic]
struct = "EnumItem"

[Enum.AdornCullingMode.GetEnumItems]
method = true
args = []

[Enum.AdornCullingMode.Never]
struct = "EnumItem"
[Enum.AlignType.GetEnumItems]
method = true
args = []

[Enum.AlignType.Parallel]
struct = "EnumItem"

[Enum.AlignType.Perpendicular]
struct = "EnumItem"
[Enum.AlphaMode.GetEnumItems]
method = true
args = []

[Enum.AlphaMode.Overlay]
struct = "EnumItem"

[Enum.AlphaMode.Transparency]
struct = "EnumItem"
[Enum.AnalyticsEconomyAction.Acquire]
struct = "EnumItem"

[Enum.AnalyticsEconomyAction.Default]
struct = "EnumItem"

[Enum.AnalyticsEconomyAction.GetEnumItems]
method = true
args = []

[Enum.AnalyticsEconomyAction.Spend]
struct = "EnumItem"
[Enum.AnalyticsLogLevel.Debug]
struct = "EnumItem"

[Enum.AnalyticsLogLevel.Error]
struct = "EnumItem"

[Enum.AnalyticsLogLevel.Fatal]
struct = "EnumItem"

[Enum.AnalyticsLogLevel.GetEnumItems]
method = true
args = []

[Enum.AnalyticsLogLevel.Information]
struct = "EnumItem"

[Enum.AnalyticsLogLevel.Trace]
struct = "EnumItem"

[Enum.AnalyticsLogLevel.Warning]
struct = "EnumItem"
[Enum.AnalyticsProgressionStatus.Abandon]
struct = "EnumItem"

[Enum.AnalyticsProgressionStatus.Begin]
struct = "EnumItem"

[Enum.AnalyticsProgressionStatus.Complete]
struct = "EnumItem"

[Enum.AnalyticsProgressionStatus.Default]
struct = "EnumItem"

[Enum.AnalyticsProgressionStatus.Fail]
struct = "EnumItem"

[Enum.AnalyticsProgressionStatus.GetEnumItems]
method = true
args = []
[Enum.AnimationPriority.Action]
struct = "EnumItem"

[Enum.AnimationPriority.Core]
struct = "EnumItem"

[Enum.AnimationPriority.GetEnumItems]
method = true
args = []

[Enum.AnimationPriority.Idle]
struct = "EnumItem"

[Enum.AnimationPriority.Movement]
struct = "EnumItem"
[Enum.AnimatorRetargetingMode.Default]
struct = "EnumItem"

[Enum.AnimatorRetargetingMode.Disabled]
struct = "EnumItem"

[Enum.AnimatorRetargetingMode.Enabled]
struct = "EnumItem"

[Enum.AnimatorRetargetingMode.GetEnumItems]
method = true
args = []
[Enum.AppShellActionType.AvatarEditorPageLoaded]
struct = "EnumItem"

[Enum.AppShellActionType.GamePageLoaded]
struct = "EnumItem"

[Enum.AppShellActionType.GetEnumItems]
method = true
args = []

[Enum.AppShellActionType.HomePageLoaded]
struct = "EnumItem"

[Enum.AppShellActionType.None]
struct = "EnumItem"

[Enum.AppShellActionType.OpenApp]
struct = "EnumItem"

[Enum.AppShellActionType.ReadConversation]
struct = "EnumItem"

[Enum.AppShellActionType.TapAvatarTab]
struct = "EnumItem"

[Enum.AppShellActionType.TapChatTab]
struct = "EnumItem"

[Enum.AppShellActionType.TapConversationEntry]
struct = "EnumItem"

[Enum.AppShellActionType.TapGamePageTab]
struct = "EnumItem"

[Enum.AppShellActionType.TapHomePageTab]
struct = "EnumItem"
[Enum.AppShellFeature.AvatarEditor]
struct = "EnumItem"

[Enum.AppShellFeature.Chat]
struct = "EnumItem"

[Enum.AppShellFeature.GamePage]
struct = "EnumItem"

[Enum.AppShellFeature.GetEnumItems]
method = true
args = []

[Enum.AppShellFeature.HomePage]
struct = "EnumItem"

[Enum.AppShellFeature.Landing]
struct = "EnumItem"

[Enum.AppShellFeature.More]
struct = "EnumItem"

[Enum.AppShellFeature.None]
struct = "EnumItem"
[Enum.AppUpdateStatus.Available]
struct = "EnumItem"

[Enum.AppUpdateStatus.Failed]
struct = "EnumItem"

[Enum.AppUpdateStatus.GetEnumItems]
method = true
args = []

[Enum.AppUpdateStatus.NotAvailable]
struct = "EnumItem"

[Enum.AppUpdateStatus.NotSupported]
struct = "EnumItem"

[Enum.AppUpdateStatus.Unknown]
struct = "EnumItem"
[Enum.ApplyStrokeMode.Border]
struct = "EnumItem"

[Enum.ApplyStrokeMode.Contextual]
struct = "EnumItem"

[Enum.ApplyStrokeMode.GetEnumItems]
method = true
args = []
[Enum.AspectType.FitWithinMaxSize]
struct = "EnumItem"

[Enum.AspectType.GetEnumItems]
method = true
args = []

[Enum.AspectType.ScaleWithParentSize]
struct = "EnumItem"
[Enum.AssetFetchStatus.Failure]
struct = "EnumItem"

[Enum.AssetFetchStatus.GetEnumItems]
method = true
args = []

[Enum.AssetFetchStatus.Success]
struct = "EnumItem"
[Enum.AssetType.Animation]
struct = "EnumItem"

[Enum.AssetType.Audio]
struct = "EnumItem"

[Enum.AssetType.BackAccessory]
struct = "EnumItem"

[Enum.AssetType.Badge]
struct = "EnumItem"

[Enum.AssetType.ClimbAnimation]
struct = "EnumItem"

[Enum.AssetType.DeathAnimation]
struct = "EnumItem"

[Enum.AssetType.Decal]
struct = "EnumItem"

[Enum.AssetType.DressSkirtAccessory]
struct = "EnumItem"

[Enum.AssetType.EarAccessory]
struct = "EnumItem"

[Enum.AssetType.EmoteAnimation]
struct = "EnumItem"

[Enum.AssetType.EyeAccessory]
struct = "EnumItem"

[Enum.AssetType.Face]
struct = "EnumItem"

[Enum.AssetType.FaceAccessory]
struct = "EnumItem"

[Enum.AssetType.FallAnimation]
struct = "EnumItem"

[Enum.AssetType.FrontAccessory]
struct = "EnumItem"

[Enum.AssetType.GamePass]
struct = "EnumItem"

[Enum.AssetType.Gear]
struct = "EnumItem"

[Enum.AssetType.GetEnumItems]
method = true
args = []

[Enum.AssetType.HairAccessory]
struct = "EnumItem"

[Enum.AssetType.Hat]
struct = "EnumItem"

[Enum.AssetType.Head]
struct = "EnumItem"

[Enum.AssetType.IdleAnimation]
struct = "EnumItem"

[Enum.AssetType.Image]
struct = "EnumItem"

[Enum.AssetType.JacketAccessory]
struct = "EnumItem"

[Enum.AssetType.JumpAnimation]
struct = "EnumItem"

[Enum.AssetType.LeftArm]
struct = "EnumItem"

[Enum.AssetType.LeftLeg]
struct = "EnumItem"

[Enum.AssetType.LeftShoeAccessory]
struct = "EnumItem"

[Enum.AssetType.Lua]
struct = "EnumItem"

[Enum.AssetType.Mesh]
struct = "EnumItem"

[Enum.AssetType.MeshPart]
struct = "EnumItem"

[Enum.AssetType.Model]
struct = "EnumItem"

[Enum.AssetType.NeckAccessory]
struct = "EnumItem"

[Enum.AssetType.Package]
struct = "EnumItem"

[Enum.AssetType.Pants]
struct = "EnumItem"

[Enum.AssetType.PantsAccessory]
struct = "EnumItem"

[Enum.AssetType.Place]
struct = "EnumItem"

[Enum.AssetType.Plugin]
struct = "EnumItem"

[Enum.AssetType.PoseAnimation]
struct = "EnumItem"

[Enum.AssetType.RightArm]
struct = "EnumItem"

[Enum.AssetType.RightLeg]
struct = "EnumItem"

[Enum.AssetType.RightShoeAccessory]
struct = "EnumItem"

[Enum.AssetType.RunAnimation]
struct = "EnumItem"

[Enum.AssetType.Shirt]
struct = "EnumItem"

[Enum.AssetType.ShirtAccessory]
struct = "EnumItem"

[Enum.AssetType.ShortsAccessory]
struct = "EnumItem"

[Enum.AssetType.ShoulderAccessory]
struct = "EnumItem"

[Enum.AssetType.SweaterAccessory]
struct = "EnumItem"

[Enum.AssetType.SwimAnimation]
struct = "EnumItem"

[Enum.AssetType.TShirt]
struct = "EnumItem"

[Enum.AssetType.TShirtAccessory]
struct = "EnumItem"

[Enum.AssetType.Torso]
struct = "EnumItem"

[Enum.AssetType.Video]
struct = "EnumItem"

[Enum.AssetType.WaistAccessory]
struct = "EnumItem"

[Enum.AssetType.WalkAnimation]
struct = "EnumItem"
[Enum.AssetTypeVerification.Always]
struct = "EnumItem"

[Enum.AssetTypeVerification.ClientOnly]
struct = "EnumItem"

[Enum.AssetTypeVerification.Default]
struct = "EnumItem"

[Enum.AssetTypeVerification.GetEnumItems]
method = true
args = []
[Enum.AutoIndentRule.Absolute]
struct = "EnumItem"

[Enum.AutoIndentRule.GetEnumItems]
method = true
args = []

[Enum.AutoIndentRule.Off]
struct = "EnumItem"

[Enum.AutoIndentRule.Relative]
struct = "EnumItem"
[Enum.AutomaticSize.GetEnumItems]
method = true
args = []

[Enum.AutomaticSize.None]
struct = "EnumItem"

[Enum.AutomaticSize.X]
struct = "EnumItem"

[Enum.AutomaticSize.XY]
struct = "EnumItem"

[Enum.AutomaticSize.Y]
struct = "EnumItem"
[Enum.AvatarAssetType.BackAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.ClimbAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.DressSkirtAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.EmoteAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.Face]
struct = "EnumItem"

[Enum.AvatarAssetType.FaceAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.FallAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.FrontAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.Gear]
struct = "EnumItem"

[Enum.AvatarAssetType.GetEnumItems]
method = true
args = []

[Enum.AvatarAssetType.HairAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.Hat]
struct = "EnumItem"

[Enum.AvatarAssetType.Head]
struct = "EnumItem"

[Enum.AvatarAssetType.IdleAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.JacketAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.JumpAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.LeftArm]
struct = "EnumItem"

[Enum.AvatarAssetType.LeftLeg]
struct = "EnumItem"

[Enum.AvatarAssetType.LeftShoeAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.NeckAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.Pants]
struct = "EnumItem"

[Enum.AvatarAssetType.PantsAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.RightArm]
struct = "EnumItem"

[Enum.AvatarAssetType.RightLeg]
struct = "EnumItem"

[Enum.AvatarAssetType.RightShoeAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.RunAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.Shirt]
struct = "EnumItem"

[Enum.AvatarAssetType.ShirtAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.ShortsAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.ShoulderAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.SweaterAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.SwimAnimation]
struct = "EnumItem"

[Enum.AvatarAssetType.TShirt]
struct = "EnumItem"

[Enum.AvatarAssetType.TShirtAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.Torso]
struct = "EnumItem"

[Enum.AvatarAssetType.WaistAccessory]
struct = "EnumItem"

[Enum.AvatarAssetType.WalkAnimation]
struct = "EnumItem"
[Enum.AvatarContextMenuOption.Chat]
struct = "EnumItem"

[Enum.AvatarContextMenuOption.Emote]
struct = "EnumItem"

[Enum.AvatarContextMenuOption.Friend]
struct = "EnumItem"

[Enum.AvatarContextMenuOption.GetEnumItems]
method = true
args = []

[Enum.AvatarContextMenuOption.InspectMenu]
struct = "EnumItem"
[Enum.AvatarItemType.Asset]
struct = "EnumItem"

[Enum.AvatarItemType.Bundle]
struct = "EnumItem"

[Enum.AvatarItemType.GetEnumItems]
method = true
args = []
[Enum.AvatarPromptResult.Failed]
struct = "EnumItem"

[Enum.AvatarPromptResult.GetEnumItems]
method = true
args = []

[Enum.AvatarPromptResult.PermissionDenied]
struct = "EnumItem"

[Enum.AvatarPromptResult.Success]
struct = "EnumItem"
[Enum.Axis.GetEnumItems]
method = true
args = []

[Enum.Axis.X]
struct = "EnumItem"

[Enum.Axis.Y]
struct = "EnumItem"

[Enum.Axis.Z]
struct = "EnumItem"
[Enum.BinType.Clone]
struct = "EnumItem"

[Enum.BinType.GameTool]
struct = "EnumItem"

[Enum.BinType.GetEnumItems]
method = true
args = []

[Enum.BinType.Grab]
struct = "EnumItem"

[Enum.BinType.Hammer]
struct = "EnumItem"

[Enum.BinType.Script]
struct = "EnumItem"
[Enum.BodyPart.GetEnumItems]
method = true
args = []

[Enum.BodyPart.Head]
struct = "EnumItem"

[Enum.BodyPart.LeftArm]
struct = "EnumItem"

[Enum.BodyPart.LeftLeg]
struct = "EnumItem"

[Enum.BodyPart.RightArm]
struct = "EnumItem"

[Enum.BodyPart.RightLeg]
struct = "EnumItem"

[Enum.BodyPart.Torso]
struct = "EnumItem"
[Enum.BodyPartR15.GetEnumItems]
method = true
args = []

[Enum.BodyPartR15.Head]
struct = "EnumItem"

[Enum.BodyPartR15.LeftFoot]
struct = "EnumItem"

[Enum.BodyPartR15.LeftHand]
struct = "EnumItem"

[Enum.BodyPartR15.LeftLowerArm]
struct = "EnumItem"

[Enum.BodyPartR15.LeftLowerLeg]
struct = "EnumItem"

[Enum.BodyPartR15.LeftUpperArm]
struct = "EnumItem"

[Enum.BodyPartR15.LeftUpperLeg]
struct = "EnumItem"

[Enum.BodyPartR15.LowerTorso]
struct = "EnumItem"

[Enum.BodyPartR15.RightFoot]
struct = "EnumItem"

[Enum.BodyPartR15.RightHand]
struct = "EnumItem"

[Enum.BodyPartR15.RightLowerArm]
struct = "EnumItem"

[Enum.BodyPartR15.RightLowerLeg]
struct = "EnumItem"

[Enum.BodyPartR15.RightUpperArm]
struct = "EnumItem"

[Enum.BodyPartR15.RightUpperLeg]
struct = "EnumItem"

[Enum.BodyPartR15.RootPart]
struct = "EnumItem"

[Enum.BodyPartR15.Unknown]
struct = "EnumItem"

[Enum.BodyPartR15.UpperTorso]
struct = "EnumItem"
[Enum.BorderMode.GetEnumItems]
method = true
args = []

[Enum.BorderMode.Inset]
struct = "EnumItem"

[Enum.BorderMode.Middle]
struct = "EnumItem"

[Enum.BorderMode.Outline]
struct = "EnumItem"
[Enum.BreakReason.Error]
struct = "EnumItem"

[Enum.BreakReason.GetEnumItems]
method = true
args = []

[Enum.BreakReason.Other]
struct = "EnumItem"

[Enum.BreakReason.SpecialBreakpoint]
struct = "EnumItem"

[Enum.BreakReason.UserBreakpoint]
struct = "EnumItem"
[Enum.BreakpointRemoveReason.GetEnumItems]
method = true
args = []

[Enum.BreakpointRemoveReason.Requested]
struct = "EnumItem"

[Enum.BreakpointRemoveReason.ScriptChanged]
struct = "EnumItem"

[Enum.BreakpointRemoveReason.ScriptRemoved]
struct = "EnumItem"
[Enum.BulkMoveMode.FireAllEvents]
struct = "EnumItem"

[Enum.BulkMoveMode.FireCFrameChanged]
struct = "EnumItem"

[Enum.BulkMoveMode.GetEnumItems]
method = true
args = []
[Enum.BundleType.Animations]
struct = "EnumItem"

[Enum.BundleType.BodyParts]
struct = "EnumItem"

[Enum.BundleType.GetEnumItems]
method = true
args = []
[Enum.Button.Dismount]
struct = "EnumItem"

[Enum.Button.GetEnumItems]
method = true
args = []

[Enum.Button.Jump]
struct = "EnumItem"
[Enum.ButtonStyle.Custom]
struct = "EnumItem"

[Enum.ButtonStyle.GetEnumItems]
method = true
args = []

[Enum.ButtonStyle.RobloxButton]
struct = "EnumItem"

[Enum.ButtonStyle.RobloxButtonDefault]
struct = "EnumItem"

[Enum.ButtonStyle.RobloxRoundButton]
struct = "EnumItem"

[Enum.ButtonStyle.RobloxRoundDefaultButton]
struct = "EnumItem"

[Enum.ButtonStyle.RobloxRoundDropdownButton]
struct = "EnumItem"
[Enum.CageType.GetEnumItems]
method = true
args = []

[Enum.CageType.Inner]
struct = "EnumItem"

[Enum.CageType.Outer]
struct = "EnumItem"
[Enum.CameraMode.Classic]
struct = "EnumItem"

[Enum.CameraMode.GetEnumItems]
method = true
args = []

[Enum.CameraMode.LockFirstPerson]
struct = "EnumItem"
[Enum.CameraPanMode.Classic]
struct = "EnumItem"

[Enum.CameraPanMode.EdgeBump]
struct = "EnumItem"

[Enum.CameraPanMode.GetEnumItems]
method = true
args = []
[Enum.CameraType.Attach]
struct = "EnumItem"

[Enum.CameraType.Custom]
struct = "EnumItem"

[Enum.CameraType.Fixed]
struct = "EnumItem"

[Enum.CameraType.Follow]
struct = "EnumItem"

[Enum.CameraType.GetEnumItems]
method = true
args = []

[Enum.CameraType.Orbital]
struct = "EnumItem"

[Enum.CameraType.Scriptable]
struct = "EnumItem"

[Enum.CameraType.Track]
struct = "EnumItem"

[Enum.CameraType.Watch]
struct = "EnumItem"
[Enum.CatalogCategoryFilter.Collectibles]
struct = "EnumItem"

[Enum.CatalogCategoryFilter.CommunityCreations]
struct = "EnumItem"

[Enum.CatalogCategoryFilter.Featured]
struct = "EnumItem"

[Enum.CatalogCategoryFilter.GetEnumItems]
method = true
args = []

[Enum.CatalogCategoryFilter.None]
struct = "EnumItem"

[Enum.CatalogCategoryFilter.Premium]
struct = "EnumItem"

[Enum.CatalogCategoryFilter.Recommended]
struct = "EnumItem"
[Enum.CatalogSortType.GetEnumItems]
method = true
args = []

[Enum.CatalogSortType.MostFavorited]
struct = "EnumItem"

[Enum.CatalogSortType.PriceHighToLow]
struct = "EnumItem"

[Enum.CatalogSortType.PriceLowToHigh]
struct = "EnumItem"

[Enum.CatalogSortType.RecentlyUpdated]
struct = "EnumItem"

[Enum.CatalogSortType.Relevance]
struct = "EnumItem"
[Enum.CellBlock.CornerWedge]
struct = "EnumItem"

[Enum.CellBlock.GetEnumItems]
method = true
args = []

[Enum.CellBlock.HorizontalWedge]
struct = "EnumItem"

[Enum.CellBlock.InverseCornerWedge]
struct = "EnumItem"

[Enum.CellBlock.Solid]
struct = "EnumItem"

[Enum.CellBlock.VerticalWedge]
struct = "EnumItem"
[Enum.CellMaterial.Aluminum]
struct = "EnumItem"

[Enum.CellMaterial.Asphalt]
struct = "EnumItem"

[Enum.CellMaterial.BluePlastic]
struct = "EnumItem"

[Enum.CellMaterial.Brick]
struct = "EnumItem"

[Enum.CellMaterial.Cement]
struct = "EnumItem"

[Enum.CellMaterial.CinderBlock]
struct = "EnumItem"

[Enum.CellMaterial.Empty]
struct = "EnumItem"

[Enum.CellMaterial.GetEnumItems]
method = true
args = []

[Enum.CellMaterial.Gold]
struct = "EnumItem"

[Enum.CellMaterial.Granite]
struct = "EnumItem"

[Enum.CellMaterial.Grass]
struct = "EnumItem"

[Enum.CellMaterial.Gravel]
struct = "EnumItem"

[Enum.CellMaterial.Iron]
struct = "EnumItem"

[Enum.CellMaterial.MossyStone]
struct = "EnumItem"

[Enum.CellMaterial.RedPlastic]
struct = "EnumItem"

[Enum.CellMaterial.Sand]
struct = "EnumItem"

[Enum.CellMaterial.Water]
struct = "EnumItem"

[Enum.CellMaterial.WoodLog]
struct = "EnumItem"

[Enum.CellMaterial.WoodPlank]
struct = "EnumItem"
[Enum.CellOrientation.GetEnumItems]
method = true
args = []

[Enum.CellOrientation.NegX]
struct = "EnumItem"

[Enum.CellOrientation.NegZ]
struct = "EnumItem"

[Enum.CellOrientation.X]
struct = "EnumItem"

[Enum.CellOrientation.Z]
struct = "EnumItem"
[Enum.CenterDialogType.GetEnumItems]
method = true
args = []

[Enum.CenterDialogType.ModalDialog]
struct = "EnumItem"

[Enum.CenterDialogType.PlayerInitiatedDialog]
struct = "EnumItem"

[Enum.CenterDialogType.QuitDialog]
struct = "EnumItem"

[Enum.CenterDialogType.UnsolicitedDialog]
struct = "EnumItem"
[Enum.ChatCallbackType.GetEnumItems]
method = true
args = []

[Enum.ChatCallbackType.OnClientFormattingMessage]
struct = "EnumItem"

[Enum.ChatCallbackType.OnClientSendingMessage]
struct = "EnumItem"

[Enum.ChatCallbackType.OnCreatingChatWindow]
struct = "EnumItem"

[Enum.ChatCallbackType.OnServerReceivingMessage]
struct = "EnumItem"
[Enum.ChatColor.Blue]
struct = "EnumItem"

[Enum.ChatColor.GetEnumItems]
method = true
args = []

[Enum.ChatColor.Green]
struct = "EnumItem"

[Enum.ChatColor.Red]
struct = "EnumItem"

[Enum.ChatColor.White]
struct = "EnumItem"
[Enum.ChatMode.GetEnumItems]
method = true
args = []

[Enum.ChatMode.Menu]
struct = "EnumItem"

[Enum.ChatMode.TextAndMenu]
struct = "EnumItem"
[Enum.ChatPrivacyMode.AllUsers]
struct = "EnumItem"

[Enum.ChatPrivacyMode.Friends]
struct = "EnumItem"

[Enum.ChatPrivacyMode.GetEnumItems]
method = true
args = []

[Enum.ChatPrivacyMode.NoOne]
struct = "EnumItem"
[Enum.ChatStyle.Bubble]
struct = "EnumItem"

[Enum.ChatStyle.Classic]
struct = "EnumItem"

[Enum.ChatStyle.ClassicAndBubble]
struct = "EnumItem"

[Enum.ChatStyle.GetEnumItems]
method = true
args = []
[Enum.ClientAnimatorThrottlingMode.Default]
struct = "EnumItem"

[Enum.ClientAnimatorThrottlingMode.Disabled]
struct = "EnumItem"

[Enum.ClientAnimatorThrottlingMode.Enabled]
struct = "EnumItem"

[Enum.ClientAnimatorThrottlingMode.GetEnumItems]
method = true
args = []
[Enum.CollisionFidelity.Box]
struct = "EnumItem"

[Enum.CollisionFidelity.Default]
struct = "EnumItem"

[Enum.CollisionFidelity.GetEnumItems]
method = true
args = []

[Enum.CollisionFidelity.Hull]
struct = "EnumItem"

[Enum.CollisionFidelity.PreciseConvexDecomposition]
struct = "EnumItem"
[Enum.CommandPermission.GetEnumItems]
method = true
args = []

[Enum.CommandPermission.LocalUser]
struct = "EnumItem"

[Enum.CommandPermission.Plugin]
struct = "EnumItem"
[Enum.ComputerCameraMovementMode.CameraToggle]
struct = "EnumItem"

[Enum.ComputerCameraMovementMode.Classic]
struct = "EnumItem"

[Enum.ComputerCameraMovementMode.Default]
struct = "EnumItem"

[Enum.ComputerCameraMovementMode.Follow]
struct = "EnumItem"

[Enum.ComputerCameraMovementMode.GetEnumItems]
method = true
args = []

[Enum.ComputerCameraMovementMode.Orbital]
struct = "EnumItem"
[Enum.ComputerMovementMode.ClickToMove]
struct = "EnumItem"

[Enum.ComputerMovementMode.Default]
struct = "EnumItem"

[Enum.ComputerMovementMode.GetEnumItems]
method = true
args = []

[Enum.ComputerMovementMode.KeyboardMouse]
struct = "EnumItem"
[Enum.ConnectionError.DisconnectBadhash]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectBlockedIP]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectBySecurityPolicy]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectClientFailure]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectCloudEditKick]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectConnectionLost]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectDevMaintenance]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectDuplicatePlayer]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectDuplicateTicket]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectErrors]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectEvicted]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectHashTimeout]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectIdle]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectIllegalTeleport]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectLuaKick]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectNewSecurityKeyMismatch]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectOnRemoteSysStats]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectPlayerless]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectProtocolMismatch]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectRaknetErrors]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectReceivePacketError]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectReceivePacketStreamError]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectRejoin]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectRobloxMaintenance]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectSecurityKeyMismatch]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectSendPacketError]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectTimeout]
struct = "EnumItem"

[Enum.ConnectionError.DisconnectWrongVersion]
struct = "EnumItem"

[Enum.ConnectionError.GetEnumItems]
method = true
args = []

[Enum.ConnectionError.OK]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchCustomMessage]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchDisabled]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchError]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchErrors]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchFlooded]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchGameEnded]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchGameFull]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchHashException]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchHashExpired]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchHttpError]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchOtherError]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchPartyCannotFit]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchRestricted]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchUnauthorized]
struct = "EnumItem"

[Enum.ConnectionError.PlacelaunchUserLeft]
struct = "EnumItem"

[Enum.ConnectionError.TeleportErrors]
struct = "EnumItem"

[Enum.ConnectionError.TeleportFailure]
struct = "EnumItem"

[Enum.ConnectionError.TeleportFlooded]
struct = "EnumItem"

[Enum.ConnectionError.TeleportGameEnded]
struct = "EnumItem"

[Enum.ConnectionError.TeleportGameFull]
struct = "EnumItem"

[Enum.ConnectionError.TeleportGameNotFound]
struct = "EnumItem"

[Enum.ConnectionError.TeleportIsTeleporting]
struct = "EnumItem"

[Enum.ConnectionError.TeleportUnauthorized]
struct = "EnumItem"
[Enum.ConnectionState.Connected]
struct = "EnumItem"

[Enum.ConnectionState.Disconnected]
struct = "EnumItem"

[Enum.ConnectionState.GetEnumItems]
method = true
args = []
[Enum.ContextActionPriority.Default]
struct = "EnumItem"

[Enum.ContextActionPriority.GetEnumItems]
method = true
args = []

[Enum.ContextActionPriority.High]
struct = "EnumItem"

[Enum.ContextActionPriority.Low]
struct = "EnumItem"

[Enum.ContextActionPriority.Medium]
struct = "EnumItem"
[Enum.ContextActionResult.GetEnumItems]
method = true
args = []

[Enum.ContextActionResult.Pass]
struct = "EnumItem"

[Enum.ContextActionResult.Sink]
struct = "EnumItem"
[Enum.ControlMode.Classic]
struct = "EnumItem"

[Enum.ControlMode.GetEnumItems]
method = true
args = []

[Enum.ControlMode.MouseLockSwitch]
struct = "EnumItem"
[Enum.CoreGuiType.All]
struct = "EnumItem"

[Enum.CoreGuiType.Backpack]
struct = "EnumItem"

[Enum.CoreGuiType.Chat]
struct = "EnumItem"

[Enum.CoreGuiType.EmotesMenu]
struct = "EnumItem"

[Enum.CoreGuiType.GetEnumItems]
method = true
args = []

[Enum.CoreGuiType.Health]
struct = "EnumItem"

[Enum.CoreGuiType.PlayerList]
struct = "EnumItem"
[Enum.CreateOutfitFailure.GetEnumItems]
method = true
args = []

[Enum.CreateOutfitFailure.InvalidName]
struct = "EnumItem"

[Enum.CreateOutfitFailure.Other]
struct = "EnumItem"

[Enum.CreateOutfitFailure.OutfitLimitReached]
struct = "EnumItem"
[Enum.CreatorType.GetEnumItems]
method = true
args = []

[Enum.CreatorType.Group]
struct = "EnumItem"

[Enum.CreatorType.User]
struct = "EnumItem"
[Enum.CurrencyType.Default]
struct = "EnumItem"

[Enum.CurrencyType.GetEnumItems]
method = true
args = []

[Enum.CurrencyType.Robux]
struct = "EnumItem"

[Enum.CurrencyType.Tix]
struct = "EnumItem"
[Enum.CustomCameraMode.Classic]
struct = "EnumItem"

[Enum.CustomCameraMode.Default]
struct = "EnumItem"

[Enum.CustomCameraMode.Follow]
struct = "EnumItem"

[Enum.CustomCameraMode.GetEnumItems]
method = true
args = []
[Enum.DataStoreRequestType.GetAsync]
struct = "EnumItem"

[Enum.DataStoreRequestType.GetEnumItems]
method = true
args = []

[Enum.DataStoreRequestType.GetSortedAsync]
struct = "EnumItem"

[Enum.DataStoreRequestType.OnUpdate]
struct = "EnumItem"

[Enum.DataStoreRequestType.SetIncrementAsync]
struct = "EnumItem"

[Enum.DataStoreRequestType.SetIncrementSortedAsync]
struct = "EnumItem"

[Enum.DataStoreRequestType.UpdateAsync]
struct = "EnumItem"
[Enum.DebuggerEndReason.ClientRequest]
struct = "EnumItem"

[Enum.DebuggerEndReason.ConfigurationFailed]
struct = "EnumItem"

[Enum.DebuggerEndReason.Disconnected]
struct = "EnumItem"

[Enum.DebuggerEndReason.GetEnumItems]
method = true
args = []

[Enum.DebuggerEndReason.InvalidHost]
struct = "EnumItem"

[Enum.DebuggerEndReason.RpcError]
struct = "EnumItem"

[Enum.DebuggerEndReason.ServerProtocolMismatch]
struct = "EnumItem"

[Enum.DebuggerEndReason.ServerShutdown]
struct = "EnumItem"

[Enum.DebuggerEndReason.Timeout]
struct = "EnumItem"
[Enum.DebuggerFrameType.C]
struct = "EnumItem"

[Enum.DebuggerFrameType.GetEnumItems]
method = true
args = []

[Enum.DebuggerFrameType.Lua]
struct = "EnumItem"
[Enum.DebuggerPauseReason.Breakpoint]
struct = "EnumItem"

[Enum.DebuggerPauseReason.Entrypoint]
struct = "EnumItem"

[Enum.DebuggerPauseReason.Exception]
struct = "EnumItem"

[Enum.DebuggerPauseReason.GetEnumItems]
method = true
args = []

[Enum.DebuggerPauseReason.Requested]
struct = "EnumItem"

[Enum.DebuggerPauseReason.SingleStep]
struct = "EnumItem"

[Enum.DebuggerPauseReason.Unknown]
struct = "EnumItem"
[Enum.DebuggerStatus.ConnectionLost]
struct = "EnumItem"

[Enum.DebuggerStatus.GetEnumItems]
method = true
args = []

[Enum.DebuggerStatus.InternalError]
struct = "EnumItem"

[Enum.DebuggerStatus.InvalidArgument]
struct = "EnumItem"

[Enum.DebuggerStatus.InvalidResponse]
struct = "EnumItem"

[Enum.DebuggerStatus.InvalidState]
struct = "EnumItem"

[Enum.DebuggerStatus.RpcError]
struct = "EnumItem"

[Enum.DebuggerStatus.Success]
struct = "EnumItem"

[Enum.DebuggerStatus.Timeout]
struct = "EnumItem"
[Enum.DevCameraOcclusionMode.GetEnumItems]
method = true
args = []

[Enum.DevCameraOcclusionMode.Invisicam]
struct = "EnumItem"

[Enum.DevCameraOcclusionMode.Zoom]
struct = "EnumItem"
[Enum.DevComputerCameraMovementMode.CameraToggle]
struct = "EnumItem"

[Enum.DevComputerCameraMovementMode.Classic]
struct = "EnumItem"

[Enum.DevComputerCameraMovementMode.Follow]
struct = "EnumItem"

[Enum.DevComputerCameraMovementMode.GetEnumItems]
method = true
args = []

[Enum.DevComputerCameraMovementMode.Orbital]
struct = "EnumItem"

[Enum.DevComputerCameraMovementMode.UserChoice]
struct = "EnumItem"
[Enum.DevComputerMovementMode.ClickToMove]
struct = "EnumItem"

[Enum.DevComputerMovementMode.GetEnumItems]
method = true
args = []

[Enum.DevComputerMovementMode.KeyboardMouse]
struct = "EnumItem"

[Enum.DevComputerMovementMode.Scriptable]
struct = "EnumItem"

[Enum.DevComputerMovementMode.UserChoice]
struct = "EnumItem"
[Enum.DevTouchCameraMovementMode.Classic]
struct = "EnumItem"

[Enum.DevTouchCameraMovementMode.Follow]
struct = "EnumItem"

[Enum.DevTouchCameraMovementMode.GetEnumItems]
method = true
args = []

[Enum.DevTouchCameraMovementMode.Orbital]
struct = "EnumItem"

[Enum.DevTouchCameraMovementMode.UserChoice]
struct = "EnumItem"
[Enum.DevTouchMovementMode.ClickToMove]
struct = "EnumItem"

[Enum.DevTouchMovementMode.DPad]
struct = "EnumItem"

[Enum.DevTouchMovementMode.DynamicThumbstick]
struct = "EnumItem"

[Enum.DevTouchMovementMode.GetEnumItems]
method = true
args = []

[Enum.DevTouchMovementMode.Scriptable]
struct = "EnumItem"

[Enum.DevTouchMovementMode.Thumbpad]
struct = "EnumItem"

[Enum.DevTouchMovementMode.Thumbstick]
struct = "EnumItem"

[Enum.DevTouchMovementMode.UserChoice]
struct = "EnumItem"
[Enum.DeveloperMemoryTag.Animation]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GetEnumItems]
method = true
args = []

[Enum.DeveloperMemoryTag.GraphicsMeshParts]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsParticles]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsParts]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsSolidModels]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsSpatialHash]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsTerrain]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsTexture]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.GraphicsTextureCharacter]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Gui]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.HttpCache]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Instances]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Internal]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.LuaHeap]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Navigation]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.PhysicsCollision]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.PhysicsParts]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Script]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Signals]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.Sounds]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.StreamingSounds]
struct = "EnumItem"

[Enum.DeveloperMemoryTag.TerrainVoxels]
struct = "EnumItem"
[Enum.DeviceType.Desktop]
struct = "EnumItem"

[Enum.DeviceType.GetEnumItems]
method = true
args = []

[Enum.DeviceType.Phone]
struct = "EnumItem"

[Enum.DeviceType.Tablet]
struct = "EnumItem"

[Enum.DeviceType.Unknown]
struct = "EnumItem"
[Enum.DialogBehaviorType.GetEnumItems]
method = true
args = []

[Enum.DialogBehaviorType.MultiplePlayers]
struct = "EnumItem"

[Enum.DialogBehaviorType.SinglePlayer]
struct = "EnumItem"
[Enum.DialogPurpose.GetEnumItems]
method = true
args = []

[Enum.DialogPurpose.Help]
struct = "EnumItem"

[Enum.DialogPurpose.Quest]
struct = "EnumItem"

[Enum.DialogPurpose.Shop]
struct = "EnumItem"
[Enum.DialogTone.Enemy]
struct = "EnumItem"

[Enum.DialogTone.Friendly]
struct = "EnumItem"

[Enum.DialogTone.GetEnumItems]
method = true
args = []

[Enum.DialogTone.Neutral]
struct = "EnumItem"
[Enum.DominantAxis.GetEnumItems]
method = true
args = []

[Enum.DominantAxis.Height]
struct = "EnumItem"

[Enum.DominantAxis.Width]
struct = "EnumItem"
[Enum.DraftStatusCode.DraftCommitted]
struct = "EnumItem"

[Enum.DraftStatusCode.DraftOutdated]
struct = "EnumItem"

[Enum.DraftStatusCode.GetEnumItems]
method = true
args = []

[Enum.DraftStatusCode.OK]
struct = "EnumItem"

[Enum.DraftStatusCode.ScriptRemoved]
struct = "EnumItem"
[Enum.DraggerCoordinateSpace.GetEnumItems]
method = true
args = []

[Enum.DraggerCoordinateSpace.Object]
struct = "EnumItem"

[Enum.DraggerCoordinateSpace.World]
struct = "EnumItem"
[Enum.DraggerMovementMode.Geometric]
struct = "EnumItem"

[Enum.DraggerMovementMode.GetEnumItems]
method = true
args = []

[Enum.DraggerMovementMode.Physical]
struct = "EnumItem"
[Enum.EasingDirection.GetEnumItems]
method = true
args = []

[Enum.EasingDirection.In]
struct = "EnumItem"

[Enum.EasingDirection.InOut]
struct = "EnumItem"

[Enum.EasingDirection.Out]
struct = "EnumItem"
[Enum.EasingStyle.Back]
struct = "EnumItem"

[Enum.EasingStyle.Bounce]
struct = "EnumItem"

[Enum.EasingStyle.Circular]
struct = "EnumItem"

[Enum.EasingStyle.Cubic]
struct = "EnumItem"

[Enum.EasingStyle.Elastic]
struct = "EnumItem"

[Enum.EasingStyle.Exponential]
struct = "EnumItem"

[Enum.EasingStyle.GetEnumItems]
method = true
args = []

[Enum.EasingStyle.Linear]
struct = "EnumItem"

[Enum.EasingStyle.Quad]
struct = "EnumItem"

[Enum.EasingStyle.Quart]
struct = "EnumItem"

[Enum.EasingStyle.Quint]
struct = "EnumItem"

[Enum.EasingStyle.Sine]
struct = "EnumItem"
[Enum.ElasticBehavior.Always]
struct = "EnumItem"

[Enum.ElasticBehavior.GetEnumItems]
method = true
args = []

[Enum.ElasticBehavior.Never]
struct = "EnumItem"

[Enum.ElasticBehavior.WhenScrollable]
struct = "EnumItem"
[Enum.EnviromentalPhysicsThrottle.Always]
struct = "EnumItem"

[Enum.EnviromentalPhysicsThrottle.DefaultAuto]
struct = "EnumItem"

[Enum.EnviromentalPhysicsThrottle.Disabled]
struct = "EnumItem"

[Enum.EnviromentalPhysicsThrottle.GetEnumItems]
method = true
args = []

[Enum.EnviromentalPhysicsThrottle.Skip16]
struct = "EnumItem"

[Enum.EnviromentalPhysicsThrottle.Skip2]
struct = "EnumItem"

[Enum.EnviromentalPhysicsThrottle.Skip4]
struct = "EnumItem"

[Enum.EnviromentalPhysicsThrottle.Skip8]
struct = "EnumItem"
[Enum.ExplosionType.Craters]
struct = "EnumItem"

[Enum.ExplosionType.GetEnumItems]
method = true
args = []

[Enum.ExplosionType.NoCraters]
struct = "EnumItem"
[Enum.FieldOfViewMode.Diagonal]
struct = "EnumItem"

[Enum.FieldOfViewMode.GetEnumItems]
method = true
args = []

[Enum.FieldOfViewMode.MaxAxis]
struct = "EnumItem"

[Enum.FieldOfViewMode.Vertical]
struct = "EnumItem"
[Enum.FillDirection.GetEnumItems]
method = true
args = []

[Enum.FillDirection.Horizontal]
struct = "EnumItem"

[Enum.FillDirection.Vertical]
struct = "EnumItem"
[Enum.FilterResult.Accepted]
struct = "EnumItem"

[Enum.FilterResult.GetEnumItems]
method = true
args = []

[Enum.FilterResult.Rejected]
struct = "EnumItem"
[Enum.Font.AmaticSC]
struct = "EnumItem"

[Enum.Font.Antique]
struct = "EnumItem"

[Enum.Font.Arcade]
struct = "EnumItem"

[Enum.Font.Arial]
struct = "EnumItem"

[Enum.Font.ArialBold]
struct = "EnumItem"

[Enum.Font.Bangers]
struct = "EnumItem"

[Enum.Font.Bodoni]
struct = "EnumItem"

[Enum.Font.Cartoon]
struct = "EnumItem"

[Enum.Font.Code]
struct = "EnumItem"

[Enum.Font.Creepster]
struct = "EnumItem"

[Enum.Font.DenkOne]
struct = "EnumItem"

[Enum.Font.Fantasy]
struct = "EnumItem"

[Enum.Font.Fondamento]
struct = "EnumItem"

[Enum.Font.FredokaOne]
struct = "EnumItem"

[Enum.Font.Garamond]
struct = "EnumItem"

[Enum.Font.GetEnumItems]
method = true
args = []

[Enum.Font.Gotham]
struct = "EnumItem"

[Enum.Font.GothamBlack]
struct = "EnumItem"

[Enum.Font.GothamBold]
struct = "EnumItem"

[Enum.Font.GothamSemibold]
struct = "EnumItem"

[Enum.Font.GrenzeGotisch]
struct = "EnumItem"

[Enum.Font.Highway]
struct = "EnumItem"

[Enum.Font.IndieFlower]
struct = "EnumItem"

[Enum.Font.JosefinSans]
struct = "EnumItem"

[Enum.Font.Jura]
struct = "EnumItem"

[Enum.Font.Kalam]
struct = "EnumItem"

[Enum.Font.Legacy]
struct = "EnumItem"

[Enum.Font.LuckiestGuy]
struct = "EnumItem"

[Enum.Font.Merriweather]
struct = "EnumItem"

[Enum.Font.Michroma]
struct = "EnumItem"

[Enum.Font.Nunito]
struct = "EnumItem"

[Enum.Font.Oswald]
struct = "EnumItem"

[Enum.Font.PatrickHand]
struct = "EnumItem"

[Enum.Font.PermanentMarker]
struct = "EnumItem"

[Enum.Font.Roboto]
struct = "EnumItem"

[Enum.Font.RobotoCondensed]
struct = "EnumItem"

[Enum.Font.RobotoMono]
struct = "EnumItem"

[Enum.Font.Sarpanch]
struct = "EnumItem"

[Enum.Font.SciFi]
struct = "EnumItem"

[Enum.Font.SourceSans]
struct = "EnumItem"

[Enum.Font.SourceSansBold]
struct = "EnumItem"

[Enum.Font.SourceSansItalic]
struct = "EnumItem"

[Enum.Font.SourceSansLight]
struct = "EnumItem"

[Enum.Font.SourceSansSemibold]
struct = "EnumItem"

[Enum.Font.SpecialElite]
struct = "EnumItem"

[Enum.Font.TitilliumWeb]
struct = "EnumItem"

[Enum.Font.Ubuntu]
struct = "EnumItem"
[Enum.FontSize.GetEnumItems]
method = true
args = []

[Enum.FontSize.Size10]
struct = "EnumItem"

[Enum.FontSize.Size11]
struct = "EnumItem"

[Enum.FontSize.Size12]
struct = "EnumItem"

[Enum.FontSize.Size14]
struct = "EnumItem"

[Enum.FontSize.Size18]
struct = "EnumItem"

[Enum.FontSize.Size24]
struct = "EnumItem"

[Enum.FontSize.Size28]
struct = "EnumItem"

[Enum.FontSize.Size32]
struct = "EnumItem"

[Enum.FontSize.Size36]
struct = "EnumItem"

[Enum.FontSize.Size42]
struct = "EnumItem"

[Enum.FontSize.Size48]
struct = "EnumItem"

[Enum.FontSize.Size60]
struct = "EnumItem"

[Enum.FontSize.Size8]
struct = "EnumItem"

[Enum.FontSize.Size9]
struct = "EnumItem"

[Enum.FontSize.Size96]
struct = "EnumItem"
[Enum.FormFactor.Brick]
struct = "EnumItem"

[Enum.FormFactor.Custom]
struct = "EnumItem"

[Enum.FormFactor.GetEnumItems]
method = true
args = []

[Enum.FormFactor.Plate]
struct = "EnumItem"

[Enum.FormFactor.Symmetric]
struct = "EnumItem"
[Enum.FrameStyle.ChatBlue]
struct = "EnumItem"

[Enum.FrameStyle.ChatGreen]
struct = "EnumItem"

[Enum.FrameStyle.ChatRed]
struct = "EnumItem"

[Enum.FrameStyle.Custom]
struct = "EnumItem"

[Enum.FrameStyle.DropShadow]
struct = "EnumItem"

[Enum.FrameStyle.GetEnumItems]
method = true
args = []

[Enum.FrameStyle.RobloxRound]
struct = "EnumItem"

[Enum.FrameStyle.RobloxSquare]
struct = "EnumItem"
[Enum.FramerateManagerMode.Automatic]
struct = "EnumItem"

[Enum.FramerateManagerMode.GetEnumItems]
method = true
args = []

[Enum.FramerateManagerMode.Off]
struct = "EnumItem"

[Enum.FramerateManagerMode.On]
struct = "EnumItem"
[Enum.FriendRequestEvent.Accept]
struct = "EnumItem"

[Enum.FriendRequestEvent.Deny]
struct = "EnumItem"

[Enum.FriendRequestEvent.GetEnumItems]
method = true
args = []

[Enum.FriendRequestEvent.Issue]
struct = "EnumItem"

[Enum.FriendRequestEvent.Revoke]
struct = "EnumItem"
[Enum.FriendStatus.Friend]
struct = "EnumItem"

[Enum.FriendStatus.FriendRequestReceived]
struct = "EnumItem"

[Enum.FriendStatus.FriendRequestSent]
struct = "EnumItem"

[Enum.FriendStatus.GetEnumItems]
method = true
args = []

[Enum.FriendStatus.NotFriend]
struct = "EnumItem"

[Enum.FriendStatus.Unknown]
struct = "EnumItem"
[Enum.FunctionalTestResult.Error]
struct = "EnumItem"

[Enum.FunctionalTestResult.GetEnumItems]
method = true
args = []

[Enum.FunctionalTestResult.Passed]
struct = "EnumItem"

[Enum.FunctionalTestResult.Warning]
struct = "EnumItem"
[Enum.GameAvatarType.GetEnumItems]
method = true
args = []

[Enum.GameAvatarType.PlayerChoice]
struct = "EnumItem"

[Enum.GameAvatarType.R15]
struct = "EnumItem"

[Enum.GameAvatarType.R6]
struct = "EnumItem"
[Enum.GearGenreSetting.AllGenres]
struct = "EnumItem"

[Enum.GearGenreSetting.GetEnumItems]
method = true
args = []

[Enum.GearGenreSetting.MatchingGenreOnly]
struct = "EnumItem"
[Enum.GearType.BuildingTools]
struct = "EnumItem"

[Enum.GearType.Explosives]
struct = "EnumItem"

[Enum.GearType.GetEnumItems]
method = true
args = []

[Enum.GearType.MeleeWeapons]
struct = "EnumItem"

[Enum.GearType.MusicalInstruments]
struct = "EnumItem"

[Enum.GearType.NavigationEnhancers]
struct = "EnumItem"

[Enum.GearType.PowerUps]
struct = "EnumItem"

[Enum.GearType.RangedWeapons]
struct = "EnumItem"

[Enum.GearType.SocialItems]
struct = "EnumItem"

[Enum.GearType.Transport]
struct = "EnumItem"
[Enum.Genre.Adventure]
struct = "EnumItem"

[Enum.Genre.All]
struct = "EnumItem"

[Enum.Genre.Fantasy]
struct = "EnumItem"

[Enum.Genre.Funny]
struct = "EnumItem"

[Enum.Genre.GetEnumItems]
method = true
args = []

[Enum.Genre.Ninja]
struct = "EnumItem"

[Enum.Genre.Pirate]
struct = "EnumItem"

[Enum.Genre.Scary]
struct = "EnumItem"

[Enum.Genre.SciFi]
struct = "EnumItem"

[Enum.Genre.SkatePark]
struct = "EnumItem"

[Enum.Genre.Sports]
struct = "EnumItem"

[Enum.Genre.TownAndCity]
struct = "EnumItem"

[Enum.Genre.Tutorial]
struct = "EnumItem"

[Enum.Genre.War]
struct = "EnumItem"

[Enum.Genre.WildWest]
struct = "EnumItem"
[Enum.GraphicsMode.Automatic]
struct = "EnumItem"

[Enum.GraphicsMode.Direct3D11]
struct = "EnumItem"

[Enum.GraphicsMode.GetEnumItems]
method = true
args = []

[Enum.GraphicsMode.Metal]
struct = "EnumItem"

[Enum.GraphicsMode.NoGraphics]
struct = "EnumItem"

[Enum.GraphicsMode.OpenGL]
struct = "EnumItem"

[Enum.GraphicsMode.Vulkan]
struct = "EnumItem"
[Enum.HandlesStyle.GetEnumItems]
method = true
args = []

[Enum.HandlesStyle.Movement]
struct = "EnumItem"

[Enum.HandlesStyle.Resize]
struct = "EnumItem"
[Enum.HighlightDepthMode.AlwaysOnTop]
struct = "EnumItem"

[Enum.HighlightDepthMode.Default]
struct = "EnumItem"

[Enum.HighlightDepthMode.GetEnumItems]
method = true
args = []
[Enum.HorizontalAlignment.Center]
struct = "EnumItem"

[Enum.HorizontalAlignment.GetEnumItems]
method = true
args = []

[Enum.HorizontalAlignment.Left]
struct = "EnumItem"

[Enum.HorizontalAlignment.Right]
struct = "EnumItem"
[Enum.HoverAnimateSpeed.Fast]
struct = "EnumItem"

[Enum.HoverAnimateSpeed.GetEnumItems]
method = true
args = []

[Enum.HoverAnimateSpeed.Medium]
struct = "EnumItem"

[Enum.HoverAnimateSpeed.Slow]
struct = "EnumItem"

[Enum.HoverAnimateSpeed.VeryFast]
struct = "EnumItem"

[Enum.HoverAnimateSpeed.VerySlow]
struct = "EnumItem"
[Enum.HttpCachePolicy.DataOnly]
struct = "EnumItem"

[Enum.HttpCachePolicy.Default]
struct = "EnumItem"

[Enum.HttpCachePolicy.Full]
struct = "EnumItem"

[Enum.HttpCachePolicy.GetEnumItems]
method = true
args = []

[Enum.HttpCachePolicy.InternalRedirectRefresh]
struct = "EnumItem"

[Enum.HttpCachePolicy.None]
struct = "EnumItem"
[Enum.HttpContentType.ApplicationJson]
struct = "EnumItem"

[Enum.HttpContentType.ApplicationUrlEncoded]
struct = "EnumItem"

[Enum.HttpContentType.ApplicationXml]
struct = "EnumItem"

[Enum.HttpContentType.GetEnumItems]
method = true
args = []

[Enum.HttpContentType.TextPlain]
struct = "EnumItem"

[Enum.HttpContentType.TextXml]
struct = "EnumItem"
[Enum.HttpError.Aborted]
struct = "EnumItem"

[Enum.HttpError.ConnectFail]
struct = "EnumItem"

[Enum.HttpError.DnsResolve]
struct = "EnumItem"

[Enum.HttpError.GetEnumItems]
method = true
args = []

[Enum.HttpError.InvalidRedirect]
struct = "EnumItem"

[Enum.HttpError.InvalidUrl]
struct = "EnumItem"

[Enum.HttpError.NetFail]
struct = "EnumItem"

[Enum.HttpError.OK]
struct = "EnumItem"

[Enum.HttpError.OutOfMemory]
struct = "EnumItem"

[Enum.HttpError.SslConnectFail]
struct = "EnumItem"

[Enum.HttpError.SslVerificationFail]
struct = "EnumItem"

[Enum.HttpError.TimedOut]
struct = "EnumItem"

[Enum.HttpError.TooManyRedirects]
struct = "EnumItem"

[Enum.HttpError.Unknown]
struct = "EnumItem"
[Enum.HttpRequestType.Analytics]
struct = "EnumItem"

[Enum.HttpRequestType.Avatar]
struct = "EnumItem"

[Enum.HttpRequestType.Chat]
struct = "EnumItem"

[Enum.HttpRequestType.Default]
struct = "EnumItem"

[Enum.HttpRequestType.GetEnumItems]
method = true
args = []

[Enum.HttpRequestType.Localization]
struct = "EnumItem"

[Enum.HttpRequestType.MarketplaceService]
struct = "EnumItem"

[Enum.HttpRequestType.Players]
struct = "EnumItem"
[Enum.HumanoidCollisionType.GetEnumItems]
method = true
args = []

[Enum.HumanoidCollisionType.InnerBox]
struct = "EnumItem"

[Enum.HumanoidCollisionType.OuterBox]
struct = "EnumItem"
[Enum.HumanoidDisplayDistanceType.GetEnumItems]
method = true
args = []

[Enum.HumanoidDisplayDistanceType.None]
struct = "EnumItem"

[Enum.HumanoidDisplayDistanceType.Subject]
struct = "EnumItem"

[Enum.HumanoidDisplayDistanceType.Viewer]
struct = "EnumItem"
[Enum.HumanoidHealthDisplayType.AlwaysOff]
struct = "EnumItem"

[Enum.HumanoidHealthDisplayType.AlwaysOn]
struct = "EnumItem"

[Enum.HumanoidHealthDisplayType.DisplayWhenDamaged]
struct = "EnumItem"

[Enum.HumanoidHealthDisplayType.GetEnumItems]
method = true
args = []
[Enum.HumanoidOnlySetCollisionsOnStateChange.Default]
struct = "EnumItem"

[Enum.HumanoidOnlySetCollisionsOnStateChange.Disabled]
struct = "EnumItem"

[Enum.HumanoidOnlySetCollisionsOnStateChange.Enabled]
struct = "EnumItem"

[Enum.HumanoidOnlySetCollisionsOnStateChange.GetEnumItems]
method = true
args = []
[Enum.HumanoidRigType.GetEnumItems]
method = true
args = []

[Enum.HumanoidRigType.R15]
struct = "EnumItem"

[Enum.HumanoidRigType.R6]
struct = "EnumItem"
[Enum.HumanoidStateType.Climbing]
struct = "EnumItem"

[Enum.HumanoidStateType.Dead]
struct = "EnumItem"

[Enum.HumanoidStateType.FallingDown]
struct = "EnumItem"

[Enum.HumanoidStateType.Flying]
struct = "EnumItem"

[Enum.HumanoidStateType.Freefall]
struct = "EnumItem"

[Enum.HumanoidStateType.GetEnumItems]
method = true
args = []

[Enum.HumanoidStateType.GettingUp]
struct = "EnumItem"

[Enum.HumanoidStateType.Jumping]
struct = "EnumItem"

[Enum.HumanoidStateType.Landed]
struct = "EnumItem"

[Enum.HumanoidStateType.None]
struct = "EnumItem"

[Enum.HumanoidStateType.Physics]
struct = "EnumItem"

[Enum.HumanoidStateType.PlatformStanding]
struct = "EnumItem"

[Enum.HumanoidStateType.Ragdoll]
struct = "EnumItem"

[Enum.HumanoidStateType.Running]
struct = "EnumItem"

[Enum.HumanoidStateType.RunningNoPhysics]
struct = "EnumItem"

[Enum.HumanoidStateType.Seated]
struct = "EnumItem"

[Enum.HumanoidStateType.StrafingNoPhysics]
struct = "EnumItem"

[Enum.HumanoidStateType.Swimming]
struct = "EnumItem"
[Enum.IKCollisionsMode.GetEnumItems]
method = true
args = []

[Enum.IKCollisionsMode.IncludeContactedMechanisms]
struct = "EnumItem"

[Enum.IKCollisionsMode.NoCollisions]
struct = "EnumItem"

[Enum.IKCollisionsMode.OtherMechanismsAnchored]
struct = "EnumItem"
[Enum.IXPLoadingStatus.ErrorConnection]
struct = "EnumItem"

[Enum.IXPLoadingStatus.ErrorInvalidUser]
struct = "EnumItem"

[Enum.IXPLoadingStatus.ErrorJsonParse]
struct = "EnumItem"

[Enum.IXPLoadingStatus.ErrorTimedOut]
struct = "EnumItem"

[Enum.IXPLoadingStatus.GetEnumItems]
method = true
args = []

[Enum.IXPLoadingStatus.Initialized]
struct = "EnumItem"

[Enum.IXPLoadingStatus.None]
struct = "EnumItem"

[Enum.IXPLoadingStatus.Pending]
struct = "EnumItem"

[Enum.IXPLoadingStatus.ShutOff]
struct = "EnumItem"
[Enum.InOut.Center]
struct = "EnumItem"

[Enum.InOut.Edge]
struct = "EnumItem"

[Enum.InOut.GetEnumItems]
method = true
args = []

[Enum.InOut.Inset]
struct = "EnumItem"
[Enum.InfoType.Asset]
struct = "EnumItem"

[Enum.InfoType.Bundle]
struct = "EnumItem"

[Enum.InfoType.GamePass]
struct = "EnumItem"

[Enum.InfoType.GetEnumItems]
method = true
args = []

[Enum.InfoType.Product]
struct = "EnumItem"

[Enum.InfoType.Subscription]
struct = "EnumItem"
[Enum.InitialDockState.Bottom]
struct = "EnumItem"

[Enum.InitialDockState.Float]
struct = "EnumItem"

[Enum.InitialDockState.GetEnumItems]
method = true
args = []

[Enum.InitialDockState.Left]
struct = "EnumItem"

[Enum.InitialDockState.Right]
struct = "EnumItem"

[Enum.InitialDockState.Top]
struct = "EnumItem"
[Enum.InputType.Constant]
struct = "EnumItem"

[Enum.InputType.GetEnumItems]
method = true
args = []

[Enum.InputType.NoInput]
struct = "EnumItem"

[Enum.InputType.Sin]
struct = "EnumItem"
[Enum.InterpolationThrottlingMode.Default]
struct = "EnumItem"

[Enum.InterpolationThrottlingMode.Disabled]
struct = "EnumItem"

[Enum.InterpolationThrottlingMode.Enabled]
struct = "EnumItem"

[Enum.InterpolationThrottlingMode.GetEnumItems]
method = true
args = []
[Enum.JointCreationMode.All]
struct = "EnumItem"

[Enum.JointCreationMode.GetEnumItems]
method = true
args = []

[Enum.JointCreationMode.None]
struct = "EnumItem"

[Enum.JointCreationMode.Surface]
struct = "EnumItem"
[Enum.KeyCode.A]
struct = "EnumItem"

[Enum.KeyCode.Ampersand]
struct = "EnumItem"

[Enum.KeyCode.Asterisk]
struct = "EnumItem"

[Enum.KeyCode.At]
struct = "EnumItem"

[Enum.KeyCode.B]
struct = "EnumItem"

[Enum.KeyCode.BackSlash]
struct = "EnumItem"

[Enum.KeyCode.Backquote]
struct = "EnumItem"

[Enum.KeyCode.Backspace]
struct = "EnumItem"

[Enum.KeyCode.Break]
struct = "EnumItem"

[Enum.KeyCode.ButtonA]
struct = "EnumItem"

[Enum.KeyCode.ButtonB]
struct = "EnumItem"

[Enum.KeyCode.ButtonL1]
struct = "EnumItem"

[Enum.KeyCode.ButtonL2]
struct = "EnumItem"

[Enum.KeyCode.ButtonL3]
struct = "EnumItem"

[Enum.KeyCode.ButtonR1]
struct = "EnumItem"

[Enum.KeyCode.ButtonR2]
struct = "EnumItem"

[Enum.KeyCode.ButtonR3]
struct = "EnumItem"

[Enum.KeyCode.ButtonSelect]
struct = "EnumItem"

[Enum.KeyCode.ButtonStart]
struct = "EnumItem"

[Enum.KeyCode.ButtonX]
struct = "EnumItem"

[Enum.KeyCode.ButtonY]
struct = "EnumItem"

[Enum.KeyCode.C]
struct = "EnumItem"

[Enum.KeyCode.CapsLock]
struct = "EnumItem"

[Enum.KeyCode.Caret]
struct = "EnumItem"

[Enum.KeyCode.Clear]
struct = "EnumItem"

[Enum.KeyCode.Colon]
struct = "EnumItem"

[Enum.KeyCode.Comma]
struct = "EnumItem"

[Enum.KeyCode.Compose]
struct = "EnumItem"

[Enum.KeyCode.D]
struct = "EnumItem"

[Enum.KeyCode.DPadDown]
struct = "EnumItem"

[Enum.KeyCode.DPadLeft]
struct = "EnumItem"

[Enum.KeyCode.DPadRight]
struct = "EnumItem"

[Enum.KeyCode.DPadUp]
struct = "EnumItem"

[Enum.KeyCode.Delete]
struct = "EnumItem"

[Enum.KeyCode.Dollar]
struct = "EnumItem"

[Enum.KeyCode.Down]
struct = "EnumItem"

[Enum.KeyCode.E]
struct = "EnumItem"

[Enum.KeyCode.Eight]
struct = "EnumItem"

[Enum.KeyCode.End]
struct = "EnumItem"

[Enum.KeyCode.Equals]
struct = "EnumItem"

[Enum.KeyCode.Escape]
struct = "EnumItem"

[Enum.KeyCode.Euro]
struct = "EnumItem"

[Enum.KeyCode.F]
struct = "EnumItem"

[Enum.KeyCode.F1]
struct = "EnumItem"

[Enum.KeyCode.F10]
struct = "EnumItem"

[Enum.KeyCode.F11]
struct = "EnumItem"

[Enum.KeyCode.F12]
struct = "EnumItem"

[Enum.KeyCode.F13]
struct = "EnumItem"

[Enum.KeyCode.F14]
struct = "EnumItem"

[Enum.KeyCode.F15]
struct = "EnumItem"

[Enum.KeyCode.F2]
struct = "EnumItem"

[Enum.KeyCode.F3]
struct = "EnumItem"

[Enum.KeyCode.F4]
struct = "EnumItem"

[Enum.KeyCode.F5]
struct = "EnumItem"

[Enum.KeyCode.F6]
struct = "EnumItem"

[Enum.KeyCode.F7]
struct = "EnumItem"

[Enum.KeyCode.F8]
struct = "EnumItem"

[Enum.KeyCode.F9]
struct = "EnumItem"

[Enum.KeyCode.Five]
struct = "EnumItem"

[Enum.KeyCode.Four]
struct = "EnumItem"

[Enum.KeyCode.G]
struct = "EnumItem"

[Enum.KeyCode.GetEnumItems]
method = true
args = []

[Enum.KeyCode.GreaterThan]
struct = "EnumItem"

[Enum.KeyCode.H]
struct = "EnumItem"

[Enum.KeyCode.Hash]
struct = "EnumItem"

[Enum.KeyCode.Help]
struct = "EnumItem"

[Enum.KeyCode.Home]
struct = "EnumItem"

[Enum.KeyCode.I]
struct = "EnumItem"

[Enum.KeyCode.Insert]
struct = "EnumItem"

[Enum.KeyCode.J]
struct = "EnumItem"

[Enum.KeyCode.K]
struct = "EnumItem"

[Enum.KeyCode.KeypadDivide]
struct = "EnumItem"

[Enum.KeyCode.KeypadEight]
struct = "EnumItem"

[Enum.KeyCode.KeypadEnter]
struct = "EnumItem"

[Enum.KeyCode.KeypadEquals]
struct = "EnumItem"

[Enum.KeyCode.KeypadFive]
struct = "EnumItem"

[Enum.KeyCode.KeypadFour]
struct = "EnumItem"

[Enum.KeyCode.KeypadMinus]
struct = "EnumItem"

[Enum.KeyCode.KeypadMultiply]
struct = "EnumItem"

[Enum.KeyCode.KeypadNine]
struct = "EnumItem"

[Enum.KeyCode.KeypadOne]
struct = "EnumItem"

[Enum.KeyCode.KeypadPeriod]
struct = "EnumItem"

[Enum.KeyCode.KeypadPlus]
struct = "EnumItem"

[Enum.KeyCode.KeypadSeven]
struct = "EnumItem"

[Enum.KeyCode.KeypadSix]
struct = "EnumItem"

[Enum.KeyCode.KeypadThree]
struct = "EnumItem"

[Enum.KeyCode.KeypadTwo]
struct = "EnumItem"

[Enum.KeyCode.KeypadZero]
struct = "EnumItem"

[Enum.KeyCode.L]
struct = "EnumItem"

[Enum.KeyCode.Left]
struct = "EnumItem"

[Enum.KeyCode.LeftAlt]
struct = "EnumItem"

[Enum.KeyCode.LeftBracket]
struct = "EnumItem"

[Enum.KeyCode.LeftControl]
struct = "EnumItem"

[Enum.KeyCode.LeftCurly]
struct = "EnumItem"

[Enum.KeyCode.LeftMeta]
struct = "EnumItem"

[Enum.KeyCode.LeftParenthesis]
struct = "EnumItem"

[Enum.KeyCode.LeftShift]
struct = "EnumItem"

[Enum.KeyCode.LeftSuper]
struct = "EnumItem"

[Enum.KeyCode.LessThan]
struct = "EnumItem"

[Enum.KeyCode.M]
struct = "EnumItem"

[Enum.KeyCode.Menu]
struct = "EnumItem"

[Enum.KeyCode.Minus]
struct = "EnumItem"

[Enum.KeyCode.Mode]
struct = "EnumItem"

[Enum.KeyCode.N]
struct = "EnumItem"

[Enum.KeyCode.Nine]
struct = "EnumItem"

[Enum.KeyCode.NumLock]
struct = "EnumItem"

[Enum.KeyCode.O]
struct = "EnumItem"

[Enum.KeyCode.One]
struct = "EnumItem"

[Enum.KeyCode.P]
struct = "EnumItem"

[Enum.KeyCode.PageDown]
struct = "EnumItem"

[Enum.KeyCode.PageUp]
struct = "EnumItem"

[Enum.KeyCode.Pause]
struct = "EnumItem"

[Enum.KeyCode.Percent]
struct = "EnumItem"

[Enum.KeyCode.Period]
struct = "EnumItem"

[Enum.KeyCode.Pipe]
struct = "EnumItem"

[Enum.KeyCode.Plus]
struct = "EnumItem"

[Enum.KeyCode.Power]
struct = "EnumItem"

[Enum.KeyCode.Print]
struct = "EnumItem"

[Enum.KeyCode.Q]
struct = "EnumItem"

[Enum.KeyCode.Question]
struct = "EnumItem"

[Enum.KeyCode.Quote]
struct = "EnumItem"

[Enum.KeyCode.QuotedDouble]
struct = "EnumItem"

[Enum.KeyCode.R]
struct = "EnumItem"

[Enum.KeyCode.Return]
struct = "EnumItem"

[Enum.KeyCode.Right]
struct = "EnumItem"

[Enum.KeyCode.RightAlt]
struct = "EnumItem"

[Enum.KeyCode.RightBracket]
struct = "EnumItem"

[Enum.KeyCode.RightControl]
struct = "EnumItem"

[Enum.KeyCode.RightCurly]
struct = "EnumItem"

[Enum.KeyCode.RightMeta]
struct = "EnumItem"

[Enum.KeyCode.RightParenthesis]
struct = "EnumItem"

[Enum.KeyCode.RightShift]
struct = "EnumItem"

[Enum.KeyCode.RightSuper]
struct = "EnumItem"

[Enum.KeyCode.S]
struct = "EnumItem"

[Enum.KeyCode.ScrollLock]
struct = "EnumItem"

[Enum.KeyCode.Semicolon]
struct = "EnumItem"

[Enum.KeyCode.Seven]
struct = "EnumItem"

[Enum.KeyCode.Six]
struct = "EnumItem"

[Enum.KeyCode.Slash]
struct = "EnumItem"

[Enum.KeyCode.Space]
struct = "EnumItem"

[Enum.KeyCode.SysReq]
struct = "EnumItem"

[Enum.KeyCode.T]
struct = "EnumItem"

[Enum.KeyCode.Tab]
struct = "EnumItem"

[Enum.KeyCode.Three]
struct = "EnumItem"

[Enum.KeyCode.Thumbstick1]
struct = "EnumItem"

[Enum.KeyCode.Thumbstick2]
struct = "EnumItem"

[Enum.KeyCode.Tilde]
struct = "EnumItem"

[Enum.KeyCode.Two]
struct = "EnumItem"

[Enum.KeyCode.U]
struct = "EnumItem"

[Enum.KeyCode.Underscore]
struct = "EnumItem"

[Enum.KeyCode.Undo]
struct = "EnumItem"

[Enum.KeyCode.Unknown]
struct = "EnumItem"

[Enum.KeyCode.Up]
struct = "EnumItem"

[Enum.KeyCode.V]
struct = "EnumItem"

[Enum.KeyCode.W]
struct = "EnumItem"

[Enum.KeyCode.World0]
struct = "EnumItem"

[Enum.KeyCode.World1]
struct = "EnumItem"

[Enum.KeyCode.World10]
struct = "EnumItem"

[Enum.KeyCode.World11]
struct = "EnumItem"

[Enum.KeyCode.World12]
struct = "EnumItem"

[Enum.KeyCode.World13]
struct = "EnumItem"

[Enum.KeyCode.World14]
struct = "EnumItem"

[Enum.KeyCode.World15]
struct = "EnumItem"

[Enum.KeyCode.World16]
struct = "EnumItem"

[Enum.KeyCode.World17]
struct = "EnumItem"

[Enum.KeyCode.World18]
struct = "EnumItem"

[Enum.KeyCode.World19]
struct = "EnumItem"

[Enum.KeyCode.World2]
struct = "EnumItem"

[Enum.KeyCode.World20]
struct = "EnumItem"

[Enum.KeyCode.World21]
struct = "EnumItem"

[Enum.KeyCode.World22]
struct = "EnumItem"

[Enum.KeyCode.World23]
struct = "EnumItem"

[Enum.KeyCode.World24]
struct = "EnumItem"

[Enum.KeyCode.World25]
struct = "EnumItem"

[Enum.KeyCode.World26]
struct = "EnumItem"

[Enum.KeyCode.World27]
struct = "EnumItem"

[Enum.KeyCode.World28]
struct = "EnumItem"

[Enum.KeyCode.World29]
struct = "EnumItem"

[Enum.KeyCode.World3]
struct = "EnumItem"

[Enum.KeyCode.World30]
struct = "EnumItem"

[Enum.KeyCode.World31]
struct = "EnumItem"

[Enum.KeyCode.World32]
struct = "EnumItem"

[Enum.KeyCode.World33]
struct = "EnumItem"

[Enum.KeyCode.World34]
struct = "EnumItem"

[Enum.KeyCode.World35]
struct = "EnumItem"

[Enum.KeyCode.World36]
struct = "EnumItem"

[Enum.KeyCode.World37]
struct = "EnumItem"

[Enum.KeyCode.World38]
struct = "EnumItem"

[Enum.KeyCode.World39]
struct = "EnumItem"

[Enum.KeyCode.World4]
struct = "EnumItem"

[Enum.KeyCode.World40]
struct = "EnumItem"

[Enum.KeyCode.World41]
struct = "EnumItem"

[Enum.KeyCode.World42]
struct = "EnumItem"

[Enum.KeyCode.World43]
struct = "EnumItem"

[Enum.KeyCode.World44]
struct = "EnumItem"

[Enum.KeyCode.World45]
struct = "EnumItem"

[Enum.KeyCode.World46]
struct = "EnumItem"

[Enum.KeyCode.World47]
struct = "EnumItem"

[Enum.KeyCode.World48]
struct = "EnumItem"

[Enum.KeyCode.World49]
struct = "EnumItem"

[Enum.KeyCode.World5]
struct = "EnumItem"

[Enum.KeyCode.World50]
struct = "EnumItem"

[Enum.KeyCode.World51]
struct = "EnumItem"

[Enum.KeyCode.World52]
struct = "EnumItem"

[Enum.KeyCode.World53]
struct = "EnumItem"

[Enum.KeyCode.World54]
struct = "EnumItem"

[Enum.KeyCode.World55]
struct = "EnumItem"

[Enum.KeyCode.World56]
struct = "EnumItem"

[Enum.KeyCode.World57]
struct = "EnumItem"

[Enum.KeyCode.World58]
struct = "EnumItem"

[Enum.KeyCode.World59]
struct = "EnumItem"

[Enum.KeyCode.World6]
struct = "EnumItem"

[Enum.KeyCode.World60]
struct = "EnumItem"

[Enum.KeyCode.World61]
struct = "EnumItem"

[Enum.KeyCode.World62]
struct = "EnumItem"

[Enum.KeyCode.World63]
struct = "EnumItem"

[Enum.KeyCode.World64]
struct = "EnumItem"

[Enum.KeyCode.World65]
struct = "EnumItem"

[Enum.KeyCode.World66]
struct = "EnumItem"

[Enum.KeyCode.World67]
struct = "EnumItem"

[Enum.KeyCode.World68]
struct = "EnumItem"

[Enum.KeyCode.World69]
struct = "EnumItem"

[Enum.KeyCode.World7]
struct = "EnumItem"

[Enum.KeyCode.World70]
struct = "EnumItem"

[Enum.KeyCode.World71]
struct = "EnumItem"

[Enum.KeyCode.World72]
struct = "EnumItem"

[Enum.KeyCode.World73]
struct = "EnumItem"

[Enum.KeyCode.World74]
struct = "EnumItem"

[Enum.KeyCode.World75]
struct = "EnumItem"

[Enum.KeyCode.World76]
struct = "EnumItem"

[Enum.KeyCode.World77]
struct = "EnumItem"

[Enum.KeyCode.World78]
struct = "EnumItem"

[Enum.KeyCode.World79]
struct = "EnumItem"

[Enum.KeyCode.World8]
struct = "EnumItem"

[Enum.KeyCode.World80]
struct = "EnumItem"

[Enum.KeyCode.World81]
struct = "EnumItem"

[Enum.KeyCode.World82]
struct = "EnumItem"

[Enum.KeyCode.World83]
struct = "EnumItem"

[Enum.KeyCode.World84]
struct = "EnumItem"

[Enum.KeyCode.World85]
struct = "EnumItem"

[Enum.KeyCode.World86]
struct = "EnumItem"

[Enum.KeyCode.World87]
struct = "EnumItem"

[Enum.KeyCode.World88]
struct = "EnumItem"

[Enum.KeyCode.World89]
struct = "EnumItem"

[Enum.KeyCode.World9]
struct = "EnumItem"

[Enum.KeyCode.World90]
struct = "EnumItem"

[Enum.KeyCode.World91]
struct = "EnumItem"

[Enum.KeyCode.World92]
struct = "EnumItem"

[Enum.KeyCode.World93]
struct = "EnumItem"

[Enum.KeyCode.World94]
struct = "EnumItem"

[Enum.KeyCode.World95]
struct = "EnumItem"

[Enum.KeyCode.X]
struct = "EnumItem"

[Enum.KeyCode.Y]
struct = "EnumItem"

[Enum.KeyCode.Z]
struct = "EnumItem"

[Enum.KeyCode.Zero]
struct = "EnumItem"
[Enum.KeyInterpolationMode.Constant]
struct = "EnumItem"

[Enum.KeyInterpolationMode.Cubic]
struct = "EnumItem"

[Enum.KeyInterpolationMode.GetEnumItems]
method = true
args = []

[Enum.KeyInterpolationMode.Linear]
struct = "EnumItem"
[Enum.KeywordFilterType.Exclude]
struct = "EnumItem"

[Enum.KeywordFilterType.GetEnumItems]
method = true
args = []

[Enum.KeywordFilterType.Include]
struct = "EnumItem"
[Enum.Language.Default]
struct = "EnumItem"

[Enum.Language.GetEnumItems]
method = true
args = []
[Enum.LeftRight.Center]
struct = "EnumItem"

[Enum.LeftRight.GetEnumItems]
method = true
args = []

[Enum.LeftRight.Left]
struct = "EnumItem"

[Enum.LeftRight.Right]
struct = "EnumItem"
[Enum.LevelOfDetailSetting.GetEnumItems]
method = true
args = []

[Enum.LevelOfDetailSetting.High]
struct = "EnumItem"

[Enum.LevelOfDetailSetting.Low]
struct = "EnumItem"

[Enum.LevelOfDetailSetting.Medium]
struct = "EnumItem"
[Enum.Limb.GetEnumItems]
method = true
args = []

[Enum.Limb.Head]
struct = "EnumItem"

[Enum.Limb.LeftArm]
struct = "EnumItem"

[Enum.Limb.LeftLeg]
struct = "EnumItem"

[Enum.Limb.RightArm]
struct = "EnumItem"

[Enum.Limb.RightLeg]
struct = "EnumItem"

[Enum.Limb.Torso]
struct = "EnumItem"

[Enum.Limb.Unknown]
struct = "EnumItem"
[Enum.LineJoinMode.Bevel]
struct = "EnumItem"

[Enum.LineJoinMode.GetEnumItems]
method = true
args = []

[Enum.LineJoinMode.Miter]
struct = "EnumItem"

[Enum.LineJoinMode.Round]
struct = "EnumItem"
[Enum.ListDisplayMode.GetEnumItems]
method = true
args = []

[Enum.ListDisplayMode.Horizontal]
struct = "EnumItem"

[Enum.ListDisplayMode.Vertical]
struct = "EnumItem"
[Enum.ListenerType.CFrame]
struct = "EnumItem"

[Enum.ListenerType.Camera]
struct = "EnumItem"

[Enum.ListenerType.GetEnumItems]
method = true
args = []

[Enum.ListenerType.ObjectCFrame]
struct = "EnumItem"

[Enum.ListenerType.ObjectPosition]
struct = "EnumItem"
[Enum.LoadCharacterLayeredClothing.Default]
struct = "EnumItem"

[Enum.LoadCharacterLayeredClothing.Disabled]
struct = "EnumItem"

[Enum.LoadCharacterLayeredClothing.Enabled]
struct = "EnumItem"

[Enum.LoadCharacterLayeredClothing.GetEnumItems]
method = true
args = []
[Enum.Material.Air]
struct = "EnumItem"

[Enum.Material.Asphalt]
struct = "EnumItem"

[Enum.Material.Basalt]
struct = "EnumItem"

[Enum.Material.Brick]
struct = "EnumItem"

[Enum.Material.Cobblestone]
struct = "EnumItem"

[Enum.Material.Concrete]
struct = "EnumItem"

[Enum.Material.CorrodedMetal]
struct = "EnumItem"

[Enum.Material.CrackedLava]
struct = "EnumItem"

[Enum.Material.DiamondPlate]
struct = "EnumItem"

[Enum.Material.Fabric]
struct = "EnumItem"

[Enum.Material.Foil]
struct = "EnumItem"

[Enum.Material.ForceField]
struct = "EnumItem"

[Enum.Material.GetEnumItems]
method = true
args = []

[Enum.Material.Glacier]
struct = "EnumItem"

[Enum.Material.Glass]
struct = "EnumItem"

[Enum.Material.Granite]
struct = "EnumItem"

[Enum.Material.Grass]
struct = "EnumItem"

[Enum.Material.Ground]
struct = "EnumItem"

[Enum.Material.Ice]
struct = "EnumItem"

[Enum.Material.LeafyGrass]
struct = "EnumItem"

[Enum.Material.Limestone]
struct = "EnumItem"

[Enum.Material.Marble]
struct = "EnumItem"

[Enum.Material.Metal]
struct = "EnumItem"

[Enum.Material.Mud]
struct = "EnumItem"

[Enum.Material.Neon]
struct = "EnumItem"

[Enum.Material.Pavement]
struct = "EnumItem"

[Enum.Material.Pebble]
struct = "EnumItem"

[Enum.Material.Plastic]
struct = "EnumItem"

[Enum.Material.Rock]
struct = "EnumItem"

[Enum.Material.Salt]
struct = "EnumItem"

[Enum.Material.Sand]
struct = "EnumItem"

[Enum.Material.Sandstone]
struct = "EnumItem"

[Enum.Material.Slate]
struct = "EnumItem"

[Enum.Material.SmoothPlastic]
struct = "EnumItem"

[Enum.Material.Snow]
struct = "EnumItem"

[Enum.Material.Water]
struct = "EnumItem"

[Enum.Material.Wood]
struct = "EnumItem"

[Enum.Material.WoodPlanks]
struct = "EnumItem"
[Enum.MembershipType.BuildersClub]
struct = "EnumItem"

[Enum.MembershipType.GetEnumItems]
method = true
args = []

[Enum.MembershipType.None]
struct = "EnumItem"

[Enum.MembershipType.OutrageousBuildersClub]
struct = "EnumItem"

[Enum.MembershipType.Premium]
struct = "EnumItem"

[Enum.MembershipType.TurboBuildersClub]
struct = "EnumItem"
[Enum.MeshPartDetailLevel.DistanceBased]
struct = "EnumItem"

[Enum.MeshPartDetailLevel.GetEnumItems]
method = true
args = []

[Enum.MeshPartDetailLevel.Level01]
struct = "EnumItem"

[Enum.MeshPartDetailLevel.Level02]
struct = "EnumItem"

[Enum.MeshPartDetailLevel.Level03]
struct = "EnumItem"

[Enum.MeshPartDetailLevel.Level04]
struct = "EnumItem"
[Enum.MeshPartHeadsAndAccessories.Default]
struct = "EnumItem"

[Enum.MeshPartHeadsAndAccessories.Disabled]
struct = "EnumItem"

[Enum.MeshPartHeadsAndAccessories.Enabled]
struct = "EnumItem"

[Enum.MeshPartHeadsAndAccessories.GetEnumItems]
method = true
args = []
[Enum.MeshScaleUnit.CM]
struct = "EnumItem"

[Enum.MeshScaleUnit.Foot]
struct = "EnumItem"

[Enum.MeshScaleUnit.GetEnumItems]
method = true
args = []

[Enum.MeshScaleUnit.Inch]
struct = "EnumItem"

[Enum.MeshScaleUnit.MM]
struct = "EnumItem"

[Enum.MeshScaleUnit.Meter]
struct = "EnumItem"

[Enum.MeshScaleUnit.Stud]
struct = "EnumItem"
[Enum.MeshType.Brick]
struct = "EnumItem"

[Enum.MeshType.CornerWedge]
struct = "EnumItem"

[Enum.MeshType.Cylinder]
struct = "EnumItem"

[Enum.MeshType.FileMesh]
struct = "EnumItem"

[Enum.MeshType.GetEnumItems]
method = true
args = []

[Enum.MeshType.Head]
struct = "EnumItem"

[Enum.MeshType.ParallelRamp]
struct = "EnumItem"

[Enum.MeshType.Prism]
struct = "EnumItem"

[Enum.MeshType.Pyramid]
struct = "EnumItem"

[Enum.MeshType.RightAngleRamp]
struct = "EnumItem"

[Enum.MeshType.Sphere]
struct = "EnumItem"

[Enum.MeshType.Torso]
struct = "EnumItem"

[Enum.MeshType.Wedge]
struct = "EnumItem"
[Enum.MessageType.GetEnumItems]
method = true
args = []

[Enum.MessageType.MessageError]
struct = "EnumItem"

[Enum.MessageType.MessageInfo]
struct = "EnumItem"

[Enum.MessageType.MessageOutput]
struct = "EnumItem"

[Enum.MessageType.MessageWarning]
struct = "EnumItem"
[Enum.ModelLevelOfDetail.Automatic]
struct = "EnumItem"

[Enum.ModelLevelOfDetail.Disabled]
struct = "EnumItem"

[Enum.ModelLevelOfDetail.GetEnumItems]
method = true
args = []

[Enum.ModelLevelOfDetail.StreamingMesh]
struct = "EnumItem"
[Enum.ModifierKey.Alt]
struct = "EnumItem"

[Enum.ModifierKey.Ctrl]
struct = "EnumItem"

[Enum.ModifierKey.GetEnumItems]
method = true
args = []

[Enum.ModifierKey.Meta]
struct = "EnumItem"

[Enum.ModifierKey.Shift]
struct = "EnumItem"
[Enum.MouseBehavior.Default]
struct = "EnumItem"

[Enum.MouseBehavior.GetEnumItems]
method = true
args = []

[Enum.MouseBehavior.LockCenter]
struct = "EnumItem"

[Enum.MouseBehavior.LockCurrentPosition]
struct = "EnumItem"
[Enum.MoveState.AirFree]
struct = "EnumItem"

[Enum.MoveState.Coasting]
struct = "EnumItem"

[Enum.MoveState.GetEnumItems]
method = true
args = []

[Enum.MoveState.Pushing]
struct = "EnumItem"

[Enum.MoveState.Stopped]
struct = "EnumItem"

[Enum.MoveState.Stopping]
struct = "EnumItem"
[Enum.NameOcclusion.EnemyOcclusion]
struct = "EnumItem"

[Enum.NameOcclusion.GetEnumItems]
method = true
args = []

[Enum.NameOcclusion.NoOcclusion]
struct = "EnumItem"

[Enum.NameOcclusion.OccludeAll]
struct = "EnumItem"
[Enum.NetworkOwnership.Automatic]
struct = "EnumItem"

[Enum.NetworkOwnership.GetEnumItems]
method = true
args = []

[Enum.NetworkOwnership.Manual]
struct = "EnumItem"

[Enum.NetworkOwnership.OnContact]
struct = "EnumItem"
[Enum.NewAnimationRuntimeSetting.Default]
struct = "EnumItem"

[Enum.NewAnimationRuntimeSetting.Disabled]
struct = "EnumItem"

[Enum.NewAnimationRuntimeSetting.Enabled]
struct = "EnumItem"

[Enum.NewAnimationRuntimeSetting.GetEnumItems]
method = true
args = []
[Enum.NormalId.Back]
struct = "EnumItem"

[Enum.NormalId.Bottom]
struct = "EnumItem"

[Enum.NormalId.Front]
struct = "EnumItem"

[Enum.NormalId.GetEnumItems]
method = true
args = []

[Enum.NormalId.Left]
struct = "EnumItem"

[Enum.NormalId.Right]
struct = "EnumItem"

[Enum.NormalId.Top]
struct = "EnumItem"
[Enum.OrientationAlignmentMode.GetEnumItems]
method = true
args = []

[Enum.OrientationAlignmentMode.OneAttachment]
struct = "EnumItem"

[Enum.OrientationAlignmentMode.TwoAttachment]
struct = "EnumItem"
[Enum.OutfitSource.All]
struct = "EnumItem"

[Enum.OutfitSource.Created]
struct = "EnumItem"

[Enum.OutfitSource.GetEnumItems]
method = true
args = []

[Enum.OutfitSource.Purchased]
struct = "EnumItem"
[Enum.OutputLayoutMode.GetEnumItems]
method = true
args = []

[Enum.OutputLayoutMode.Horizontal]
struct = "EnumItem"

[Enum.OutputLayoutMode.Vertical]
struct = "EnumItem"
[Enum.OverrideMouseIconBehavior.ForceHide]
struct = "EnumItem"

[Enum.OverrideMouseIconBehavior.ForceShow]
struct = "EnumItem"

[Enum.OverrideMouseIconBehavior.GetEnumItems]
method = true
args = []

[Enum.OverrideMouseIconBehavior.None]
struct = "EnumItem"
[Enum.PackagePermission.Edit]
struct = "EnumItem"

[Enum.PackagePermission.GetEnumItems]
method = true
args = []

[Enum.PackagePermission.NoAccess]
struct = "EnumItem"

[Enum.PackagePermission.None]
struct = "EnumItem"

[Enum.PackagePermission.Own]
struct = "EnumItem"

[Enum.PackagePermission.Revoked]
struct = "EnumItem"

[Enum.PackagePermission.UseView]
struct = "EnumItem"
[Enum.PacketPriority.GetEnumItems]
method = true
args = []

[Enum.PacketPriority.HIGH_PRIORITY]
struct = "EnumItem"

[Enum.PacketPriority.IMMEDIATE_PRIORITY]
struct = "EnumItem"

[Enum.PacketPriority.LOW_PRIORITY]
struct = "EnumItem"

[Enum.PacketPriority.MEDIUM_PRIORITY]
struct = "EnumItem"
[Enum.PartType.Ball]
struct = "EnumItem"

[Enum.PartType.Block]
struct = "EnumItem"

[Enum.PartType.Cylinder]
struct = "EnumItem"

[Enum.PartType.GetEnumItems]
method = true
args = []
[Enum.ParticleEmitterShape.Box]
struct = "EnumItem"

[Enum.ParticleEmitterShape.Cylinder]
struct = "EnumItem"

[Enum.ParticleEmitterShape.Disc]
struct = "EnumItem"

[Enum.ParticleEmitterShape.GetEnumItems]
method = true
args = []

[Enum.ParticleEmitterShape.Sphere]
struct = "EnumItem"
[Enum.ParticleEmitterShapeInOut.GetEnumItems]
method = true
args = []

[Enum.ParticleEmitterShapeInOut.InAndOut]
struct = "EnumItem"

[Enum.ParticleEmitterShapeInOut.Inward]
struct = "EnumItem"

[Enum.ParticleEmitterShapeInOut.Outward]
struct = "EnumItem"
[Enum.ParticleEmitterShapeStyle.GetEnumItems]
method = true
args = []

[Enum.ParticleEmitterShapeStyle.Surface]
struct = "EnumItem"

[Enum.ParticleEmitterShapeStyle.Volume]
struct = "EnumItem"
[Enum.ParticleOrientation.FacingCamera]
struct = "EnumItem"

[Enum.ParticleOrientation.FacingCameraWorldUp]
struct = "EnumItem"

[Enum.ParticleOrientation.GetEnumItems]
method = true
args = []

[Enum.ParticleOrientation.VelocityParallel]
struct = "EnumItem"

[Enum.ParticleOrientation.VelocityPerpendicular]
struct = "EnumItem"
[Enum.PathStatus.ClosestNoPath]
struct = "EnumItem"

[Enum.PathStatus.ClosestOutOfRange]
struct = "EnumItem"

[Enum.PathStatus.FailFinishNotEmpty]
struct = "EnumItem"

[Enum.PathStatus.FailStartNotEmpty]
struct = "EnumItem"

[Enum.PathStatus.GetEnumItems]
method = true
args = []

[Enum.PathStatus.NoPath]
struct = "EnumItem"

[Enum.PathStatus.Success]
struct = "EnumItem"
[Enum.PathWaypointAction.GetEnumItems]
method = true
args = []

[Enum.PathWaypointAction.Jump]
struct = "EnumItem"

[Enum.PathWaypointAction.Walk]
struct = "EnumItem"
[Enum.PermissionLevelShown.Game]
struct = "EnumItem"

[Enum.PermissionLevelShown.GetEnumItems]
method = true
args = []

[Enum.PermissionLevelShown.Roblox]
struct = "EnumItem"

[Enum.PermissionLevelShown.RobloxGame]
struct = "EnumItem"

[Enum.PermissionLevelShown.RobloxScript]
struct = "EnumItem"

[Enum.PermissionLevelShown.Studio]
struct = "EnumItem"
[Enum.PhysicsSimulationRate.Fixed120Hz]
struct = "EnumItem"

[Enum.PhysicsSimulationRate.Fixed240Hz]
struct = "EnumItem"

[Enum.PhysicsSimulationRate.Fixed60Hz]
struct = "EnumItem"

[Enum.PhysicsSimulationRate.GetEnumItems]
method = true
args = []
[Enum.PhysicsSteppingMethod.Adaptive]
struct = "EnumItem"

[Enum.PhysicsSteppingMethod.Default]
struct = "EnumItem"

[Enum.PhysicsSteppingMethod.Fixed]
struct = "EnumItem"

[Enum.PhysicsSteppingMethod.GetEnumItems]
method = true
args = []
[Enum.Platform.Android]
struct = "EnumItem"

[Enum.Platform.AndroidTV]
struct = "EnumItem"

[Enum.Platform.BeOS]
struct = "EnumItem"

[Enum.Platform.Chromecast]
struct = "EnumItem"

[Enum.Platform.DOS]
struct = "EnumItem"

[Enum.Platform.GetEnumItems]
method = true
args = []

[Enum.Platform.IOS]
struct = "EnumItem"

[Enum.Platform.Linux]
struct = "EnumItem"

[Enum.Platform.NX]
struct = "EnumItem"

[Enum.Platform.None]
struct = "EnumItem"

[Enum.Platform.OSX]
struct = "EnumItem"

[Enum.Platform.Ouya]
struct = "EnumItem"

[Enum.Platform.PS3]
struct = "EnumItem"

[Enum.Platform.PS4]
struct = "EnumItem"

[Enum.Platform.SteamOS]
struct = "EnumItem"

[Enum.Platform.UWP]
struct = "EnumItem"

[Enum.Platform.WebOS]
struct = "EnumItem"

[Enum.Platform.WiiU]
struct = "EnumItem"

[Enum.Platform.Windows]
struct = "EnumItem"

[Enum.Platform.XBox360]
struct = "EnumItem"

[Enum.Platform.XBoxOne]
struct = "EnumItem"
[Enum.PlaybackState.Begin]
struct = "EnumItem"

[Enum.PlaybackState.Cancelled]
struct = "EnumItem"

[Enum.PlaybackState.Completed]
struct = "EnumItem"

[Enum.PlaybackState.Delayed]
struct = "EnumItem"

[Enum.PlaybackState.GetEnumItems]
method = true
args = []

[Enum.PlaybackState.Paused]
struct = "EnumItem"

[Enum.PlaybackState.Playing]
struct = "EnumItem"
[Enum.PlayerActions.CharacterBackward]
struct = "EnumItem"

[Enum.PlayerActions.CharacterForward]
struct = "EnumItem"

[Enum.PlayerActions.CharacterJump]
struct = "EnumItem"

[Enum.PlayerActions.CharacterLeft]
struct = "EnumItem"

[Enum.PlayerActions.CharacterRight]
struct = "EnumItem"

[Enum.PlayerActions.GetEnumItems]
method = true
args = []
[Enum.PlayerChatType.All]
struct = "EnumItem"

[Enum.PlayerChatType.GetEnumItems]
method = true
args = []

[Enum.PlayerChatType.Team]
struct = "EnumItem"

[Enum.PlayerChatType.Whisper]
struct = "EnumItem"
[Enum.PoseEasingDirection.GetEnumItems]
method = true
args = []

[Enum.PoseEasingDirection.In]
struct = "EnumItem"

[Enum.PoseEasingDirection.InOut]
struct = "EnumItem"

[Enum.PoseEasingDirection.Out]
struct = "EnumItem"
[Enum.PoseEasingStyle.Bounce]
struct = "EnumItem"

[Enum.PoseEasingStyle.Constant]
struct = "EnumItem"

[Enum.PoseEasingStyle.Cubic]
struct = "EnumItem"

[Enum.PoseEasingStyle.Elastic]
struct = "EnumItem"

[Enum.PoseEasingStyle.GetEnumItems]
method = true
args = []

[Enum.PoseEasingStyle.Linear]
struct = "EnumItem"
[Enum.PositionAlignmentMode.GetEnumItems]
method = true
args = []

[Enum.PositionAlignmentMode.OneAttachment]
struct = "EnumItem"

[Enum.PositionAlignmentMode.TwoAttachment]
struct = "EnumItem"
[Enum.PrivilegeType.Admin]
struct = "EnumItem"

[Enum.PrivilegeType.Banned]
struct = "EnumItem"

[Enum.PrivilegeType.GetEnumItems]
method = true
args = []

[Enum.PrivilegeType.Member]
struct = "EnumItem"

[Enum.PrivilegeType.Owner]
struct = "EnumItem"

[Enum.PrivilegeType.Visitor]
struct = "EnumItem"
[Enum.ProductLocationRestriction.AllGames]
struct = "EnumItem"

[Enum.ProductLocationRestriction.AllowedGames]
struct = "EnumItem"

[Enum.ProductLocationRestriction.AvatarShop]
struct = "EnumItem"

[Enum.ProductLocationRestriction.GetEnumItems]
method = true
args = []
[Enum.ProductPurchaseDecision.GetEnumItems]
method = true
args = []

[Enum.ProductPurchaseDecision.NotProcessedYet]
struct = "EnumItem"

[Enum.ProductPurchaseDecision.PurchaseGranted]
struct = "EnumItem"
[Enum.ProximityPromptExclusivity.AlwaysShow]
struct = "EnumItem"

[Enum.ProximityPromptExclusivity.GetEnumItems]
method = true
args = []

[Enum.ProximityPromptExclusivity.OneGlobally]
struct = "EnumItem"

[Enum.ProximityPromptExclusivity.OnePerButton]
struct = "EnumItem"
[Enum.ProximityPromptInputType.Gamepad]
struct = "EnumItem"

[Enum.ProximityPromptInputType.GetEnumItems]
method = true
args = []

[Enum.ProximityPromptInputType.Keyboard]
struct = "EnumItem"

[Enum.ProximityPromptInputType.Touch]
struct = "EnumItem"
[Enum.ProximityPromptStyle.Custom]
struct = "EnumItem"

[Enum.ProximityPromptStyle.Default]
struct = "EnumItem"

[Enum.ProximityPromptStyle.GetEnumItems]
method = true
args = []
[Enum.QualityLevel.Automatic]
struct = "EnumItem"

[Enum.QualityLevel.GetEnumItems]
method = true
args = []

[Enum.QualityLevel.Level01]
struct = "EnumItem"

[Enum.QualityLevel.Level02]
struct = "EnumItem"

[Enum.QualityLevel.Level03]
struct = "EnumItem"

[Enum.QualityLevel.Level04]
struct = "EnumItem"

[Enum.QualityLevel.Level05]
struct = "EnumItem"

[Enum.QualityLevel.Level06]
struct = "EnumItem"

[Enum.QualityLevel.Level07]
struct = "EnumItem"

[Enum.QualityLevel.Level08]
struct = "EnumItem"

[Enum.QualityLevel.Level09]
struct = "EnumItem"

[Enum.QualityLevel.Level10]
struct = "EnumItem"

[Enum.QualityLevel.Level11]
struct = "EnumItem"

[Enum.QualityLevel.Level12]
struct = "EnumItem"

[Enum.QualityLevel.Level13]
struct = "EnumItem"

[Enum.QualityLevel.Level14]
struct = "EnumItem"

[Enum.QualityLevel.Level15]
struct = "EnumItem"

[Enum.QualityLevel.Level16]
struct = "EnumItem"

[Enum.QualityLevel.Level17]
struct = "EnumItem"

[Enum.QualityLevel.Level18]
struct = "EnumItem"

[Enum.QualityLevel.Level19]
struct = "EnumItem"

[Enum.QualityLevel.Level20]
struct = "EnumItem"

[Enum.QualityLevel.Level21]
struct = "EnumItem"
[Enum.R15CollisionType.GetEnumItems]
method = true
args = []

[Enum.R15CollisionType.InnerBox]
struct = "EnumItem"

[Enum.R15CollisionType.OuterBox]
struct = "EnumItem"
[Enum.RaycastFilterType.Blacklist]
struct = "EnumItem"

[Enum.RaycastFilterType.GetEnumItems]
method = true
args = []

[Enum.RaycastFilterType.Whitelist]
struct = "EnumItem"
[Enum.RenderFidelity.Automatic]
struct = "EnumItem"

[Enum.RenderFidelity.GetEnumItems]
method = true
args = []

[Enum.RenderFidelity.Performance]
struct = "EnumItem"

[Enum.RenderFidelity.Precise]
struct = "EnumItem"
[Enum.RenderPriority.Camera]
struct = "EnumItem"

[Enum.RenderPriority.Character]
struct = "EnumItem"

[Enum.RenderPriority.First]
struct = "EnumItem"

[Enum.RenderPriority.GetEnumItems]
method = true
args = []

[Enum.RenderPriority.Input]
struct = "EnumItem"

[Enum.RenderPriority.Last]
struct = "EnumItem"
[Enum.RenderingTestComparisonMethod.GetEnumItems]
method = true
args = []

[Enum.RenderingTestComparisonMethod.diff]
struct = "EnumItem"

[Enum.RenderingTestComparisonMethod.psnr]
struct = "EnumItem"
[Enum.ResamplerMode.Default]
struct = "EnumItem"

[Enum.ResamplerMode.GetEnumItems]
method = true
args = []

[Enum.ResamplerMode.Pixelated]
struct = "EnumItem"
[Enum.ReturnKeyType.Default]
struct = "EnumItem"

[Enum.ReturnKeyType.Done]
struct = "EnumItem"

[Enum.ReturnKeyType.GetEnumItems]
method = true
args = []

[Enum.ReturnKeyType.Go]
struct = "EnumItem"

[Enum.ReturnKeyType.Next]
struct = "EnumItem"

[Enum.ReturnKeyType.Search]
struct = "EnumItem"

[Enum.ReturnKeyType.Send]
struct = "EnumItem"
[Enum.ReverbType.Alley]
struct = "EnumItem"

[Enum.ReverbType.Arena]
struct = "EnumItem"

[Enum.ReverbType.Auditorium]
struct = "EnumItem"

[Enum.ReverbType.Bathroom]
struct = "EnumItem"

[Enum.ReverbType.CarpettedHallway]
struct = "EnumItem"

[Enum.ReverbType.Cave]
struct = "EnumItem"

[Enum.ReverbType.City]
struct = "EnumItem"

[Enum.ReverbType.ConcertHall]
struct = "EnumItem"

[Enum.ReverbType.Forest]
struct = "EnumItem"

[Enum.ReverbType.GenericReverb]
struct = "EnumItem"

[Enum.ReverbType.GetEnumItems]
method = true
args = []

[Enum.ReverbType.Hallway]
struct = "EnumItem"

[Enum.ReverbType.Hangar]
struct = "EnumItem"

[Enum.ReverbType.LivingRoom]
struct = "EnumItem"

[Enum.ReverbType.Mountains]
struct = "EnumItem"

[Enum.ReverbType.NoReverb]
struct = "EnumItem"

[Enum.ReverbType.PaddedCell]
struct = "EnumItem"

[Enum.ReverbType.ParkingLot]
struct = "EnumItem"

[Enum.ReverbType.Plain]
struct = "EnumItem"

[Enum.ReverbType.Quarry]
struct = "EnumItem"

[Enum.ReverbType.Room]
struct = "EnumItem"

[Enum.ReverbType.SewerPipe]
struct = "EnumItem"

[Enum.ReverbType.StoneCorridor]
struct = "EnumItem"

[Enum.ReverbType.StoneRoom]
struct = "EnumItem"

[Enum.ReverbType.UnderWater]
struct = "EnumItem"
[Enum.RibbonTool.ColorPicker]
struct = "EnumItem"

[Enum.RibbonTool.GetEnumItems]
method = true
args = []

[Enum.RibbonTool.Group]
struct = "EnumItem"

[Enum.RibbonTool.MaterialPicker]
struct = "EnumItem"

[Enum.RibbonTool.Move]
struct = "EnumItem"

[Enum.RibbonTool.None]
struct = "EnumItem"

[Enum.RibbonTool.Rotate]
struct = "EnumItem"

[Enum.RibbonTool.Scale]
struct = "EnumItem"

[Enum.RibbonTool.Select]
struct = "EnumItem"

[Enum.RibbonTool.Transform]
struct = "EnumItem"

[Enum.RibbonTool.Ungroup]
struct = "EnumItem"
[Enum.RigType.Custom]
struct = "EnumItem"

[Enum.RigType.GetEnumItems]
method = true
args = []

[Enum.RigType.None]
struct = "EnumItem"

[Enum.RigType.R15]
struct = "EnumItem"

[Enum.RigType.Rthro]
struct = "EnumItem"

[Enum.RigType.RthroNarrow]
struct = "EnumItem"
[Enum.RollOffMode.GetEnumItems]
method = true
args = []

[Enum.RollOffMode.Inverse]
struct = "EnumItem"

[Enum.RollOffMode.InverseTapered]
struct = "EnumItem"

[Enum.RollOffMode.Linear]
struct = "EnumItem"

[Enum.RollOffMode.LinearSquare]
struct = "EnumItem"
[Enum.RotationOrder.GetEnumItems]
method = true
args = []

[Enum.RotationOrder.XYZ]
struct = "EnumItem"

[Enum.RotationOrder.XZY]
struct = "EnumItem"

[Enum.RotationOrder.YXZ]
struct = "EnumItem"

[Enum.RotationOrder.YZX]
struct = "EnumItem"

[Enum.RotationOrder.ZXY]
struct = "EnumItem"

[Enum.RotationOrder.ZYX]
struct = "EnumItem"
[Enum.RotationType.CameraRelative]
struct = "EnumItem"

[Enum.RotationType.GetEnumItems]
method = true
args = []

[Enum.RotationType.MovementRelative]
struct = "EnumItem"
[Enum.RuntimeUndoBehavior.Aggregate]
struct = "EnumItem"

[Enum.RuntimeUndoBehavior.GetEnumItems]
method = true
args = []

[Enum.RuntimeUndoBehavior.Hybrid]
struct = "EnumItem"

[Enum.RuntimeUndoBehavior.Snapshot]
struct = "EnumItem"
[Enum.SaveFilter.GetEnumItems]
method = true
args = []

[Enum.SaveFilter.SaveAll]
struct = "EnumItem"

[Enum.SaveFilter.SaveGame]
struct = "EnumItem"

[Enum.SaveFilter.SaveWorld]
struct = "EnumItem"
[Enum.SavedQualitySetting.Automatic]
struct = "EnumItem"

[Enum.SavedQualitySetting.GetEnumItems]
method = true
args = []

[Enum.SavedQualitySetting.QualityLevel1]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel10]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel2]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel3]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel4]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel5]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel6]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel7]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel8]
struct = "EnumItem"

[Enum.SavedQualitySetting.QualityLevel9]
struct = "EnumItem"
[Enum.ScaleType.Crop]
struct = "EnumItem"

[Enum.ScaleType.Fit]
struct = "EnumItem"

[Enum.ScaleType.GetEnumItems]
method = true
args = []

[Enum.ScaleType.Slice]
struct = "EnumItem"

[Enum.ScaleType.Stretch]
struct = "EnumItem"

[Enum.ScaleType.Tile]
struct = "EnumItem"
[Enum.ScreenOrientation.GetEnumItems]
method = true
args = []

[Enum.ScreenOrientation.LandscapeLeft]
struct = "EnumItem"

[Enum.ScreenOrientation.LandscapeRight]
struct = "EnumItem"

[Enum.ScreenOrientation.LandscapeSensor]
struct = "EnumItem"

[Enum.ScreenOrientation.Portrait]
struct = "EnumItem"

[Enum.ScreenOrientation.Sensor]
struct = "EnumItem"
[Enum.ScrollBarInset.Always]
struct = "EnumItem"

[Enum.ScrollBarInset.GetEnumItems]
method = true
args = []

[Enum.ScrollBarInset.None]
struct = "EnumItem"

[Enum.ScrollBarInset.ScrollBar]
struct = "EnumItem"
[Enum.ScrollingDirection.GetEnumItems]
method = true
args = []

[Enum.ScrollingDirection.X]
struct = "EnumItem"

[Enum.ScrollingDirection.XY]
struct = "EnumItem"

[Enum.ScrollingDirection.Y]
struct = "EnumItem"
[Enum.ServerAudioBehavior.Enabled]
struct = "EnumItem"

[Enum.ServerAudioBehavior.GetEnumItems]
method = true
args = []

[Enum.ServerAudioBehavior.Muted]
struct = "EnumItem"

[Enum.ServerAudioBehavior.OnlineGame]
struct = "EnumItem"
[Enum.SignalBehavior.Default]
struct = "EnumItem"

[Enum.SignalBehavior.Deferred]
struct = "EnumItem"

[Enum.SignalBehavior.GetEnumItems]
method = true
args = []

[Enum.SignalBehavior.Immediate]
struct = "EnumItem"
[Enum.SizeConstraint.GetEnumItems]
method = true
args = []

[Enum.SizeConstraint.RelativeXX]
struct = "EnumItem"

[Enum.SizeConstraint.RelativeXY]
struct = "EnumItem"

[Enum.SizeConstraint.RelativeYY]
struct = "EnumItem"
[Enum.SortDirection.Ascending]
struct = "EnumItem"

[Enum.SortDirection.Descending]
struct = "EnumItem"

[Enum.SortDirection.GetEnumItems]
method = true
args = []
[Enum.SortOrder.Custom]
struct = "EnumItem"

[Enum.SortOrder.GetEnumItems]
method = true
args = []

[Enum.SortOrder.LayoutOrder]
struct = "EnumItem"

[Enum.SortOrder.Name]
struct = "EnumItem"
[Enum.SoundType.Boing]
struct = "EnumItem"

[Enum.SoundType.Bomb]
struct = "EnumItem"

[Enum.SoundType.Break]
struct = "EnumItem"

[Enum.SoundType.Click]
struct = "EnumItem"

[Enum.SoundType.Clock]
struct = "EnumItem"

[Enum.SoundType.GetEnumItems]
method = true
args = []

[Enum.SoundType.NoSound]
struct = "EnumItem"

[Enum.SoundType.Page]
struct = "EnumItem"

[Enum.SoundType.Ping]
struct = "EnumItem"

[Enum.SoundType.Slingshot]
struct = "EnumItem"

[Enum.SoundType.Snap]
struct = "EnumItem"

[Enum.SoundType.Splat]
struct = "EnumItem"

[Enum.SoundType.Step]
struct = "EnumItem"

[Enum.SoundType.StepOn]
struct = "EnumItem"

[Enum.SoundType.Swoosh]
struct = "EnumItem"

[Enum.SoundType.Victory]
struct = "EnumItem"
[Enum.SpecialKey.ChatHotkey]
struct = "EnumItem"

[Enum.SpecialKey.End]
struct = "EnumItem"

[Enum.SpecialKey.GetEnumItems]
method = true
args = []

[Enum.SpecialKey.Home]
struct = "EnumItem"

[Enum.SpecialKey.Insert]
struct = "EnumItem"

[Enum.SpecialKey.PageDown]
struct = "EnumItem"

[Enum.SpecialKey.PageUp]
struct = "EnumItem"
[Enum.StartCorner.BottomLeft]
struct = "EnumItem"

[Enum.StartCorner.BottomRight]
struct = "EnumItem"

[Enum.StartCorner.GetEnumItems]
method = true
args = []

[Enum.StartCorner.TopLeft]
struct = "EnumItem"

[Enum.StartCorner.TopRight]
struct = "EnumItem"
[Enum.Status.Confusion]
struct = "EnumItem"

[Enum.Status.GetEnumItems]
method = true
args = []

[Enum.Status.Poison]
struct = "EnumItem"
[Enum.StreamOutBehavior.Default]
struct = "EnumItem"

[Enum.StreamOutBehavior.GetEnumItems]
method = true
args = []

[Enum.StreamOutBehavior.LowMemory]
struct = "EnumItem"

[Enum.StreamOutBehavior.Opportunistic]
struct = "EnumItem"
[Enum.StreamingPauseMode.ClientPhysicsPause]
struct = "EnumItem"

[Enum.StreamingPauseMode.Default]
struct = "EnumItem"

[Enum.StreamingPauseMode.Disabled]
struct = "EnumItem"

[Enum.StreamingPauseMode.GetEnumItems]
method = true
args = []
[Enum.StudioCloseMode.CloseDoc]
struct = "EnumItem"

[Enum.StudioCloseMode.CloseStudio]
struct = "EnumItem"

[Enum.StudioCloseMode.GetEnumItems]
method = true
args = []

[Enum.StudioCloseMode.None]
struct = "EnumItem"
[Enum.StudioDataModelType.Edit]
struct = "EnumItem"

[Enum.StudioDataModelType.GetEnumItems]
method = true
args = []

[Enum.StudioDataModelType.None]
struct = "EnumItem"

[Enum.StudioDataModelType.PlayClient]
struct = "EnumItem"

[Enum.StudioDataModelType.PlayServer]
struct = "EnumItem"

[Enum.StudioDataModelType.RobloxPlugin]
struct = "EnumItem"

[Enum.StudioDataModelType.UserPlugin]
struct = "EnumItem"
[Enum.StudioScriptEditorColorCategories.ActiveLine]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Background]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Bool]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Bracket]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Builtin]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Comment]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.DebuggerCurrentLine]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.DebuggerErrorLine]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Default]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Error]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.FindSelectionBackground]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Function]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.FunctionName]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.GetEnumItems]
method = true
args = []

[Enum.StudioScriptEditorColorCategories.Keyword]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Local]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.LuauKeyword]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MatchingWordBackground]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuBackground]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuBorder]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuPrimaryText]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuScrollbarBackground]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuScrollbarHandle]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuSecondaryText]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuSelectedBackground]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.MenuSelectedText]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Method]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Nil]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Number]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Operator]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Property]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Ruler]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.SelectionBackground]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.SelectionText]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Self]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.String]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.TODO]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Warning]
struct = "EnumItem"

[Enum.StudioScriptEditorColorCategories.Whitespace]
struct = "EnumItem"
[Enum.StudioScriptEditorColorPresets.Custom]
struct = "EnumItem"

[Enum.StudioScriptEditorColorPresets.Extra1]
struct = "EnumItem"

[Enum.StudioScriptEditorColorPresets.Extra2]
struct = "EnumItem"

[Enum.StudioScriptEditorColorPresets.GetEnumItems]
method = true
args = []

[Enum.StudioScriptEditorColorPresets.RobloxDefault]
struct = "EnumItem"
[Enum.StudioStyleGuideColor.AttributeCog]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Border]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.BrightText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Button]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ButtonBorder]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ButtonText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.CategoryItem]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ChatIncomingBgColor]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ChatIncomingTextColor]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ChatModeratedMessageColor]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ChatOutgoingBgColor]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ChatOutgoingTextColor]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.CheckedFieldBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.CheckedFieldBorder]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.CheckedFieldIndicator]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ColorPickerFrame]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.CurrentMarker]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Dark]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DebuggerCurrentLine]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DebuggerErrorLine]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DialogButton]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DialogButtonBorder]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DialogButtonText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DialogMainButton]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DialogMainButtonText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffFilePathBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffFilePathBorder]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffFilePathText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffLineNum]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffLineNumAdditionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffLineNumDeletionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffLineNumNoChangeBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffLineNumSeparatorBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextAddition]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextAdditionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextDeletion]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextDeletionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextHunkInfo]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextNoChange]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextNoChangeBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DiffTextSeparatorBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.DimmedText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Dropdown]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.EmulatorBar]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.EmulatorDropDown]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ErrorText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.FilterButtonAccent]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.FilterButtonBorder]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.FilterButtonBorderAlt]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.FilterButtonChecked]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.FilterButtonDefault]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.FilterButtonHover]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.GameSettingsTableItem]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.GameSettingsTooltip]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.GetEnumItems]
method = true
args = []

[Enum.StudioStyleGuideColor.HeaderSection]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.InfoBarWarningBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.InfoBarWarningText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.InfoText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.InputFieldBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.InputFieldBorder]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Item]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Light]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.LinkText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.MainBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.MainButton]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.MainText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Mid]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Midlight]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Notification]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.RibbonButton]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.RibbonTab]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.RibbonTabTopBar]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptBool]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptBracket]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptBuiltInFunction]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptComment]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptEditorCurrentLine]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptError]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptFindSelectionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptFunction]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptFunctionName]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptKeyword]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptLocal]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptLuauKeyword]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptMatchingWordSelectionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptMethod]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptNil]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptNumber]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptOperator]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptProperty]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptRuler]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptSelectionBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptSelectionText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptSelf]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptSideWidget]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptString]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptTodo]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptWarning]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScriptWhitespace]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScrollBar]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ScrollBarBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.SensitiveText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Separator]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Shadow]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.StatusBar]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.SubText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Tab]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.TabBar]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.TableItem]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Titlebar]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.TitlebarText]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.Tooltip]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.ViewPortBackground]
struct = "EnumItem"

[Enum.StudioStyleGuideColor.WarningText]
struct = "EnumItem"
[Enum.StudioStyleGuideModifier.Default]
struct = "EnumItem"

[Enum.StudioStyleGuideModifier.Disabled]
struct = "EnumItem"

[Enum.StudioStyleGuideModifier.GetEnumItems]
method = true
args = []

[Enum.StudioStyleGuideModifier.Hover]
struct = "EnumItem"

[Enum.StudioStyleGuideModifier.Pressed]
struct = "EnumItem"

[Enum.StudioStyleGuideModifier.Selected]
struct = "EnumItem"
[Enum.Style.AlternatingSupports]
struct = "EnumItem"

[Enum.Style.BridgeStyleSupports]
struct = "EnumItem"

[Enum.Style.GetEnumItems]
method = true
args = []

[Enum.Style.NoSupports]
struct = "EnumItem"
[Enum.SurfaceConstraint.GetEnumItems]
method = true
args = []

[Enum.SurfaceConstraint.Hinge]
struct = "EnumItem"

[Enum.SurfaceConstraint.Motor]
struct = "EnumItem"

[Enum.SurfaceConstraint.None]
struct = "EnumItem"

[Enum.SurfaceConstraint.SteppingMotor]
struct = "EnumItem"
[Enum.SurfaceGuiSizingMode.FixedSize]
struct = "EnumItem"

[Enum.SurfaceGuiSizingMode.GetEnumItems]
method = true
args = []

[Enum.SurfaceGuiSizingMode.PixelsPerStud]
struct = "EnumItem"
[Enum.SurfaceType.GetEnumItems]
method = true
args = []

[Enum.SurfaceType.Glue]
struct = "EnumItem"

[Enum.SurfaceType.Hinge]
struct = "EnumItem"

[Enum.SurfaceType.Inlet]
struct = "EnumItem"

[Enum.SurfaceType.Motor]
struct = "EnumItem"

[Enum.SurfaceType.Smooth]
struct = "EnumItem"

[Enum.SurfaceType.SmoothNoOutlines]
struct = "EnumItem"

[Enum.SurfaceType.SteppingMotor]
struct = "EnumItem"

[Enum.SurfaceType.Studs]
struct = "EnumItem"

[Enum.SurfaceType.Universal]
struct = "EnumItem"

[Enum.SurfaceType.Weld]
struct = "EnumItem"
[Enum.SwipeDirection.Down]
struct = "EnumItem"

[Enum.SwipeDirection.GetEnumItems]
method = true
args = []

[Enum.SwipeDirection.Left]
struct = "EnumItem"

[Enum.SwipeDirection.None]
struct = "EnumItem"

[Enum.SwipeDirection.Right]
struct = "EnumItem"

[Enum.SwipeDirection.Up]
struct = "EnumItem"
[Enum.TableMajorAxis.ColumnMajor]
struct = "EnumItem"

[Enum.TableMajorAxis.GetEnumItems]
method = true
args = []

[Enum.TableMajorAxis.RowMajor]
struct = "EnumItem"
[Enum.Technology.Compatibility]
struct = "EnumItem"

[Enum.Technology.Future]
struct = "EnumItem"

[Enum.Technology.GetEnumItems]
method = true
args = []

[Enum.Technology.Legacy]
struct = "EnumItem"

[Enum.Technology.ShadowMap]
struct = "EnumItem"

[Enum.Technology.Voxel]
struct = "EnumItem"
[Enum.TeleportMethod.GetEnumItems]
method = true
args = []

[Enum.TeleportMethod.TeleportPartyAsync]
struct = "EnumItem"

[Enum.TeleportMethod.TeleportToPlaceInstance]
struct = "EnumItem"

[Enum.TeleportMethod.TeleportToPrivateServer]
struct = "EnumItem"

[Enum.TeleportMethod.TeleportToSpawnByName]
struct = "EnumItem"

[Enum.TeleportMethod.TeleportUnknown]
struct = "EnumItem"
[Enum.TeleportResult.Failure]
struct = "EnumItem"

[Enum.TeleportResult.Flooded]
struct = "EnumItem"

[Enum.TeleportResult.GameEnded]
struct = "EnumItem"

[Enum.TeleportResult.GameFull]
struct = "EnumItem"

[Enum.TeleportResult.GameNotFound]
struct = "EnumItem"

[Enum.TeleportResult.GetEnumItems]
method = true
args = []

[Enum.TeleportResult.IsTeleporting]
struct = "EnumItem"

[Enum.TeleportResult.Success]
struct = "EnumItem"

[Enum.TeleportResult.Unauthorized]
struct = "EnumItem"
[Enum.TeleportState.Failed]
struct = "EnumItem"

[Enum.TeleportState.GetEnumItems]
method = true
args = []

[Enum.TeleportState.InProgress]
struct = "EnumItem"

[Enum.TeleportState.RequestedFromServer]
struct = "EnumItem"

[Enum.TeleportState.Started]
struct = "EnumItem"

[Enum.TeleportState.WaitingForServer]
struct = "EnumItem"
[Enum.TeleportType.GetEnumItems]
method = true
args = []

[Enum.TeleportType.ToInstance]
struct = "EnumItem"

[Enum.TeleportType.ToPlace]
struct = "EnumItem"

[Enum.TeleportType.ToReservedServer]
struct = "EnumItem"
[Enum.TerrainAcquisitionMethod.Convert]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.EditAddTool]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.EditReplaceTool]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.EditSeaLevelTool]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.Generate]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.GetEnumItems]
method = true
args = []

[Enum.TerrainAcquisitionMethod.Import]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.Legacy]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.None]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.Other]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.RegionFillTool]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.RegionPasteTool]
struct = "EnumItem"

[Enum.TerrainAcquisitionMethod.Template]
struct = "EnumItem"
[Enum.TextChatMessageStatus.Floodchecked]
struct = "EnumItem"

[Enum.TextChatMessageStatus.GetEnumItems]
method = true
args = []

[Enum.TextChatMessageStatus.InvalidPrivacySettings]
struct = "EnumItem"

[Enum.TextChatMessageStatus.InvalidTextChannelPermissions]
struct = "EnumItem"

[Enum.TextChatMessageStatus.Success]
struct = "EnumItem"

[Enum.TextChatMessageStatus.TextFilterFailed]
struct = "EnumItem"
[Enum.TextFilterContext.GetEnumItems]
method = true
args = []

[Enum.TextFilterContext.PrivateChat]
struct = "EnumItem"

[Enum.TextFilterContext.PublicChat]
struct = "EnumItem"
[Enum.TextInputType.Default]
struct = "EnumItem"

[Enum.TextInputType.Email]
struct = "EnumItem"

[Enum.TextInputType.GetEnumItems]
method = true
args = []

[Enum.TextInputType.NoSuggestions]
struct = "EnumItem"

[Enum.TextInputType.Number]
struct = "EnumItem"

[Enum.TextInputType.OneTimePassword]
struct = "EnumItem"

[Enum.TextInputType.Password]
struct = "EnumItem"

[Enum.TextInputType.PasswordShown]
struct = "EnumItem"

[Enum.TextInputType.Phone]
struct = "EnumItem"

[Enum.TextInputType.Username]
struct = "EnumItem"
[Enum.TextTruncate.AtEnd]
struct = "EnumItem"

[Enum.TextTruncate.GetEnumItems]
method = true
args = []

[Enum.TextTruncate.None]
struct = "EnumItem"
[Enum.TextXAlignment.Center]
struct = "EnumItem"

[Enum.TextXAlignment.GetEnumItems]
method = true
args = []

[Enum.TextXAlignment.Left]
struct = "EnumItem"

[Enum.TextXAlignment.Right]
struct = "EnumItem"
[Enum.TextYAlignment.Bottom]
struct = "EnumItem"

[Enum.TextYAlignment.Center]
struct = "EnumItem"

[Enum.TextYAlignment.GetEnumItems]
method = true
args = []

[Enum.TextYAlignment.Top]
struct = "EnumItem"
[Enum.TextureMode.GetEnumItems]
method = true
args = []

[Enum.TextureMode.Static]
struct = "EnumItem"

[Enum.TextureMode.Stretch]
struct = "EnumItem"

[Enum.TextureMode.Wrap]
struct = "EnumItem"
[Enum.TextureQueryType.GetEnumItems]
method = true
args = []

[Enum.TextureQueryType.Humanoid]
struct = "EnumItem"

[Enum.TextureQueryType.HumanoidOrphaned]
struct = "EnumItem"

[Enum.TextureQueryType.NonHumanoid]
struct = "EnumItem"

[Enum.TextureQueryType.NonHumanoidOrphaned]
struct = "EnumItem"
[Enum.ThreadPoolConfig.Auto]
struct = "EnumItem"

[Enum.ThreadPoolConfig.GetEnumItems]
method = true
args = []

[Enum.ThreadPoolConfig.PerCore1]
struct = "EnumItem"

[Enum.ThreadPoolConfig.PerCore2]
struct = "EnumItem"

[Enum.ThreadPoolConfig.PerCore3]
struct = "EnumItem"

[Enum.ThreadPoolConfig.PerCore4]
struct = "EnumItem"

[Enum.ThreadPoolConfig.Threads1]
struct = "EnumItem"

[Enum.ThreadPoolConfig.Threads16]
struct = "EnumItem"

[Enum.ThreadPoolConfig.Threads2]
struct = "EnumItem"

[Enum.ThreadPoolConfig.Threads3]
struct = "EnumItem"

[Enum.ThreadPoolConfig.Threads4]
struct = "EnumItem"

[Enum.ThreadPoolConfig.Threads8]
struct = "EnumItem"
[Enum.ThrottlingPriority.Default]
struct = "EnumItem"

[Enum.ThrottlingPriority.ElevatedOnServer]
struct = "EnumItem"

[Enum.ThrottlingPriority.Extreme]
struct = "EnumItem"

[Enum.ThrottlingPriority.GetEnumItems]
method = true
args = []
[Enum.ThumbnailSize.GetEnumItems]
method = true
args = []

[Enum.ThumbnailSize.Size100x100]
struct = "EnumItem"

[Enum.ThumbnailSize.Size150x150]
struct = "EnumItem"

[Enum.ThumbnailSize.Size180x180]
struct = "EnumItem"

[Enum.ThumbnailSize.Size352x352]
struct = "EnumItem"

[Enum.ThumbnailSize.Size420x420]
struct = "EnumItem"

[Enum.ThumbnailSize.Size48x48]
struct = "EnumItem"

[Enum.ThumbnailSize.Size60x60]
struct = "EnumItem"
[Enum.ThumbnailType.AvatarBust]
struct = "EnumItem"

[Enum.ThumbnailType.AvatarThumbnail]
struct = "EnumItem"

[Enum.ThumbnailType.GetEnumItems]
method = true
args = []

[Enum.ThumbnailType.HeadShot]
struct = "EnumItem"
[Enum.TickCountSampleMethod.Benchmark]
struct = "EnumItem"

[Enum.TickCountSampleMethod.Fast]
struct = "EnumItem"

[Enum.TickCountSampleMethod.GetEnumItems]
method = true
args = []

[Enum.TickCountSampleMethod.Precise]
struct = "EnumItem"
[Enum.TopBottom.Bottom]
struct = "EnumItem"

[Enum.TopBottom.Center]
struct = "EnumItem"

[Enum.TopBottom.GetEnumItems]
method = true
args = []

[Enum.TopBottom.Top]
struct = "EnumItem"
[Enum.TouchCameraMovementMode.Classic]
struct = "EnumItem"

[Enum.TouchCameraMovementMode.Default]
struct = "EnumItem"

[Enum.TouchCameraMovementMode.Follow]
struct = "EnumItem"

[Enum.TouchCameraMovementMode.GetEnumItems]
method = true
args = []

[Enum.TouchCameraMovementMode.Orbital]
struct = "EnumItem"
[Enum.TouchMovementMode.ClickToMove]
struct = "EnumItem"

[Enum.TouchMovementMode.DPad]
struct = "EnumItem"

[Enum.TouchMovementMode.Default]
struct = "EnumItem"

[Enum.TouchMovementMode.DynamicThumbstick]
struct = "EnumItem"

[Enum.TouchMovementMode.GetEnumItems]
method = true
args = []

[Enum.TouchMovementMode.Thumbpad]
struct = "EnumItem"

[Enum.TouchMovementMode.Thumbstick]
struct = "EnumItem"
[Enum.TriStateBoolean.False]
struct = "EnumItem"

[Enum.TriStateBoolean.GetEnumItems]
method = true
args = []

[Enum.TriStateBoolean.True]
struct = "EnumItem"

[Enum.TriStateBoolean.Unknown]
struct = "EnumItem"
[Enum.TweenStatus.Canceled]
struct = "EnumItem"

[Enum.TweenStatus.Completed]
struct = "EnumItem"

[Enum.TweenStatus.GetEnumItems]
method = true
args = []
[Enum.UITheme.Dark]
struct = "EnumItem"

[Enum.UITheme.GetEnumItems]
method = true
args = []

[Enum.UITheme.Light]
struct = "EnumItem"
[Enum.UiMessageType.GetEnumItems]
method = true
args = []

[Enum.UiMessageType.UiMessageError]
struct = "EnumItem"

[Enum.UiMessageType.UiMessageInfo]
struct = "EnumItem"
[Enum.UserCFrame.GetEnumItems]
method = true
args = []

[Enum.UserCFrame.Head]
struct = "EnumItem"

[Enum.UserCFrame.LeftHand]
struct = "EnumItem"

[Enum.UserCFrame.RightHand]
struct = "EnumItem"
[Enum.UserInputState.Begin]
struct = "EnumItem"

[Enum.UserInputState.Cancel]
struct = "EnumItem"

[Enum.UserInputState.Change]
struct = "EnumItem"

[Enum.UserInputState.End]
struct = "EnumItem"

[Enum.UserInputState.GetEnumItems]
method = true
args = []

[Enum.UserInputState.None]
struct = "EnumItem"
[Enum.UserInputType.Accelerometer]
struct = "EnumItem"

[Enum.UserInputType.Focus]
struct = "EnumItem"

[Enum.UserInputType.Gamepad1]
struct = "EnumItem"

[Enum.UserInputType.Gamepad2]
struct = "EnumItem"

[Enum.UserInputType.Gamepad3]
struct = "EnumItem"

[Enum.UserInputType.Gamepad4]
struct = "EnumItem"

[Enum.UserInputType.Gamepad5]
struct = "EnumItem"

[Enum.UserInputType.Gamepad6]
struct = "EnumItem"

[Enum.UserInputType.Gamepad7]
struct = "EnumItem"

[Enum.UserInputType.Gamepad8]
struct = "EnumItem"

[Enum.UserInputType.GetEnumItems]
method = true
args = []

[Enum.UserInputType.Gyro]
struct = "EnumItem"

[Enum.UserInputType.InputMethod]
struct = "EnumItem"

[Enum.UserInputType.Keyboard]
struct = "EnumItem"

[Enum.UserInputType.MouseButton1]
struct = "EnumItem"

[Enum.UserInputType.MouseButton2]
struct = "EnumItem"

[Enum.UserInputType.MouseButton3]
struct = "EnumItem"

[Enum.UserInputType.MouseMovement]
struct = "EnumItem"

[Enum.UserInputType.MouseWheel]
struct = "EnumItem"

[Enum.UserInputType.None]
struct = "EnumItem"

[Enum.UserInputType.TextInput]
struct = "EnumItem"

[Enum.UserInputType.Touch]
struct = "EnumItem"
[Enum.VRTouchpad.GetEnumItems]
method = true
args = []

[Enum.VRTouchpad.Left]
struct = "EnumItem"

[Enum.VRTouchpad.Right]
struct = "EnumItem"
[Enum.VRTouchpadMode.ABXY]
struct = "EnumItem"

[Enum.VRTouchpadMode.GetEnumItems]
method = true
args = []

[Enum.VRTouchpadMode.Touch]
struct = "EnumItem"

[Enum.VRTouchpadMode.VirtualThumbstick]
struct = "EnumItem"
[Enum.VelocityConstraintMode.GetEnumItems]
method = true
args = []

[Enum.VelocityConstraintMode.Line]
struct = "EnumItem"

[Enum.VelocityConstraintMode.Plane]
struct = "EnumItem"

[Enum.VelocityConstraintMode.Vector]
struct = "EnumItem"
[Enum.VerticalAlignment.Bottom]
struct = "EnumItem"

[Enum.VerticalAlignment.Center]
struct = "EnumItem"

[Enum.VerticalAlignment.GetEnumItems]
method = true
args = []

[Enum.VerticalAlignment.Top]
struct = "EnumItem"
[Enum.VerticalScrollBarPosition.GetEnumItems]
method = true
args = []

[Enum.VerticalScrollBarPosition.Left]
struct = "EnumItem"

[Enum.VerticalScrollBarPosition.Right]
struct = "EnumItem"
[Enum.VibrationMotor.GetEnumItems]
method = true
args = []

[Enum.VibrationMotor.Large]
struct = "EnumItem"

[Enum.VibrationMotor.LeftHand]
struct = "EnumItem"

[Enum.VibrationMotor.LeftTrigger]
struct = "EnumItem"

[Enum.VibrationMotor.RightHand]
struct = "EnumItem"

[Enum.VibrationMotor.RightTrigger]
struct = "EnumItem"

[Enum.VibrationMotor.Small]
struct = "EnumItem"
[Enum.VirtualCursorMode.Default]
struct = "EnumItem"

[Enum.VirtualCursorMode.Disabled]
struct = "EnumItem"

[Enum.VirtualCursorMode.Enabled]
struct = "EnumItem"

[Enum.VirtualCursorMode.GetEnumItems]
method = true
args = []
[Enum.VirtualInputMode.GetEnumItems]
method = true
args = []

[Enum.VirtualInputMode.None]
struct = "EnumItem"

[Enum.VirtualInputMode.Playing]
struct = "EnumItem"

[Enum.VirtualInputMode.Recording]
struct = "EnumItem"
[Enum.VoiceChatState.Ended]
struct = "EnumItem"

[Enum.VoiceChatState.Failed]
struct = "EnumItem"

[Enum.VoiceChatState.GetEnumItems]
method = true
args = []

[Enum.VoiceChatState.Idle]
struct = "EnumItem"

[Enum.VoiceChatState.Joined]
struct = "EnumItem"

[Enum.VoiceChatState.Joining]
struct = "EnumItem"

[Enum.VoiceChatState.JoiningRetry]
struct = "EnumItem"

[Enum.VoiceChatState.Leaving]
struct = "EnumItem"
[Enum.WaterDirection.GetEnumItems]
method = true
args = []

[Enum.WaterDirection.NegX]
struct = "EnumItem"

[Enum.WaterDirection.NegY]
struct = "EnumItem"

[Enum.WaterDirection.NegZ]
struct = "EnumItem"

[Enum.WaterDirection.X]
struct = "EnumItem"

[Enum.WaterDirection.Y]
struct = "EnumItem"

[Enum.WaterDirection.Z]
struct = "EnumItem"
[Enum.WaterForce.GetEnumItems]
method = true
args = []

[Enum.WaterForce.Max]
struct = "EnumItem"

[Enum.WaterForce.Medium]
struct = "EnumItem"

[Enum.WaterForce.None]
struct = "EnumItem"

[Enum.WaterForce.Small]
struct = "EnumItem"

[Enum.WaterForce.Strong]
struct = "EnumItem"
[Enum.WrapLayerDebugMode.BoundCage]
struct = "EnumItem"

[Enum.WrapLayerDebugMode.BoundCageAndLinks]
struct = "EnumItem"

[Enum.WrapLayerDebugMode.GetEnumItems]
method = true
args = []

[Enum.WrapLayerDebugMode.LayerCage]
struct = "EnumItem"

[Enum.WrapLayerDebugMode.None]
struct = "EnumItem"

[Enum.WrapLayerDebugMode.OuterCage]
struct = "EnumItem"

[Enum.WrapLayerDebugMode.Rbf]
struct = "EnumItem"

[Enum.WrapLayerDebugMode.Reference]
struct = "EnumItem"
[Enum.WrapTargetDebugMode.GetEnumItems]
method = true
args = []

[Enum.WrapTargetDebugMode.None]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.Rbf]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.TargetCageCompressed]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.TargetCageInterface]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.TargetCageOriginal]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.TargetLayerCageCompressed]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.TargetLayerCageOriginal]
struct = "EnumItem"

[Enum.WrapTargetDebugMode.TargetLayerInterface]
struct = "EnumItem"
[Enum.ZIndexBehavior.GetEnumItems]
method = true
args = []

[Enum.ZIndexBehavior.Global]
struct = "EnumItem"

[Enum.ZIndexBehavior.Sibling]
struct = "EnumItem"
[[Faces.new.args]]
type = "..."
[[Instance.new.args]]
type = ["Accoutrement", "Accessory", "Hat", "AdvancedDragger", "AnalyticsService", "Animation", "CurveAnimation", "KeyframeSequence", "AnimationController", "AnimationRigData", "Animator", "Atmosphere", "Attachment", "Bone", "Backpack", "HopperBin", "Tool", "Flag", "WrapLayer", "WrapTarget", "Beam", "BindableEvent", "BindableFunction", "BodyAngularVelocity", "BodyForce", "BodyGyro", "BodyPosition", "BodyThrust", "BodyVelocity", "RocketPropulsion", "Breakpoint", "Camera", "BodyColors", "CharacterMesh", "Pants", "Shirt", "ShirtGraphic", "Skin", "ClickDetector", "Clouds", "Configuration", "AlignOrientation", "AlignPosition", "AngularVelocity", "BallSocketConstraint", "HingeConstraint", "LineForce", "LinearVelocity", "Plane", "RodConstraint", "RopeConstraint", "CylindricalConstraint", "PrismaticConstraint", "SpringConstraint", "Torque", "TorsionSpringConstraint", "UniversalConstraint", "VectorForce", "HumanoidController", "SkateboardController", "VehicleController", "CustomEvent", "CustomEventReceiver", "BlockMesh", "CylinderMesh", "FileMesh", "SpecialMesh", "DataStoreIncrementOptions", "DataStoreOptions", "DataStoreSetOptions", "DebuggerWatch", "Dialog", "DialogChoice", "Dragger", "EulerRotationCurve", "Explosion", "FaceControls", "Decal", "Texture", "Hole", "MotorFeature", "Fire", "FloatCurve", "FlyweightService", "CSGDictionaryService", "NonReplicatedCSGDictionaryService", "Folder", "ForceField", "FunctionalTest", "CanvasGroup", "Frame", "ImageButton", "TextButton", "ImageLabel", "TextLabel", "ScrollingFrame", "TextBox", "VideoFrame", "ViewportFrame", "BillboardGui", "ScreenGui", "GuiMain", "SurfaceGui", "FloorWire", "SelectionBox", "BoxHandleAdornment", "ConeHandleAdornment", "CylinderHandleAdornment", "ImageHandleAdornment", "LineHandleAdornment", "SphereHandleAdornment", "ParabolaAdornment", "SelectionSphere", "ArcHandles", "Handles", "SurfaceSelection", "SelectionPartLasso", "SelectionPointLasso", "HeightmapImporterService", "Highlight", "Humanoid", "HumanoidDescription", "RotateP", "RotateV", "Glue", "ManualGlue", "ManualWeld", "Motor", "Motor6D", "Rotate", "Snap", "VelocityMotor", "Weld", "Keyframe", "KeyframeMarker", "PointLight", "SpotLight", "SurfaceLight", "LocalizationTable", "Script", "LocalScript", "ModuleScript", "MaterialVariant", "MemoryStoreService", "Message", "Hint", "NoCollisionConstraint", "CornerWedgePart", "Part", "FlagStand", "Seat", "SkateboardPlatform", "SpawnLocation", "WedgePart", "MeshPart", "PartOperation", "NegateOperation", "UnionOperation", "TrussPart", "VehicleSeat", "Model", "Actor", "WorldModel", "PartOperationAsset", "ParticleEmitter", "PathfindingLink", "PathfindingModifier", "Player", "PluginAction", "NumberPose", "Pose", "BloomEffect", "BlurEffect", "ColorCorrectionEffect", "DepthOfFieldEffect", "SunRaysEffect", "ProximityPrompt", "ProximityPromptService", "ReflectionMetadata", "ReflectionMetadataCallbacks", "ReflectionMetadataClasses", "ReflectionMetadataEnums", "ReflectionMetadataEvents", "ReflectionMetadataFunctions", "ReflectionMetadataClass", "ReflectionMetadataEnum", "ReflectionMetadataEnumItem", "ReflectionMetadataMember", "ReflectionMetadataProperties", "ReflectionMetadataYieldFunctions", "RemoteEvent", "RemoteFunction", "RenderingTest", "RotationCurve", "Sky", "Smoke", "Sound", "ChorusSoundEffect", "CompressorSoundEffect", "ChannelSelectorSoundEffect", "DistortionSoundEffect", "EchoSoundEffect", "EqualizerSoundEffect", "FlangeSoundEffect", "PitchShiftSoundEffect", "ReverbSoundEffect", "TremoloSoundEffect", "SoundGroup", "Sparkles", "Speaker", "StandalonePluginScripts", "StarterGear", "SurfaceAppearance", "Team", "TeleportOptions", "TerrainRegion", "TestService", "TextChannel", "Trail", "Tween", "UIAspectRatioConstraint", "UISizeConstraint", "UITextSizeConstraint", "UICorner", "UIGradient", "UIGridLayout", "UIListLayout", "UIPageLayout", "UITableLayout", "UIPadding", "UIScale", "UIStroke", "BinaryStringValue", "BoolValue", "BrickColorValue", "CFrameValue", "Color3Value", "DoubleConstrainedValue", "IntConstrainedValue", "IntValue", "NumberValue", "ObjectValue", "RayValue", "StringValue", "Vector3Value", "Vector3Curve", "VirtualInputManager", "VoiceChannel", "WeldConstraint"]
[[NumberRange.new.args]]
type = "number"

[[NumberRange.new.args]]
required = false
type = "number"
[[NumberSequence.new.args]]
type = "any"

[[NumberSequence.new.args]]
required = false
type = "number"
[[NumberSequenceKeypoint.new.args]]
type = "number"

[[NumberSequenceKeypoint.new.args]]
type = "number"

[[NumberSequenceKeypoint.new.args]]
required = false
type = "number"
[OverlapParams.new]
args = []
[[PathWaypoint.new.args]]
required = false

[PathWaypoint.new.args.type]
display = "Vector3"

[[PathWaypoint.new.args]]
required = false

[PathWaypoint.new.args.type]
display = "PathWaypointAction"
[[PhysicalProperties.new.args]]
type = "any"

[[PhysicalProperties.new.args]]
required = false
type = "number"

[[PhysicalProperties.new.args]]
required = false
type = "number"

[[PhysicalProperties.new.args]]
required = false
type = "number"

[[PhysicalProperties.new.args]]
required = false
type = "number"
[[Random.new.args]]
required = false
type = "number"
[[Ray.new.args]]
[Ray.new.args.type]
display = "Vector3"

[[Ray.new.args]]
[Ray.new.args.type]
display = "Vector3"
[RaycastParams.new]
args = []
[[Rect.new.args]]
type = "any"

[[Rect.new.args]]
type = "any"

[[Rect.new.args]]
required = false
type = "number"

[[Rect.new.args]]
required = false
type = "number"
[[Region3.new.args]]
[Region3.new.args.type]
display = "Vector3"

[[Region3.new.args]]
[Region3.new.args.type]
display = "Vector3"
[[Region3int16.new.args]]
required = false

[Region3int16.new.args.type]
display = "Vector3"

[[Region3int16.new.args]]
required = false

[Region3int16.new.args.type]
display = "Vector3"
[[TweenInfo.new.args]]
required = false
type = "number"

[[TweenInfo.new.args]]
required = false

[TweenInfo.new.args.type]
display = "EasingStyle"

[[TweenInfo.new.args]]
required = false

[TweenInfo.new.args.type]
display = "EasingDirection"

[[TweenInfo.new.args]]
required = false
type = "number"

[[TweenInfo.new.args]]
required = false
type = "bool"

[[TweenInfo.new.args]]
required = false
type = "number"
[[UDim.new.args]]
required = false
type = "number"

[[UDim.new.args]]
required = false
type = "number"
[[UDim2.fromOffset.args]]
required = "use UDim2.new() if you want an empty UDim2"
type = "number"

[[UDim2.fromOffset.args]]
required = false
type = "number"
[[UDim2.fromScale.args]]
required = "use UDim2.new() if you want an empty UDim2"
type = "number"

[[UDim2.fromScale.args]]
required = false
type = "number"
[[UDim2.new.args]]
required = false
type = "any"

[[UDim2.new.args]]
required = false
type = "any"

[[UDim2.new.args]]
required = false
type = "number"

[[UDim2.new.args]]
required = false
type = "number"

[UserSettings]
args = []
[[Vector2.new.args]]
required = false
type = "number"

[[Vector2.new.args]]
required = false
type = "number"
[[Vector2int16.new.args]]
required = false
type = "number"

[[Vector2int16.new.args]]
required = false
type = "number"
[[Vector3.FromAxis.args]]
[Vector3.FromAxis.args.type]
display = "Axis"
[[Vector3.FromNormalId.args]]
[Vector3.FromNormalId.args.type]
display = "NormalId"
[[Vector3.new.args]]
required = false
type = "number"

[[Vector3.new.args]]
required = false
type = "number"

[[Vector3.new.args]]
required = false
type = "number"
[[Vector3int16.new.args]]
required = false
type = "number"

[[Vector3int16.new.args]]
required = false
type = "number"

[[Vector3int16.new.args]]
required = false
type = "number"
[[bit32.arshift.args]]
type = "number"

[[bit32.arshift.args]]
type = "number"
[[bit32.band.args]]
type = "..."
[[bit32.bnot.args]]
type = "number"
[[bit32.bor.args]]
type = "..."
[[bit32.btest.args]]
type = "..."
[[bit32.bxor.args]]
type = "..."
[[bit32.extract.args]]
type = "number"

[[bit32.extract.args]]
type = "number"

[[bit32.extract.args]]
required = false
type = "number"
[[bit32.lrotate.args]]
type = "number"

[[bit32.lrotate.args]]
type = "number"
[[bit32.lshift.args]]
type = "number"

[[bit32.lshift.args]]
type = "number"
[[bit32.replace.args]]
type = "number"

[[bit32.replace.args]]
type = "number"

[[bit32.replace.args]]
required = false
type = "number"
[[bit32.rrotate.args]]
type = "number"

[[bit32.rrotate.args]]
type = "number"
[[bit32.rshift.args]]
type = "number"

[[bit32.rshift.args]]
type = "number"
[[collectgarbage.args]]
type = ["count"]
[coroutine.isyieldable]
args = []
[debug.debug]
removed = true

[debug.getfenv]
removed = true

[debug.gethook]
removed = true

[debug.getinfo]
removed = true

[debug.getlocal]
removed = true

[debug.getmetatable]
removed = true

[debug.getregistry]
removed = true

[debug.getupvalue]
removed = true
[[debug.info.args]]
type = "any"

[[debug.info.args]]
type = "any"

[[debug.info.args]]
required = false
type = "string"
[[debug.profilebegin.args]]
type = "string"

[debug.profileend]
args = []

[debug.setfenv]
removed = true

[debug.sethook]
removed = true

[debug.setlocal]
removed = true

[debug.setmetatable]
removed = true

[debug.setupvalue]
removed = true
[[delay.args]]
type = "number"

[[delay.args]]
type = "function"

[dofile]
removed = true

[elapsedTime]
args = []
[[error.args]]
required = "Erroring without an explanation is unhelpful to users."
type = "any"

[[error.args]]
required = false
type = "number"

[game]
struct = "DataModel"

[io]
removed = true

[load]
removed = true

[loadfile]
removed = true
[[math.clamp.args]]
type = "number"

[[math.clamp.args]]
type = "number"

[[math.clamp.args]]
type = "number"
[[math.log.args]]
type = "number"

[[math.log.args]]
required = false
type = "number"
[[math.noise.args]]
type = "number"

[[math.noise.args]]
required = false
type = "number"

[[math.noise.args]]
required = false
type = "number"
[[math.round.args]]
type = "number"
[[math.sign.args]]
type = "number"

[module]
removed = true
[os.execute]
removed = true

[os.exit]
removed = true

[os.getenv]
removed = true

[os.remove]
removed = true

[os.rename]
removed = true

[os.setlocale]
removed = true

[os.tmpname]
removed = true

[package]
removed = true

[plugin]
struct = "Plugin"
[[require.args]]
type = "number"

[script]
struct = "Script"

[settings]
args = []

[shared]
property = true
writable = "new-fields"
[[spawn.args]]
type = "function"
[string.dump]
removed = true
[[string.pack.args]]
type = "string"

[[string.pack.args]]
type = "..."
[[string.packsize.args]]
type = "string"
[[string.split.args]]
type = "string"

[[string.split.args]]
required = false
type = "string"
[[string.unpack.args]]
type = "string"

[[string.unpack.args]]
type = "string"

[[string.unpack.args]]
required = false
type = "number"
[[table.clear.args]]
type = "table"
[[table.create.args]]
type = "number"

[[table.create.args]]
required = false
type = "any"
[[table.find.args]]
type = "table"

[[table.find.args]]
type = "any"

[[table.find.args]]
required = false
type = "number"
[[table.move.args]]
type = "table"

[[table.move.args]]
type = "number"

[[table.move.args]]
type = "number"

[[table.move.args]]
type = "number"

[[table.move.args]]
required = false
type = "table"
[[table.pack.args]]
type = "..."
[[table.unpack.args]]
type = "table"

[[table.unpack.args]]
required = false
type = "number"

[[table.unpack.args]]
required = false
type = "number"
[[task.defer.args]]
type = "function"

[[task.defer.args]]
required = false
type = "..."
[[task.delay.args]]
required = false
type = "number"

[[task.delay.args]]
type = "function"

[[task.delay.args]]
required = false
type = "..."

[task.desynchronize]
args = []
[[task.spawn.args]]
type = "function"

[[task.spawn.args]]
required = false
type = "..."

[task.synchronize]
args = []
[[task.wait.args]]
required = false
type = "number"

[tick]
args = []

[time]
args = []
[[typeof.args]]
type = "any"
[[utf8.char.args]]
required = "utf8.char should be used with an argument despite it not throwing"
type = "number"

[[utf8.char.args]]
required = false
type = "..."

[utf8.charpattern]
property = true
[[utf8.codepoint.args]]
type = "string"

[[utf8.codepoint.args]]
required = false
type = "number"

[[utf8.codepoint.args]]
required = false
type = "number"
[[utf8.codes.args]]
type = "string"
[[utf8.graphemes.args]]
type = "string"

[[utf8.graphemes.args]]
required = false
type = "number"

[[utf8.graphemes.args]]
required = false
type = "number"
[[utf8.len.args]]
type = "string"

[[utf8.len.args]]
required = false
type = "number"

[[utf8.len.args]]
required = false
type = "number"
[[utf8.nfcnormalize.args]]
type = "string"
[[utf8.nfdnormalize.args]]
type = "string"
[[utf8.offset.args]]
type = "string"

[[utf8.offset.args]]
required = false
type = "number"

[[utf8.offset.args]]
required = false
type = "number"
[[wait.args]]
required = false
type = "number"
[[warn.args]]
type = "string"

[[warn.args]]
required = false
type = "..."

[workspace]
struct = "Workspace"
