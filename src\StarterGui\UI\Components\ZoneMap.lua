-- 區域地圖組件 - 顯示遊戲世界地圖和傳送界面
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local StateManager = require(script.Parent.Parent.StateManager)
local ZoneConfig = require(game.ReplicatedStorage.Configuration.ZoneConfig)

local ZoneMap = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

function ZoneMap.new()
    return scope:New "Frame" {
        Name = "ZoneMapFrame",
        Size = UDim2.new(0.9, 0, 0.9, 0),
        Position = UDim2.new(0.05, 0, 0.05, 0),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Visible = scope:Computed(function(use)
            local ui = use(StateManager.UI)
            return ui.mapOpen
        end),
        ZIndex = 50,
        
        [scope.Children] = {
            scope:New "UICorner" {
                CornerRadius = UDim.new(0, 10)
            },
            
            -- 標題欄
            scope:New "Frame" {
                Name = "TitleBar",
                Size = UDim2.new(1, 0, 0.08, 0),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(20, 20, 20),
                BorderSizePixel = 0,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 10)
                    },
                    
                    scope:New "TextLabel" {
                        Name = "Title",
                        Size = UDim2.new(0.8, 0, 1, 0),
                        Position = UDim2.new(0.1, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = "🗺️ 世界地圖",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    },
                    
                    -- 關閉按鈕
                    scope:New "TextButton" {
                        Name = "CloseButton",
                        Size = UDim2.new(0.06, 0, 0.8, 0),
                        Position = UDim2.new(0.92, 0, 0.1, 0),
                        BackgroundColor3 = Color3.fromRGB(255, 0, 0),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "✕",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        
                        [scope.Children] = {
                            scope:New "UICorner" {
                                CornerRadius = UDim.new(0.5, 0)
                            }
                        },
                        
                        [scope.OnEvent "Activated"] = function()
                            StateManager:UpdateUI({ mapOpen = false })
                        end
                    }
                }
            },
            
            -- 地圖內容區域
            scope:New "Frame" {
                Name = "MapContent",
                Size = UDim2.new(1, -20, 0.92, -20),
                Position = UDim2.new(0, 10, 0.08, 10),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    -- 左側區域列表
                    scope:New "Frame" {
                        Name = "ZoneList",
                        Size = UDim2.new(0.35, -10, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            scope:New "UICorner" {
                                CornerRadius = UDim.new(0, 8)
                            },
                            
                            -- 區域列表標題
                            scope:New "TextLabel" {
                                Name = "ListTitle",
                                Size = UDim2.new(1, 0, 0.08, 0),
                                Position = UDim2.new(0, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = "可用區域",
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            },
                            
                            -- 區域列表滾動框
                            scope:New "ScrollingFrame" {
                                Name = "ZoneScrollFrame",
                                Size = UDim2.new(1, -10, 0.92, -10),
                                Position = UDim2.new(0, 5, 0.08, 5),
                                BackgroundTransparency = 1,
                                BorderSizePixel = 0,
                                ScrollBarThickness = 6,
                                ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100),
                                
                                [scope.Children] = {
                                    scope:New "UIListLayout" {
                                        Padding = UDim.new(0, 5),
                                        SortOrder = Enum.SortOrder.LayoutOrder
                                    },
                                    
                                    -- 動態生成區域項目
                                    [scope.Children] = ZoneMap.createZoneListItems()
                                }
                            }
                        }
                    },
                    
                    -- 右側地圖視圖
                    scope:New "Frame" {
                        Name = "MapView",
                        Size = UDim2.new(0.65, -10, 1, 0),
                        Position = UDim2.new(0.35, 10, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(50, 50, 50),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            scope:New "UICorner" {
                                CornerRadius = UDim.new(0, 8)
                            },
                            
                            -- 地圖背景
                            scope:New "ImageLabel" {
                                Name = "MapBackground",
                                Size = UDim2.new(1, -10, 1, -10),
                                Position = UDim2.new(0, 5, 0, 5),
                                BackgroundTransparency = 1,
                                Image = "rbxassetid://6031075938", -- 地圖背景圖片
                                ScaleType = Enum.ScaleType.Stretch,
                                
                                [scope.Children] = {
                                    scope:New "UICorner" {
                                        CornerRadius = UDim.new(0, 8)
                                    },
                                    
                                    -- 動態生成區域標記
                                    [scope.Children] = ZoneMap.createZoneMarkers()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
end

-- 創建區域列表項目
function ZoneMap.createZoneListItems()
    return scope:Computed(function()
        local zoneItems = {}
        local zones = ZoneConfig:GetAllZones()
        
        local sortedZones = {}
        for zoneId, zoneConfig in pairs(zones) do
            table.insert(sortedZones, { id = zoneId, config = zoneConfig })
        end
        
        -- 按等級需求排序
        table.sort(sortedZones, function(a, b)
            return (ZoneConfig.LevelRequirements[a.id] or 1) < (ZoneConfig.LevelRequirements[b.id] or 1)
        end)
        
        for i, zone in ipairs(sortedZones) do
            local zoneItem = ZoneMap.createZoneListItem(zone.id, zone.config, i)
            table.insert(zoneItems, zoneItem)
        end
        
        return zoneItems
    end)
end

-- 創建單個區域列表項目
function ZoneMap.createZoneListItem(zoneId, zoneConfig, index)
    local levelReq = ZoneConfig.LevelRequirements[zoneId] or 1
    local teleportCost = ZoneConfig:GetTeleportCost(zoneId)
    
    return scope:New "Frame" {
        Name = "ZoneItem_" .. zoneId,
        Size = UDim2.new(1, 0, 0, 80),
        LayoutOrder = index,
        BackgroundColor3 = scope:Computed(function()
            -- 根據區域類型設置顏色
            if zoneConfig.type == ZoneConfig.ZoneTypes.SAFE then
                return Color3.fromRGB(0, 150, 0)
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
                return Color3.fromRGB(150, 100, 0)
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
                return Color3.fromRGB(150, 0, 0)
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.EVENT then
                return Color3.fromRGB(150, 0, 150)
            else
                return Color3.fromRGB(100, 100, 100)
            end
        end),
        BackgroundTransparency = 0.3,
        BorderSizePixel = 1,
        BorderColor3 = Color3.fromRGB(200, 200, 200),
        
        [scope.Children] = {
            scope:New "UICorner" {
                CornerRadius = UDim.new(0, 6)
            },
            
            -- 區域圖標
            scope:New "ImageLabel" {
                Name = "ZoneIcon",
                Size = UDim2.new(0, 50, 0, 50),
                Position = UDim2.new(0, 10, 0, 15),
                BackgroundTransparency = 1,
                Image = scope:Computed(function()
                    -- 根據區域類型返回圖標
                    if zoneConfig.type == ZoneConfig.ZoneTypes.SAFE then
                        return "rbxassetid://6031075938" -- 城堡圖標
                    elseif zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
                        return "rbxassetid://6031097225" -- 森林圖標
                    elseif zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
                        return "rbxassetid://6031094678" -- 劍圖標
                    else
                        return "rbxassetid://6031075938" -- 預設圖標
                    end
                end),
                ScaleType = Enum.ScaleType.Fit
            },
            
            -- 區域信息
            scope:New "Frame" {
                Name = "ZoneInfo",
                Size = UDim2.new(0.6, -70, 1, -10),
                Position = UDim2.new(0, 70, 0, 5),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    -- 區域名稱
                    scope:New "TextLabel" {
                        Name = "ZoneName",
                        Size = UDim2.new(1, 0, 0.4, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = zoneConfig.name,
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        TextXAlignment = Enum.TextXAlignment.Left
                    },
                    
                    -- 區域描述
                    scope:New "TextLabel" {
                        Name = "ZoneDescription",
                        Size = UDim2.new(1, 0, 0.3, 0),
                        Position = UDim2.new(0, 0, 0.4, 0),
                        BackgroundTransparency = 1,
                        Text = zoneConfig.description,
                        TextColor3 = Color3.fromRGB(200, 200, 200),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        TextXAlignment = Enum.TextXAlignment.Left,
                        TextWrapped = true
                    },
                    
                    -- 等級需求
                    scope:New "TextLabel" {
                        Name = "LevelRequirement",
                        Size = UDim2.new(1, 0, 0.3, 0),
                        Position = UDim2.new(0, 0, 0.7, 0),
                        BackgroundTransparency = 1,
                        Text = "需要等級: " .. levelReq,
                        TextColor3 = Color3.fromRGB(255, 255, 0),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        TextXAlignment = Enum.TextXAlignment.Left
                    }
                }
            },
            
            -- 傳送按鈕
            scope:New "TextButton" {
                Name = "TeleportButton",
                Size = UDim2.new(0.25, -10, 0.6, 0),
                Position = UDim2.new(0.75, 0, 0.2, 0),
                BackgroundColor3 = Color3.fromRGB(0, 150, 255),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = scope:Computed(function()
                    if teleportCost.coins > 0 then
                        return "傳送\n💰" .. teleportCost.coins
                    else
                        return "傳送\n免費"
                    end
                end),
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 6)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    ZoneMap.handleTeleportRequest(zoneId, zoneConfig.name)
                end
            }
        }
    }
end

-- 創建地圖區域標記
function ZoneMap.createZoneMarkers()
    return scope:Computed(function()
        local markers = {}
        local zones = ZoneConfig:GetAllZones()
        
        -- 預定義的區域位置 (相對於地圖)
        local zonePositions = {
            castle = { x = 0.5, y = 0.5 },    -- 中心
            forest = { x = 0.3, y = 0.7 },    -- 左下
            ice = { x = 0.2, y = 0.2 },       -- 左上
            lava = { x = 0.8, y = 0.8 },      -- 右下
            pvp_arena = { x = 0.7, y = 0.3 }, -- 右上
            event_plaza = { x = 0.5, y = 0.2 } -- 上中
        }
        
        for zoneId, zoneConfig in pairs(zones) do
            local position = zonePositions[zoneId]
            if position then
                local marker = ZoneMap.createZoneMarker(zoneId, zoneConfig, position)
                table.insert(markers, marker)
            end
        end
        
        return markers
    end)
end

-- 創建單個區域標記
function ZoneMap.createZoneMarker(zoneId, zoneConfig, position)
    return scope:New "TextButton" {
        Name = "ZoneMarker_" .. zoneId,
        Size = UDim2.new(0, 40, 0, 40),
        Position = UDim2.new(position.x, -20, position.y, -20),
        BackgroundColor3 = scope:Computed(function()
            if zoneConfig.type == ZoneConfig.ZoneTypes.SAFE then
                return Color3.fromRGB(0, 255, 0)
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
                return Color3.fromRGB(255, 165, 0)
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
                return Color3.fromRGB(255, 0, 0)
            else
                return Color3.fromRGB(150, 150, 150)
            end
        end),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 2,
        BorderColor3 = Color3.fromRGB(255, 255, 255),
        Text = scope:Computed(function()
            -- 根據區域類型顯示圖標
            if zoneConfig.type == ZoneConfig.ZoneTypes.SAFE then
                return "🏰"
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
                return "🌲"
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
                return "⚔️"
            elseif zoneConfig.type == ZoneConfig.ZoneTypes.EVENT then
                return "🎉"
            else
                return "📍"
            end
        end),
        TextColor3 = Color3.fromRGB(255, 255, 255),
        TextScaled = true,
        Font = Enum.Font.GothamBold,
        
        [scope.Children] = {
            scope:New "UICorner" {
                CornerRadius = UDim.new(0.5, 0)
            },
            
            -- 區域名稱標籤
            scope:New "TextLabel" {
                Name = "ZoneLabel",
                Size = UDim2.new(2, 0, 0.5, 0),
                Position = UDim2.new(-0.5, 0, 1.1, 0),
                BackgroundColor3 = Color3.fromRGB(0, 0, 0),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = zoneConfig.name,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.Gotham,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 4)
                    }
                }
            }
        },
        
        [scope.OnEvent "Activated"] = function()
            ZoneMap.handleTeleportRequest(zoneId, zoneConfig.name)
        end
    }
end

-- 處理傳送請求
function ZoneMap.handleTeleportRequest(zoneId, zoneName)
    print("🚀 請求傳送到:", zoneName)
    
    -- 獲取 ZoneController
    local Knit = require(game.ReplicatedStorage.Packages.Knit)
    local ZoneController = Knit.GetController("ZoneController")
    
    if ZoneController then
        ZoneController:teleportToZone(zoneId)
    else
        warn("❌ 無法獲取 ZoneController")
    end
end

function ZoneMap.cleanup()
    scope:doCleanup()
end

return ZoneMap
