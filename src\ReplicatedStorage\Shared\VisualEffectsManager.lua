-- 視覺效果管理器 - 怪物視覺效果標準
local VisualEffectsManager = {}

-- 依賴
local MonsterConfig = require(game.ReplicatedStorage.Configuration.MonsterConfig)

-- 效果緩存
VisualEffectsManager._effectCache = {}

-- 初始化視覺效果管理器
function VisualEffectsManager:Initialize()
    print("✨ 初始化視覺效果管理器...")
    
    -- 清空緩存
    self._effectCache = {}
    
    print("✅ 視覺效果管理器初始化完成")
end

-- 應用稀有度視覺效果
function VisualEffectsManager:ApplyRarityEffects(model, rarity)
    if not model or not model:IsA("Model") then
        warn("❌ 無效的模型")
        return false
    end
    
    if not MonsterConfig.RARITY[rarity] then
        warn("❌ 無效的稀有度:", rarity)
        return false
    end
    
    local effectConfig = MonsterConfig.VISUAL_EFFECTS.MATERIALS[rarity]
    local colorConfig = MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity]
    
    -- 應用材質效果
    self:ApplyMaterialEffects(model, effectConfig, colorConfig)
    
    -- 應用特殊效果
    if MonsterConfig.VISUAL_EFFECTS.SPECIAL_EFFECTS[rarity] then
        self:ApplySpecialEffects(model, rarity)
    end
    
    -- 添加稀有度標識
    self:AddRarityIndicator(model, rarity)
    
    print(string.format("✨ 已為 %s 應用 %s 級視覺效果", model.Name, rarity))
    return true
end

-- 應用材質效果
function VisualEffectsManager:ApplyMaterialEffects(model, effectConfig, colorConfig)
    local neonPartsCount = 0
    local totalParts = 0
    
    local function applyToModel(obj)
        for _, child in pairs(obj:GetChildren()) do
            if child:IsA("BasePart") then
                totalParts = totalParts + 1
                
                -- 應用主要材質
                if math.random() > 0.3 then -- 70% 機率使用主要材質
                    child.Material = effectConfig.primary
                    child.Color = colorConfig.primary
                else
                    child.Material = effectConfig.secondary
                    child.Color = colorConfig.secondary
                end
                
                -- 應用 Neon 效果
                if effectConfig.allowNeon and neonPartsCount < (effectConfig.minNeonParts or 0) then
                    child.Material = Enum.Material.Neon
                    child.Color = colorConfig.accent
                    neonPartsCount = neonPartsCount + 1
                elseif effectConfig.allowNeon and math.random() < 0.2 then -- 20% 機率額外 Neon
                    child.Material = Enum.Material.Neon
                    child.Color = colorConfig.accent
                    neonPartsCount = neonPartsCount + 1
                end
                
                -- 應用 Glass 效果
                if effectConfig.allowGlass and math.random() < 0.15 then -- 15% 機率 Glass
                    child.Material = Enum.Material.Glass
                    child.Transparency = 0.3
                    child.Color = colorConfig.secondary
                end
                
            elseif child:IsA("Model") then
                applyToModel(child)
            end
        end
    end
    
    applyToModel(model)
    
    print(string.format("🎨 材質效果: %d/%d 部件使用 Neon", neonPartsCount, totalParts))
end

-- 應用特殊效果
function VisualEffectsManager:ApplySpecialEffects(model, rarity)
    local effects = MonsterConfig.VISUAL_EFFECTS.SPECIAL_EFFECTS[rarity]
    if not effects then return end
    
    local primaryPart = model.PrimaryPart or self:FindLargestPart(model)
    if not primaryPart then return end
    
    for _, effectType in ipairs(effects) do
        self:CreateEffect(primaryPart, effectType, rarity)
    end
end

-- 創建特效
function VisualEffectsManager:CreateEffect(part, effectType, rarity)
    local effect = nil
    
    if effectType == "Sparkles" then
        effect = Instance.new("Sparkles")
        effect.SparkleColor = MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].accent
        
    elseif effectType == "PointLight" then
        effect = Instance.new("PointLight")
        effect.Color = MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].accent
        effect.Brightness = rarity == "LEGENDARY" and 2 or 1
        effect.Range = rarity == "LEGENDARY" and 20 or 10
        
    elseif effectType == "Fire" then
        effect = Instance.new("Fire")
        effect.Color = MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].primary
        effect.SecondaryColor = MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].secondary
        effect.Size = rarity == "LEGENDARY" and 8 or 5
        
    elseif effectType == "Smoke" then
        effect = Instance.new("Smoke")
        effect.Color = MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].primary
        effect.Size = rarity == "LEGENDARY" and 3 or 2
        effect.RiseVelocity = 2
        
    elseif effectType == "ParticleEmitter" then
        effect = Instance.new("ParticleEmitter")
        effect.Color = ColorSequence.new(MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].accent)
        effect.Size = NumberSequence.new(1, 0)
        effect.Lifetime = NumberRange.new(1, 3)
        effect.Rate = rarity == "LEGENDARY" and 50 or 25
        effect.SpreadAngle = Vector2.new(45, 45)
        effect.Speed = NumberRange.new(5, 10)
        
    elseif effectType == "Beam" then
        -- Beam 需要兩個 Attachment
        local attachment1 = Instance.new("Attachment")
        local attachment2 = Instance.new("Attachment")
        attachment1.Position = Vector3.new(0, part.Size.Y/2, 0)
        attachment2.Position = Vector3.new(0, -part.Size.Y/2, 0)
        attachment1.Parent = part
        attachment2.Parent = part
        
        effect = Instance.new("Beam")
        effect.Attachment0 = attachment1
        effect.Attachment1 = attachment2
        effect.Color = ColorSequence.new(MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].accent)
        effect.Width0 = 2
        effect.Width1 = 2
        effect.Transparency = NumberSequence.new(0.5)
        
    elseif effectType == "Trail" then
        local attachment = Instance.new("Attachment")
        attachment.Parent = part
        
        effect = Instance.new("Trail")
        effect.Attachment0 = attachment
        effect.Attachment1 = attachment
        effect.Color = ColorSequence.new(MonsterConfig.VISUAL_EFFECTS.COLOR_SCHEMES[rarity].accent)
        effect.Lifetime = 2
        effect.MinLength = 0.5
        effect.Transparency = NumberSequence.new(0, 1)
    end
    
    if effect then
        effect.Parent = part
        print(string.format("✨ 已添加 %s 效果到 %s", effectType, part.Name))
    end
end

-- 添加稀有度標識
function VisualEffectsManager:AddRarityIndicator(model, rarity)
    -- 創建稀有度標識 GUI
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "RarityIndicator"
    billboardGui.Size = UDim2.new(0, 100, 0, 30)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.Adornee = model.PrimaryPart or self:FindLargestPart(model)
    
    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(1, 0, 1, 0)
    frame.BackgroundColor3 = MonsterConfig.RARITY[rarity].color
    frame.BorderSizePixel = 0
    frame.Parent = billboardGui
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = MonsterConfig.RARITY[rarity].name
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = frame
    
    billboardGui.Parent = model
    
    -- 添加動畫效果
    if rarity == "LEGENDARY" or rarity == "EPIC" then
        self:AddFloatingAnimation(billboardGui)
    end
end

-- 添加浮動動畫
function VisualEffectsManager:AddFloatingAnimation(gui)
    local tweenService = game:GetService("TweenService")
    
    local tweenInfo = TweenInfo.new(
        2, -- 持續時間
        Enum.EasingStyle.Sine,
        Enum.EasingDirection.InOut,
        -1, -- 無限重複
        true -- 反向
    )
    
    local tween = tweenService:Create(gui, tweenInfo, {
        StudsOffset = gui.StudsOffset + Vector3.new(0, 1, 0)
    })
    
    tween:Play()
end

-- 尋找最大的部件
function VisualEffectsManager:FindLargestPart(model)
    local largestPart = nil
    local largestVolume = 0
    
    local function searchModel(obj)
        for _, child in pairs(obj:GetChildren()) do
            if child:IsA("BasePart") then
                local volume = child.Size.X * child.Size.Y * child.Size.Z
                if volume > largestVolume then
                    largestVolume = volume
                    largestPart = child
                end
            elseif child:IsA("Model") then
                searchModel(child)
            end
        end
    end
    
    searchModel(model)
    return largestPart
end

-- 移除所有效果
function VisualEffectsManager:RemoveAllEffects(model)
    local function removeEffects(obj)
        for _, child in pairs(obj:GetChildren()) do
            if child:IsA("Sparkles") or child:IsA("PointLight") or 
               child:IsA("Fire") or child:IsA("Smoke") or 
               child:IsA("ParticleEmitter") or child:IsA("Beam") or 
               child:IsA("Trail") or child:IsA("BillboardGui") then
                child:Destroy()
            elseif child:IsA("Model") then
                removeEffects(child)
            end
        end
    end
    
    removeEffects(model)
    print(string.format("🧹 已移除 %s 的所有視覺效果", model.Name))
end

-- 更新稀有度效果
function VisualEffectsManager:UpdateRarityEffects(model, newRarity)
    -- 先移除舊效果
    self:RemoveAllEffects(model)
    
    -- 應用新效果
    self:ApplyRarityEffects(model, newRarity)
end

-- 預覽效果 (用於編輯器)
function VisualEffectsManager:PreviewEffects(model, rarity, duration)
    duration = duration or 5
    
    -- 保存原始狀態
    local originalState = self:SaveModelState(model)
    
    -- 應用預覽效果
    self:ApplyRarityEffects(model, rarity)
    
    -- 定時恢復
    task.spawn(function()
        task.wait(duration)
        self:RestoreModelState(model, originalState)
        print(string.format("🔄 已恢復 %s 的原始狀態", model.Name))
    end)
end

-- 保存模型狀態
function VisualEffectsManager:SaveModelState(model)
    local state = {}
    
    local function saveState(obj, path)
        for _, child in pairs(obj:GetChildren()) do
            if child:IsA("BasePart") then
                state[path .. child.Name] = {
                    material = child.Material,
                    color = child.Color,
                    transparency = child.Transparency
                }
            elseif child:IsA("Model") then
                saveState(child, path .. child.Name .. "/")
            end
        end
    end
    
    saveState(model, "")
    return state
end

-- 恢復模型狀態
function VisualEffectsManager:RestoreModelState(model, state)
    -- 先移除效果
    self:RemoveAllEffects(model)
    
    local function restoreState(obj, path)
        for _, child in pairs(obj:GetChildren()) do
            if child:IsA("BasePart") then
                local savedState = state[path .. child.Name]
                if savedState then
                    child.Material = savedState.material
                    child.Color = savedState.color
                    child.Transparency = savedState.transparency
                end
            elseif child:IsA("Model") then
                restoreState(child, path .. child.Name .. "/")
            end
        end
    end
    
    restoreState(model, "")
end

-- 調試信息
function VisualEffectsManager:Debug()
    print("✨ 視覺效果管理器調試信息:")
    print("  緩存效果數量:", #self._effectCache)
    
    print("  支援的效果類型:")
    for rarity, effects in pairs(MonsterConfig.VISUAL_EFFECTS.SPECIAL_EFFECTS) do
        print(string.format("    %s: %s", rarity, table.concat(effects, ", ")))
    end
end

return VisualEffectsManager
