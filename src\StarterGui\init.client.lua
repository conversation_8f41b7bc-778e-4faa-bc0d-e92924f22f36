-- 客戶端主啟動腳本
-- 初始化 Knit 控制器和 Fusion UI

print("🎨 PetSim99Lua 客戶端啟動中...")

-- 引入依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)

-- 引入配置
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)

-- 引入 UI 系統
local MainUI = require(script.UI.MainUI)
local StateManager = require(script.UI.StateManager)

print("✅ 依賴已載入")

-- 添加所有 Knit 控制器
local controllersFolder = script.Controllers
for _, controllerModule in pairs(controllersFolder:GetChildren()) do
    if controllerModule:IsA("ModuleScript") then
        require(controllerModule)
        print("🎮 已載入控制器:", controllerModule.Name)
    end
end

-- 啟動 Knit
Knit.Start():andThen(function()
    print("🌐 Knit 客戶端已啟動")

    -- 初始化 Fusion UI
    local mainUI = MainUI.new()
    print("🖼️ Fusion UI 已初始化")

    -- 設置載入狀態
    StateManager:UpdateUI({ isLoading = true })

    -- 初始化控制器
    local UIController = Knit.GetController("UIController")
    local InputController = Knit.GetController("InputController")
    local AudioController = Knit.GetController("AudioController")
    local ZoneController = Knit.GetController("ZoneController")

    if UIController then
        print("🎮 UIController 已連接")
    end

    if InputController then
        print("⌨️ InputController 已連接")
    end

    if AudioController then
        print("🔊 AudioController 已連接")
    end

    if ZoneController then
        print("🗺️ ZoneController 已連接")
    end

    -- 連接到服務端並載入數據
    local PlayerService = Knit.GetService("PlayerService")
    if PlayerService then
        -- 請求玩家數據
        PlayerService:GetPlayerData():andThen(function(data)
            if data then
                print("📊 玩家數據已載入")

                -- 更新狀態管理器
                if data.playerData then
                    StateManager:UpdatePlayerData(data.playerData)
                end

                if data.heroStats then
                    StateManager:UpdateHeroStats(data.heroStats)
                end

                if data.currencies then
                    StateManager:UpdateCurrencies(data.currencies)
                end

                if data.pets then
                    StateManager:UpdatePets(data.pets)
                end

                if data.quests then
                    StateManager:UpdateQuests(data.quests)
                end

                if data.pokedex then
                    StateManager:UpdatePokedex(data.pokedex)
                end

                if data.vip then
                    StateManager:UpdateVIP(data.vip)
                end

                if data.settings then
                    StateManager:UpdateSettings(data.settings)
                end

                print("✅ 所有數據已同步到 UI")
            end

            -- 關閉載入畫面
            StateManager:UpdateUI({ isLoading = false })

        end):catch(function(err)
            warn("❌ 玩家數據載入失敗:", err)
            StateManager:UpdateUI({ isLoading = false })
        end)
    end

end):catch(function(err)
    error("❌ Knit 客戶端啟動失敗: " .. tostring(err))
end)

-- 錯誤處理
local function handleError(err)
    warn("🚨 客戶端錯誤:", err)
    warn(debug.traceback())
end

-- 全局錯誤捕獲
local success, err = pcall(function()
    -- 主要初始化邏輯已在上面完成
end)

if not success then
    handleError(err)
end

print("🎮 PetSim99Lua 客戶端啟動完成！")
print("📱 遊戲版本:", GameConfig.VERSION)
