-- Fusion 0.2 完整實現 - 純Fusion UI專用
local Fusion = {}

-- 版本信息
Fusion.version = {
    major = 0,
    minor = 2,
    isRelease = true
}

-- State 管理
local StateObject = {}
StateObject.__index = StateObject

function StateObject:get()
    return self._value
end

function StateObject:set(newValue)
    if self._value ~= newValue then
        self._value = newValue
        -- 觸發所有監聽器
        for _, listener in pairs(self._listeners) do
            pcall(listener, newValue)
        end
    end
end

function StateObject:onChange(callback)
    table.insert(self._listeners, callback)
    return function()
        -- 返回斷開連接的函數
        for i, listener in ipairs(self._listeners) do
            if listener == callback then
                table.remove(self._listeners, i)
                break
            end
        end
    end
end

function Fusion.State(initialValue)
    local state = setmetatable({
        _value = initialValue,
        _listeners = {}
    }, StateObject)
    return state
end

-- Computed 管理
local ComputedObject = {}
ComputedObject.__index = ComputedObject

function ComputedObject:get()
    return self._compute()
end

function ComputedObject:onChange(callback)
    table.insert(self._listeners, callback)
    return function()
        for i, listener in ipairs(self._listeners) do
            if listener == callback then
                table.remove(self._listeners, i)
                break
            end
        end
    end
end

function Fusion.Computed(computeFunction)
    local computed = setmetatable({
        _compute = computeFunction,
        _listeners = {}
    }, ComputedObject)
    return computed
end

-- New 函數
function Fusion.New(className)
    return function(properties)
        local instance = Instance.new(className)
        local connections = {}

        for property, value in pairs(properties) do
            if property == Fusion.Children then
                -- 處理子元素
                if type(value) == "table" then
                    for _, child in pairs(value) do
                        if typeof(child) == "Instance" then
                            child.Parent = instance
                        elseif type(child) == "table" and child.get then
                            -- 響應式子元素
                            local currentChild = child:get()
                            if typeof(currentChild) == "Instance" then
                                currentChild.Parent = instance
                            end

                            local connection = child:onChange(function(newChild)
                                if typeof(newChild) == "Instance" then
                                    newChild.Parent = instance
                                end
                            end)
                            table.insert(connections, connection)
                        end
                    end
                end
            elseif string.sub(property, 1, 8) == "OnEvent_" then
                -- 處理事件
                local eventName = string.sub(property, 9)
                if instance[eventName] then
                    local connection = instance[eventName]:Connect(value)
                    table.insert(connections, connection)
                end
            else
                -- 處理普通屬性
                if type(value) == "table" and value.get then
                    -- 響應式屬性
                    instance[property] = value:get()
                    local connection = value:onChange(function(newValue)
                        instance[property] = newValue
                    end)
                    table.insert(connections, connection)
                else
                    -- 靜態屬性
                    instance[property] = value
                end
            end
        end

        return instance
    end
end

-- 特殊屬性
Fusion.Children = "Children"

-- 事件處理
function Fusion.OnEvent(eventName)
    return "OnEvent_" .. eventName
end

-- 工具函數
function Fusion.Value(initialValue)
    return Fusion.State(initialValue)
end

return Fusion
