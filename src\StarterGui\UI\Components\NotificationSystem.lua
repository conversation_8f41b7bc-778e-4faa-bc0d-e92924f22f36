-- 通知系統組件 - 顯示遊戲通知和提示
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local TweenService = game:GetService("TweenService")

local NotificationSystem = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

-- 通知隊列
local notificationQueue = {}
local activeNotifications = {}

function NotificationSystem.new()
    return scope:New "Frame" {
        Name = "NotificationSystem",
        Size = UDim2.new(0.3, 0, 1, 0),
        Position = UDim2.new(0.7, 0, 0, 0),
        BackgroundTransparency = 1,
        ZIndex = 100, -- 確保在最上層
        
        [scope.Children] = {
            scope:New "UIListLayout" {
                Padding = UDim.new(0, 10),
                SortOrder = Enum.SortOrder.LayoutOrder,
                VerticalAlignment = Enum.VerticalAlignment.Top
            },
            
            scope:New "UIPadding" {
                PaddingTop = UDim.new(0, 100), -- 避免與狀態欄重疊
                PaddingRight = UDim.new(0, 20)
            }
        }
    }
end

-- 顯示通知
function NotificationSystem.showNotification(message, notificationType, duration)
    notificationType = notificationType or "info"
    duration = duration or 3
    
    local notification = NotificationSystem.createNotification(message, notificationType, duration)
    
    -- 添加到活躍通知列表
    table.insert(activeNotifications, notification)
    
    -- 設置自動移除
    task.wait(duration)
    NotificationSystem.removeNotification(notification)
end

function NotificationSystem.createNotification(message, notificationType, duration)
    local notificationId = tick() .. math.random()
    
    local backgroundColor, iconText, textColor
    
    -- 根據通知類型設置樣式
    if notificationType == "success" then
        backgroundColor = Color3.fromRGB(0, 150, 0)
        iconText = "✓"
        textColor = Color3.fromRGB(255, 255, 255)
    elseif notificationType == "error" then
        backgroundColor = Color3.fromRGB(200, 0, 0)
        iconText = "✕"
        textColor = Color3.fromRGB(255, 255, 255)
    elseif notificationType == "warning" then
        backgroundColor = Color3.fromRGB(255, 165, 0)
        iconText = "⚠"
        textColor = Color3.fromRGB(0, 0, 0)
    elseif notificationType == "info" then
        backgroundColor = Color3.fromRGB(0, 100, 200)
        iconText = "ℹ"
        textColor = Color3.fromRGB(255, 255, 255)
    elseif notificationType == "pet" then
        backgroundColor = Color3.fromRGB(150, 0, 150)
        iconText = "🐾"
        textColor = Color3.fromRGB(255, 255, 255)
    elseif notificationType == "quest" then
        backgroundColor = Color3.fromRGB(255, 215, 0)
        iconText = "📋"
        textColor = Color3.fromRGB(0, 0, 0)
    elseif notificationType == "level" then
        backgroundColor = Color3.fromRGB(255, 215, 0)
        iconText = "⭐"
        textColor = Color3.fromRGB(0, 0, 0)
    else
        backgroundColor = Color3.fromRGB(100, 100, 100)
        iconText = "•"
        textColor = Color3.fromRGB(255, 255, 255)
    end
    
    local notification = scope:New "Frame" {
        Name = "Notification_" .. notificationId,
        Size = UDim2.new(1, 0, 0, 60),
        BackgroundColor3 = backgroundColor,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        LayoutOrder = #activeNotifications + 1,
        
        -- 初始位置在右側外面
        Position = UDim2.new(1, 50, 0, 0),
        
        [scope.Children] = {
            -- 圓角效果
            scope:New "UICorner" {
                CornerRadius = UDim.new(0, 8)
            },
            
            -- 陰影效果
            scope:New "UIStroke" {
                Color = Color3.fromRGB(0, 0, 0),
                Transparency = 0.7,
                Thickness = 2
            },
            
            -- 圖標
            scope:New "TextLabel" {
                Name = "Icon",
                Size = UDim2.new(0, 40, 1, 0),
                Position = UDim2.new(0, 10, 0, 0),
                BackgroundTransparency = 1,
                Text = iconText,
                TextColor3 = textColor,
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                TextStrokeTransparency = 0,
                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
            },
            
            -- 消息文字
            scope:New "TextLabel" {
                Name = "Message",
                Size = UDim2.new(1, -120, 1, -10),
                Position = UDim2.new(0, 60, 0, 5),
                BackgroundTransparency = 1,
                Text = message,
                TextColor3 = textColor,
                TextScaled = true,
                Font = Enum.Font.Gotham,
                TextXAlignment = Enum.TextXAlignment.Left,
                TextWrapped = true,
                TextStrokeTransparency = 0,
                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
            },
            
            -- 關閉按鈕
            scope:New "TextButton" {
                Name = "CloseButton",
                Size = UDim2.new(0, 30, 0, 30),
                Position = UDim2.new(1, -40, 0, 15),
                BackgroundColor3 = Color3.fromRGB(255, 255, 255),
                BackgroundTransparency = 0.8,
                BorderSizePixel = 0,
                Text = "✕",
                TextColor3 = textColor,
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0.5, 0)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    NotificationSystem.removeNotification(notification)
                end
            },
            
            -- 進度條 (顯示剩餘時間)
            scope:New "Frame" {
                Name = "ProgressBar",
                Size = UDim2.new(1, 0, 0, 3),
                Position = UDim2.new(0, 0, 1, -3),
                BackgroundColor3 = Color3.fromRGB(255, 255, 255),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                
                [scope.Children] = {
                    scope:New "Frame" {
                        Name = "Progress",
                        Size = UDim2.new(1, 0, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = textColor,
                        BackgroundTransparency = 0.5,
                        BorderSizePixel = 0
                    }
                }
            }
        }
    }
    
    -- 滑入動畫
    local slideInTween = TweenService:Create(
        notification,
        TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        { Position = UDim2.new(0, 0, 0, 0) }
    )
    
    -- 進度條動畫
    local progressBar = notification:FindFirstChild("ProgressBar"):FindFirstChild("Progress")
    local progressTween = TweenService:Create(
        progressBar,
        TweenInfo.new(duration, Enum.EasingStyle.Linear),
        { Size = UDim2.new(0, 0, 1, 0) }
    )
    
    -- 播放動畫
    slideInTween:Play()
    progressTween:Play()
    
    -- 存儲動畫引用以便清理
    notification:SetAttribute("SlideInTween", slideInTween)
    notification:SetAttribute("ProgressTween", progressTween)
    notification:SetAttribute("NotificationId", notificationId)
    
    return notification
end

function NotificationSystem.removeNotification(notification)
    if not notification or not notification.Parent then
        return
    end
    
    -- 停止進度條動畫
    local progressTween = notification:GetAttribute("ProgressTween")
    if progressTween then
        progressTween:Cancel()
    end
    
    -- 滑出動畫
    local slideOutTween = TweenService:Create(
        notification,
        TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
        { 
            Position = UDim2.new(1, 50, 0, 0),
            BackgroundTransparency = 1
        }
    )
    
    slideOutTween:Play()
    
    slideOutTween.Completed:Connect(function()
        -- 從活躍通知列表中移除
        for i, activeNotif in ipairs(activeNotifications) do
            if activeNotif == notification then
                table.remove(activeNotifications, i)
                break
            end
        end
        
        -- 銷毀通知
        if notification and notification.Parent then
            notification:Destroy()
        end
    end)
end

-- 清除所有通知
function NotificationSystem.clearAllNotifications()
    for _, notification in ipairs(activeNotifications) do
        NotificationSystem.removeNotification(notification)
    end
    activeNotifications = {}
end

-- 預設通知類型的便捷方法
function NotificationSystem.showSuccess(message, duration)
    NotificationSystem.showNotification(message, "success", duration)
end

function NotificationSystem.showError(message, duration)
    NotificationSystem.showNotification(message, "error", duration)
end

function NotificationSystem.showWarning(message, duration)
    NotificationSystem.showNotification(message, "warning", duration)
end

function NotificationSystem.showInfo(message, duration)
    NotificationSystem.showNotification(message, "info", duration)
end

function NotificationSystem.showPetNotification(message, duration)
    NotificationSystem.showNotification(message, "pet", duration)
end

function NotificationSystem.showQuestNotification(message, duration)
    NotificationSystem.showNotification(message, "quest", duration)
end

function NotificationSystem.showLevelUpNotification(message, duration)
    NotificationSystem.showNotification(message, "level", duration)
end

-- 批量通知 (用於多個獎勵等)
function NotificationSystem.showBatchNotifications(notifications, delay)
    delay = delay or 0.5
    
    for i, notif in ipairs(notifications) do
        task.spawn(function()
            task.wait((i - 1) * delay)
            NotificationSystem.showNotification(notif.message, notif.type, notif.duration)
        end)
    end
end

function NotificationSystem.cleanup()
    NotificationSystem.clearAllNotifications()
    scope:doCleanup()
end

return NotificationSystem
