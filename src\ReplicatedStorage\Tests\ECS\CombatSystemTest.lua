-- 戰鬥系統測試
return function()
    local Matter = require(game.ReplicatedStorage.Packages.Matter)
    local Components = require(game.ReplicatedStorage.Components)
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
    
    describe("CombatSystem", function()
        local world
        local combatSystem
        
        beforeEach(function()
            -- 創建測試世界
            world = Matter.World.new()
            
            -- 創建戰鬥系統實例
            local CombatSystem = require(game.ServerScriptService.Systems.CombatSystem)
            combatSystem = CombatSystem.new()
        end)
        
        afterEach(function()
            if world then
                world:clear()
            end
        end)
        
        it("應該正確初始化戰鬥", function()
            -- 創建攻擊者
            local attacker = world:spawn(
                Components.HeroComponent({
                    level = 5,
                    experience = 0,
                    skillPoints = 0
                }),
                Components.StatsComponent({
                    attack = 25,
                    defense = 10,
                    critRate = 0.1,
                    critDamage = 1.5
                }),
                Components.HealthComponent({
                    current = 200,
                    maximum = 200,
                    regeneration = 1,
                    lastRegenTime = tick()
                })
            )
            
            -- 創建目標
            local target = world:spawn(
                Components.MonsterComponent({
                    speciesId = "test_monster",
                    level = 3,
                    spawnZone = "forest",
                    spawnTime = tick(),
                    lastAttackTime = 0
                }),
                Components.HealthComponent({
                    current = 100,
                    maximum = 100,
                    regeneration = 0,
                    lastRegenTime = tick()
                })
            )
            
            -- 初始化戰鬥
            world:insert(attacker, Components.CombatComponent({
                isInCombat = true,
                target = target,
                combatStartTime = tick(),
                lastAttackTime = 0
            }))
            
            -- 驗證戰鬥狀態
            local combat = world:get(attacker, Components.CombatComponent)
            expect(combat).to.be.ok()
            expect(combat.isInCombat).to.equal(true)
            expect(combat.target).to.equal(target)
        end)
        
        it("應該正確計算傷害", function()
            -- 創建攻擊者屬性
            local attackerStats = {
                attack = 30,
                defense = 10,
                critRate = 0.0, -- 設為0確保測試穩定性
                critDamage = 1.5
            }
            
            -- 創建目標屬性
            local targetDefense = 5
            
            -- 計算預期傷害
            local baseDamage = attackerStats.attack
            local damageReduction = targetDefense * 0.5 -- 假設防禦減傷公式
            local expectedDamage = math.max(1, baseDamage - damageReduction)
            
            -- 模擬傷害計算
            local actualDamage = math.max(1, baseDamage - damageReduction)
            
            expect(actualDamage).to.equal(expectedDamage)
            expect(actualDamage).to.be.greaterThan(0)
        end)
        
        it("應該正確處理暴擊", function()
            -- 創建高暴擊率的攻擊者
            local attackerStats = {
                attack = 20,
                defense = 10,
                critRate = 1.0, -- 100% 暴擊率
                critDamage = 2.0 -- 200% 暴擊傷害
            }
            
            local baseDamage = 20
            local isCrit = math.random() < attackerStats.critRate
            
            -- 由於暴擊率是100%，應該總是暴擊
            expect(isCrit).to.equal(true)
            
            local critDamage = baseDamage * attackerStats.critDamage
            expect(critDamage).to.equal(40)
        end)
        
        it("應該正確處理戰鬥結束", function()
            -- 創建攻擊者
            local attacker = world:spawn(
                Components.CombatComponent({
                    isInCombat = true,
                    target = nil,
                    combatStartTime = tick(),
                    lastAttackTime = 0
                }),
                Components.HealthComponent({
                    current = 100,
                    maximum = 100,
                    regeneration = 1,
                    lastRegenTime = tick()
                })
            )
            
            -- 創建死亡的目標
            local target = world:spawn(
                Components.HealthComponent({
                    current = 0, -- 已死亡
                    maximum = 100,
                    regeneration = 0,
                    lastRegenTime = tick()
                })
            )
            
            -- 模擬戰鬥結束
            world:insert(attacker, Components.CombatComponent({
                isInCombat = false,
                target = nil,
                combatStartTime = 0,
                lastAttackTime = 0
            }))
            
            -- 驗證戰鬥結束
            local combat = world:get(attacker, Components.CombatComponent)
            expect(combat.isInCombat).to.equal(false)
            expect(combat.target).to.equal(nil)
        end)
        
        it("應該正確處理經驗值獎勵", function()
            -- 創建英雄
            local hero = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                })
            )
            
            -- 模擬擊敗怪物獲得經驗
            local monsterLevel = 3
            local baseExpReward = 50
            local levelDifference = monsterLevel - 1 -- 英雄等級1
            local expMultiplier = 1 + (levelDifference * 0.1) -- 等級差異加成
            local expectedExp = math.floor(baseExpReward * expMultiplier)
            
            -- 給予經驗值
            local heroData = world:get(hero, Components.HeroComponent)
            world:insert(hero, Components.HeroComponent({
                level = heroData.level,
                experience = heroData.experience + expectedExp,
                skillPoints = heroData.skillPoints
            }))
            
            -- 驗證經驗值獲得
            local updatedHero = world:get(hero, Components.HeroComponent)
            expect(updatedHero.experience).to.equal(expectedExp)
        end)
        
        it("應該正確處理戰鬥冷卻", function()
            -- 創建攻擊者
            local attacker = world:spawn(
                Components.CombatComponent({
                    isInCombat = true,
                    target = nil,
                    combatStartTime = tick(),
                    lastAttackTime = tick() - 5 -- 5秒前攻擊
                })
            )
            
            local combat = world:get(attacker, Components.CombatComponent)
            local currentTime = tick()
            local timeSinceLastAttack = currentTime - combat.lastAttackTime
            local attackCooldown = GameConfig.COMBAT.ATTACK_COOLDOWN or 2
            
            -- 檢查是否可以攻擊
            local canAttack = timeSinceLastAttack >= attackCooldown
            expect(canAttack).to.equal(true)
            
            -- 模擬剛剛攻擊過
            world:insert(attacker, Components.CombatComponent({
                isInCombat = combat.isInCombat,
                target = combat.target,
                combatStartTime = combat.combatStartTime,
                lastAttackTime = currentTime
            }))
            
            -- 立即檢查，應該無法攻擊
            local updatedCombat = world:get(attacker, Components.CombatComponent)
            local newTimeSinceAttack = currentTime - updatedCombat.lastAttackTime
            local canAttackAgain = newTimeSinceAttack >= attackCooldown
            expect(canAttackAgain).to.equal(false)
        end)
        
        it("應該正確處理死亡狀態", function()
            -- 創建瀕死的實體
            local entity = world:spawn(
                Components.HealthComponent({
                    current = 1,
                    maximum = 100,
                    regeneration = 0,
                    lastRegenTime = tick()
                })
            )
            
            -- 造成致命傷害
            local health = world:get(entity, Components.HealthComponent)
            local damage = 10 -- 超過當前血量
            local newHealth = math.max(0, health.current - damage)
            
            world:insert(entity, Components.HealthComponent({
                current = newHealth,
                maximum = health.maximum,
                regeneration = health.regeneration,
                lastRegenTime = health.lastRegenTime
            }))
            
            -- 驗證死亡狀態
            local updatedHealth = world:get(entity, Components.HealthComponent)
            expect(updatedHealth.current).to.equal(0)
            
            -- 檢查是否死亡
            local isDead = updatedHealth.current <= 0
            expect(isDead).to.equal(true)
        end)
        
        it("應該正確處理範圍攻擊", function()
            -- 創建攻擊者
            local attacker = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(0, 0, 0),
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.StatsComponent({
                    attack = 20,
                    defense = 10,
                    critRate = 0.0,
                    critDamage = 1.5
                })
            )
            
            -- 創建範圍內的目標
            local target1 = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(5, 0, 0), -- 5單位距離
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.HealthComponent({
                    current = 50,
                    maximum = 50,
                    regeneration = 0,
                    lastRegenTime = tick()
                })
            )
            
            -- 創建範圍外的目標
            local target2 = world:spawn(
                Components.TransformComponent({
                    position = Vector3.new(15, 0, 0), -- 15單位距離
                    rotation = Vector3.new(0, 0, 0),
                    scale = Vector3.new(1, 1, 1)
                }),
                Components.HealthComponent({
                    current = 50,
                    maximum = 50,
                    regeneration = 0,
                    lastRegenTime = tick()
                })
            )
            
            -- 檢查攻擊範圍
            local attackerPos = world:get(attacker, Components.TransformComponent).position
            local target1Pos = world:get(target1, Components.TransformComponent).position
            local target2Pos = world:get(target2, Components.TransformComponent).position
            
            local attackRange = 10
            local distance1 = (attackerPos - target1Pos).Magnitude
            local distance2 = (attackerPos - target2Pos).Magnitude
            
            expect(distance1).to.be.lessThan(attackRange)
            expect(distance2).to.be.greaterThan(attackRange)
        end)
    end)
end
