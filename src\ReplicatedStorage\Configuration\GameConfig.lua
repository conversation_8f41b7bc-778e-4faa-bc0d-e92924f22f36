-- 遊戲核心配置
local GameConfig = {}

-- 遊戲基本設置
GameConfig.GAME_NAME = "PetSim99Lua"
GameConfig.VERSION = "1.0.0"
GameConfig.MAX_PLAYERS = 50

-- 英雄系統配置
GameConfig.HERO = {
    MAX_LEVEL = 100,
    BASE_HEALTH = 100,
    HEALTH_PER_LEVEL = 50,
    BASE_ATTACK = 15,
    ATTACK_PER_LEVEL = 2,
    BASE_DEFENSE = 8,
    DEFENSE_PER_LEVEL = 1,
    BASE_CRIT_RATE = 0.05,
    BASE_CRIT_DAMAGE = 1.5,
    
    -- 韓式慢速成長經驗需求
    EXP_FORMULA = {
        PHASE_1 = { MIN_LEVEL = 1, MAX_LEVEL = 10, BASE = 100, MULTIPLIER = 1.2 },
        PHASE_2 = { MIN_LEVEL = 11, MAX_LEVEL = 30, BASE = 1000, MULTIPLIER = 1.4 },
        PHASE_3 = { MIN_LEVEL = 31, MAX_LEVEL = 60, BASE = 50000, MULTIPLIER = 1.6 },
        PHASE_4 = { MIN_LEVEL = 61, MAX_LEVEL = 100, BASE = 5000000, MULTIPLIER = 1.8 }
    }
}

-- 寵物系統配置
GameConfig.PET = {
    MAX_ACTIVE_PETS = 5,
    MAX_FOLLOWING_PETS = 1,
    MAX_LEVEL = 100,
    BASE_HEALTH = 50,
    HEALTH_PER_LEVEL = 25,
    
    -- 稀有度配置
    RARITY = {
        COMMON = { name = "普通", color = Color3.fromRGB(255, 255, 255), multiplier = 1.0 },
        RARE = { name = "稀有", color = Color3.fromRGB(0, 255, 0), multiplier = 1.5 },
        EPIC = { name = "史詩", color = Color3.fromRGB(0, 0, 255), multiplier = 2.0 },
        MYTHIC = { name = "神話", color = Color3.fromRGB(128, 0, 128), multiplier = 3.0 },
        LEGENDARY = { name = "傳說", color = Color3.fromRGB(255, 215, 0), multiplier = 5.0 }
    },
    
    -- 扭蛋機率配置
    GACHA_RATES = {
        GOLD = {
            COMMON = 0.70,
            RARE = 0.25,
            EPIC = 0.05,
            MYTHIC = 0.00,
            LEGENDARY = 0.00
        },
        GEM = {
            COMMON = 0.00,
            RARE = 0.40,
            EPIC = 0.35,
            MYTHIC = 0.20,
            LEGENDARY = 0.05
        },
        ROBUX = {
            COMMON = 0.00,
            RARE = 0.30,
            EPIC = 0.40,
            MYTHIC = 0.25,
            LEGENDARY = 0.05
        }
    }
}

-- 區域系統配置
GameConfig.ZONES = {
    CASTLE = {
        name = "城堡",
        unlockLevel = 1,
        unlockCost = 0,
        isPvPEnabled = false,
        isSafeZone = true,
        monsterSpawn = false
    },
    FOREST = {
        name = "森林",
        unlockLevel = 5,
        unlockCost = 2000,
        isPvPEnabled = false,
        isSafeZone = false,
        monsterSpawn = true,
        monsterLevelRange = { min = 1, max = 20 }
    },
    ICE = {
        name = "冰雪",
        unlockLevel = 25,
        unlockCost = 20000,
        isPvPEnabled = false,
        isSafeZone = false,
        monsterSpawn = true,
        monsterLevelRange = { min = 21, max = 50 }
    },
    LAVA = {
        name = "熔岩",
        unlockLevel = 50,
        unlockCost = 100000,
        isPvPEnabled = true,
        isSafeZone = false,
        monsterSpawn = true,
        monsterLevelRange = { min = 51, max = 80 }
    }
}

-- 經濟系統配置
GameConfig.ECONOMY = {
    -- 初始貨幣
    STARTING_COINS = 1000,
    STARTING_GEMS = 20,
    STARTING_ROBUX = 0,
    
    -- 扭蛋價格
    EGG_PRICES = {
        GOLD = { currency = "coins", amount = 1000 },
        GEM = { currency = "gems", amount = 100 },
        ROBUX = { currency = "robux", amount = 50 }
    },
    
    -- 寶石稀缺性控制
    DAILY_GEM_LIMIT = 50,
    GEM_SOURCES = {
        DAILY_LOGIN = { min = 5, max = 10 },
        DAILY_QUEST = { min = 3, max = 8 },
        WEEKLY_QUEST = { min = 15, max = 25 },
        MONTHLY_QUEST = { min = 50, max = 100 }
    }
}

-- VIP 系統配置
GameConfig.VIP = {
    MAX_LEVEL = 10,
    LEVEL_THRESHOLDS = {
        [1] = 80,    -- $1
        [2] = 240,   -- $3
        [3] = 480,   -- $6
        [4] = 800,   -- $10
        [5] = 1600,  -- $20
        [6] = 3200,  -- $40
        [7] = 6400,  -- $80
        [8] = 12000, -- $150
        [9] = 20000, -- $250
        [10] = 40000 -- $500
    },
    
    BENEFITS = {
        [0] = { loginMultiplier = 1.0, gachaDiscount = 0, expBonus = 0, dailyGems = 0 },
        [1] = { loginMultiplier = 1.2, gachaDiscount = 0.05, expBonus = 0.1, dailyGems = 10 },
        [2] = { loginMultiplier = 1.4, gachaDiscount = 0.08, expBonus = 0.15, dailyGems = 15 },
        [3] = { loginMultiplier = 1.6, gachaDiscount = 0.12, expBonus = 0.2, dailyGems = 20 },
        [4] = { loginMultiplier = 1.8, gachaDiscount = 0.15, expBonus = 0.3, dailyGems = 25 },
        [5] = { loginMultiplier = 2.0, gachaDiscount = 0.18, expBonus = 0.4, dailyGems = 35 },
        [6] = { loginMultiplier = 2.2, gachaDiscount = 0.22, expBonus = 0.5, dailyGems = 45 },
        [7] = { loginMultiplier = 2.5, gachaDiscount = 0.25, expBonus = 0.6, dailyGems = 60 },
        [8] = { loginMultiplier = 2.8, gachaDiscount = 0.28, expBonus = 0.8, dailyGems = 80 },
        [9] = { loginMultiplier = 3.0, gachaDiscount = 0.30, expBonus = 1.0, dailyGems = 100 },
        [10] = { loginMultiplier = 3.5, gachaDiscount = 0.35, expBonus = 1.5, dailyGems = 150 }
    }
}

-- 戰鬥系統配置
GameConfig.COMBAT = {
    TURN_DURATION = 3, -- 秒
    CRIT_DAMAGE_MULTIPLIER = 1.5,
    DEFENSE_REDUCTION = 0.5,
    DAMAGE_VARIANCE = 0.2, -- ±20%
    
    -- 屬性克制
    TYPE_EFFECTIVENESS = {
        FIRE = { weak_to = "ICE", strong_against = "NATURE" },
        ICE = { weak_to = "NATURE", strong_against = "FIRE" },
        NATURE = { weak_to = "FIRE", strong_against = "ICE" },
        LIGHT = { weak_to = "DARK", strong_against = "DARK" },
        DARK = { weak_to = "LIGHT", strong_against = "LIGHT" },
        NORMAL = { weak_to = nil, strong_against = nil }
    }
}

return GameConfig
