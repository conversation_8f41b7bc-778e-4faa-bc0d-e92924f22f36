-- 狀態管理系統 - 使用 Fusion 0.2 管理全局遊戲狀態
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)

-- Fusion 0.2 組件
local State = Fusion.State
local Computed = Fusion.Computed

local StateManager = {}

-- 玩家數據狀態
StateManager.PlayerData = State({
    userId = 0,
    displayName = "",
    joinTime = 0,
    isOnline = false
})

-- 英雄狀態
StateManager.HeroStats = State({
    level = 1,
    experience = 0,
    maxHealth = 150,
    currentHealth = 150,
    attack = 15,
    defense = 8,
    critRate = 0.05,
    critDamage = 1.5
})

-- 貨幣狀態
StateManager.Currencies = State({
    coins = 1000,
    gems = 20,
    robux = 0,
    experience = 0
})

-- 寵物狀態
StateManager.Pets = State({
    activePets = {},
    allPets = {},
    selectedPet = nil
})

-- 戰鬥狀態
StateManager.Combat = State({
    isInCombat = false,
    target = nil,
    combatStartTime = 0,
    lastAttackTime = 0
})

-- UI 狀態
StateManager.UI = State({
    currentScreen = "main",
    isLoading = false,
    notifications = {},
    inventoryOpen = false,
    petMenuOpen = false,
    questMenuOpen = false,
    mapOpen = false,
    settingsOpen = false
})

-- 任務狀態
StateManager.Quests = State({
    dailyQuests = {},
    weeklyQuests = {},
    monthlyQuests = {},
    completedToday = 0
})

-- 圖鑑狀態
StateManager.Pokedex = State({
    encounteredSpecies = {},
    ownedSpecies = {},
    completionRate = 0,
    totalSpecies = 97
})

-- VIP 狀態
StateManager.VIP = State({
    level = 0,
    totalSpent = 0,
    premiumActive = false,
    premiumExpiry = 0,
    benefits = {}
})

-- 設置狀態
StateManager.Settings = State({
    soundEnabled = true,
    musicEnabled = true,
    notificationsEnabled = true,
    language = "zh-TW",
    graphics = "medium",
    masterVolume = 1.0,
    musicVolume = 0.7,
    sfxVolume = 0.8
})

-- 計算屬性
StateManager.HealthPercentage = Computed(function()
    local hero = StateManager.HeroStats:get()
    if hero.maxHealth and hero.maxHealth > 0 then
        return hero.currentHealth / hero.maxHealth
    end
    return 1
end)

StateManager.ExpPercentage = Computed(function()
    local hero = StateManager.HeroStats:get()
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)

    if hero.level >= GameConfig.HERO.MAX_LEVEL then
        return 1
    end

    local Utils = require(game.ReplicatedStorage.Shared.Utils)
    local requiredExp = Utils.calculateExpForLevel(hero.level + 1)
    return requiredExp > 0 and (hero.experience / requiredExp) or 0
end)

StateManager.FormattedCurrencies = Computed(function()
    local currencies = StateManager.Currencies:get()
    local Utils = require(game.ReplicatedStorage.Shared.Utils)

    return {
        coins = Utils.formatNumber(currencies.coins),
        gems = Utils.formatNumber(currencies.gems),
        robux = Utils.formatNumber(currencies.robux)
    }
end)

StateManager.ActivePetCount = Computed(function()
    local pets = StateManager.Pets:get()
    return #pets.activePets
end)

StateManager.TotalPetCount = Computed(function()
    local pets = StateManager.Pets:get()
    return #pets.allPets
end)

StateManager.QuestProgress = Computed(function()
    local quests = StateManager.Quests:get()
    local totalQuests = #quests.dailyQuests + #quests.weeklyQuests + #quests.monthlyQuests
    local completedQuests = 0

    for _, quest in ipairs(quests.dailyQuests) do
        if quest.completed then
            completedQuests = completedQuests + 1
        end
    end

    for _, quest in ipairs(quests.weeklyQuests) do
        if quest.completed then
            completedQuests = completedQuests + 1
        end
    end

    for _, quest in ipairs(quests.monthlyQuests) do
        if quest.completed then
            completedQuests = completedQuests + 1
        end
    end

    return {
        completed = completedQuests,
        total = totalQuests,
        percentage = totalQuests > 0 and (completedQuests / totalQuests) or 0
    }
end)

-- 更新方法
function StateManager:UpdatePlayerData(newData)
    local current = self.PlayerData:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newData) do
        merged[key] = value
    end

    self.PlayerData:set(merged)
end

function StateManager:UpdateHeroStats(newStats)
    local current = self.HeroStats:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newStats) do
        merged[key] = value
    end

    self.HeroStats:set(merged)
end

function StateManager:UpdateCurrencies(newCurrencies)
    local current = self.Currencies:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newCurrencies) do
        merged[key] = value
    end

    self.Currencies:set(merged)
end

function StateManager:UpdatePets(newPets)
    local current = self.Pets:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newPets) do
        merged[key] = value
    end

    self.Pets:set(merged)
end

function StateManager:UpdateCombat(newCombat)
    local current = self.Combat:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newCombat) do
        merged[key] = value
    end

    self.Combat:set(merged)
end

function StateManager:UpdateUI(newUI)
    local current = self.UI:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newUI) do
        merged[key] = value
    end

    self.UI:set(merged)
end

function StateManager:UpdateQuests(newQuests)
    local current = self.Quests:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newQuests) do
        merged[key] = value
    end

    self.Quests:set(merged)
end

function StateManager:UpdatePokedex(newPokedex)
    local current = self.Pokedex:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newPokedex) do
        merged[key] = value
    end

    self.Pokedex:set(merged)
end

function StateManager:UpdateVIP(newVIP)
    local current = self.VIP:get()
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newVIP) do
        merged[key] = value
    end
    
    self.VIP:set(merged)
end

function StateManager:UpdateSettings(newSettings)
    local current = self.Settings:get()
    local merged = {}

    for key, value in pairs(current) do
        merged[key] = value
    end

    for key, value in pairs(newSettings) do
        merged[key] = value
    end

    self.Settings:set(merged)
end

-- 獲取當前值的方法
function StateManager:GetPlayerData()
    return self.PlayerData:get()
end

function StateManager:GetHeroStats()
    return self.HeroStats:get()
end

function StateManager:GetCurrencies()
    return self.Currencies:get()
end

function StateManager:GetPets()
    return self.Pets:get()
end

function StateManager:GetCombat()
    return self.Combat:get()
end

function StateManager:GetUI()
    return self.UI:get()
end

function StateManager:GetQuests()
    return self.Quests:get()
end

function StateManager:GetPokedex()
    return self.Pokedex:get()
end

function StateManager:GetVIP()
    return self.VIP:get()
end

function StateManager:GetSettings()
    return self.Settings:get()
end

-- 清理方法 (Fusion 0.2 不需要scope清理)
function StateManager:Cleanup()
    -- Fusion 0.2 自動管理清理
end

return StateManager
