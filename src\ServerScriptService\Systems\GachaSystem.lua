-- 扭蛋系統
-- 實現金幣扭蛋、寶石扭蛋、R幣扭蛋三種扭蛋類型

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local Matter = require(ReplicatedStorage.Packages.Matter)
local MonsterComponents = require(ReplicatedStorage.Components.MonsterComponents)
local PetConfig = require(ReplicatedStorage.Configuration.PetConfig)
local Signal = require(ReplicatedStorage.Packages.Signal)

local GachaSystem = {}

-- 事件信號
GachaSystem.onPetObtained = Signal.new()
GachaSystem.onGachaRoll = Signal.new()

-- 扭蛋配置
local GACHA_CONFIG = {
    COIN = {
        name = "金幣扭蛋",
        cost = 1000,
        currency = "coins",
        rates = {
            COMMON = 0.70,    -- 70%
            RARE = 0.25,      -- 25%
            EPIC = 0.05,      -- 5%
            MYTHIC = 0.00,    -- 0%
            LEGENDARY = 0.00  -- 0%
        }
    },
    GEM = {
        name = "寶石扭蛋",
        cost = 100,
        currency = "gems",
        rates = {
            COMMON = 0.00,    -- 0%
            RARE = 0.40,      -- 40%
            EPIC = 0.35,      -- 35%
            MYTHIC = 0.20,    -- 20%
            LEGENDARY = 0.05  -- 5%
        }
    },
    ROBUX = {
        name = "R幣扭蛋",
        cost = 50,
        currency = "robux",
        rates = {
            COMMON = 0.00,    -- 0%
            RARE = 0.50,      -- 50%
            EPIC = 0.30,      -- 30%
            MYTHIC = 0.15,    -- 15%
            LEGENDARY = 0.05  -- 5%
        }
    }
}

-- 根據稀有度獲取寵物列表
function GachaSystem.getPetsByRarity(rarity)
    local pets = {}
    
    for speciesId, data in pairs(PetConfig.Species) do
        if data.rarity == rarity then
            table.insert(pets, speciesId)
        end
    end
    
    return pets
end

-- 根據機率獲取隨機稀有度
function GachaSystem.getRandomRarity(rates)
    local random = math.random()
    local cumulative = 0
    
    -- 按照稀有度順序檢查
    local rarityOrder = {"COMMON", "RARE", "EPIC", "MYTHIC", "LEGENDARY"}
    
    for _, rarity in ipairs(rarityOrder) do
        cumulative = cumulative + (rates[rarity] or 0)
        if random <= cumulative then
            return rarity
        end
    end
    
    return "COMMON" -- 默認返回普通
end

-- 隨機選擇寵物
function GachaSystem.getRandomPet(rarity)
    local pets = GachaSystem.getPetsByRarity(rarity)
    
    if #pets == 0 then
        -- 如果該稀有度沒有寵物，降級到普通
        pets = GachaSystem.getPetsByRarity("COMMON")
    end
    
    if #pets > 0 then
        return pets[math.random(#pets)]
    end
    
    return nil
end

-- 檢查玩家貨幣是否足夠
function GachaSystem.canAfford(world, playerId, gachaType)
    local config = GACHA_CONFIG[gachaType]
    if not config then return false end
    
    -- 這裡應該從玩家數據中檢查貨幣
    -- 暫時返回true用於測試
    return true
end

-- 扣除玩家貨幣
function GachaSystem.deductCurrency(world, playerId, gachaType)
    local config = GACHA_CONFIG[gachaType]
    if not config then return false end
    
    -- 這裡應該實際扣除玩家貨幣
    -- 暫時返回true用於測試
    print(string.format("💰 扣除 %d %s", config.cost, config.currency))
    return true
end

-- 給玩家添加寵物
function GachaSystem.addPetToPlayer(world, playerId, speciesId, rarity)
    local currentTime = tick()
    
    -- 創建寵物實體
    local pet = world:spawn(
        MonsterComponents.Monster({
            speciesId = speciesId,
            name = PetConfig.Species[speciesId] and PetConfig.Species[speciesId].name or speciesId,
            rarity = rarity,
            level = 1,
            zone = "",
            isWild = false,
            spawnTime = currentTime,
            lastSeenTime = currentTime
        })
    )
    
    -- 更新圖鑑條目為已擁有
    for entityId, entry in world:query(MonsterComponents.PokedexEntry) do
        if entry.playerId == playerId and entry.speciesId == speciesId then
            entry.isOwned = true
            entry.ownedTime = currentTime
            world:insert(entityId, MonsterComponents.PokedexEntry(entry))
            break
        end
    end
    
    -- 如果圖鑑條目不存在，創建一個
    local hasEntry = false
    for entityId, entry in world:query(MonsterComponents.PokedexEntry) do
        if entry.playerId == playerId and entry.speciesId == speciesId then
            hasEntry = true
            break
        end
    end
    
    if not hasEntry then
        world:spawn(
            MonsterComponents.PokedexEntry({
                playerId = playerId,
                speciesId = speciesId,
                isDiscovered = true,
                isOwned = true,
                discoveryTime = currentTime,
                ownedTime = currentTime,
                encounterCount = 0,
                defeatCount = 0,
                notes = "通過扭蛋獲得"
            })
        )
    end
    
    return pet
end

-- 執行扭蛋
function GachaSystem.rollGacha(world, playerId, gachaType)
    local config = GACHA_CONFIG[gachaType]
    if not config then
        return nil, "無效的扭蛋類型"
    end
    
    -- 檢查是否有足夠貨幣
    if not GachaSystem.canAfford(world, playerId, gachaType) then
        return nil, string.format("貨幣不足，需要 %d %s", config.cost, config.currency)
    end
    
    -- 扣除貨幣
    if not GachaSystem.deductCurrency(world, playerId, gachaType) then
        return nil, "扣除貨幣失敗"
    end
    
    -- 獲取隨機稀有度
    local rarity = GachaSystem.getRandomRarity(config.rates)
    
    -- 獲取隨機寵物
    local speciesId = GachaSystem.getRandomPet(rarity)
    if not speciesId then
        return nil, "沒有可用的寵物"
    end
    
    -- 添加寵物到玩家
    local pet = GachaSystem.addPetToPlayer(world, playerId, speciesId, rarity)
    
    -- 觸發事件
    GachaSystem.onPetObtained:Fire(playerId, speciesId, rarity, gachaType)
    GachaSystem.onGachaRoll:Fire(playerId, gachaType, speciesId, rarity)
    
    print(string.format("🎁 玩家 %d 通過 %s 獲得了 %s (%s)！", 
        playerId, config.name, speciesId, rarity))
    
    return {
        petId = pet,
        speciesId = speciesId,
        rarity = rarity,
        gachaType = gachaType,
        cost = config.cost,
        currency = config.currency
    }
end

-- 批量扭蛋
function GachaSystem.rollMultipleGacha(world, playerId, gachaType, count)
    local results = {}
    local errors = {}
    
    for i = 1, count do
        local result, error = GachaSystem.rollGacha(world, playerId, gachaType)
        
        if result then
            table.insert(results, result)
        else
            table.insert(errors, error)
            break -- 如果失敗就停止
        end
    end
    
    return results, errors
end

-- 獲取扭蛋統計
function GachaSystem.getGachaStats(world, playerId)
    local stats = {
        totalRolls = 0,
        petsByRarity = {
            COMMON = 0,
            RARE = 0,
            EPIC = 0,
            MYTHIC = 0,
            LEGENDARY = 0
        },
        totalSpent = {
            coins = 0,
            gems = 0,
            robux = 0
        }
    }
    
    -- 這裡應該從數據庫或玩家數據中獲取統計
    -- 暫時返回空統計
    
    return stats
end

-- 獲取扭蛋配置信息
function GachaSystem.getGachaInfo(gachaType)
    return GACHA_CONFIG[gachaType]
end

-- 獲取所有扭蛋類型
function GachaSystem.getAllGachaTypes()
    local types = {}
    for gachaType, config in pairs(GACHA_CONFIG) do
        table.insert(types, {
            type = gachaType,
            name = config.name,
            cost = config.cost,
            currency = config.currency,
            rates = config.rates
        })
    end
    return types
end

-- 檢查是否有免費扭蛋
function GachaSystem.hasFreeRoll(world, playerId)
    -- 這裡應該檢查玩家的免費扭蛋狀態
    -- 暫時返回false
    return false
end

-- 使用免費扭蛋
function GachaSystem.useFreeRoll(world, playerId)
    -- 使用免費扭蛋（通常是金幣扭蛋的機率）
    local rarity = GachaSystem.getRandomRarity(GACHA_CONFIG.COIN.rates)
    local speciesId = GachaSystem.getRandomPet(rarity)
    
    if speciesId then
        local pet = GachaSystem.addPetToPlayer(world, playerId, speciesId, rarity)
        
        GachaSystem.onPetObtained:Fire(playerId, speciesId, rarity, "FREE")
        
        print(string.format("🆓 玩家 %d 通過免費扭蛋獲得了 %s (%s)！", 
            playerId, speciesId, rarity))
        
        return {
            petId = pet,
            speciesId = speciesId,
            rarity = rarity,
            gachaType = "FREE",
            cost = 0,
            currency = "none"
        }
    end
    
    return nil
end

-- 初始化系統
function GachaSystem.initialize()
    print("✅ 扭蛋系統初始化完成")
    print("📊 扭蛋類型:")
    for gachaType, config in pairs(GACHA_CONFIG) do
        print(string.format("  - %s: %d %s", config.name, config.cost, config.currency))
    end
end

return GachaSystem
