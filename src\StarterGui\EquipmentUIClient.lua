-- 裝備UI客戶端腳本 - 確保UI正確顯示
-- 這個腳本會在玩家進入遊戲時自動執行

local Players = game:GetService("Players")
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- 等待一下確保所有服務載入
wait(1)

print("🎮 開始創建裝備UI...")

-- 清理舊UI
local oldUI = playerGui:FindFirstChild("EquipmentUI")
if oldUI then
    oldUI:Destroy()
    print("✅ 清理了舊UI")
end

-- 創建主ScreenGui
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "EquipmentUI"
screenGui.ResetOnSpawn = false
screenGui.DisplayOrder = 100  -- 確保在最上層
screenGui.Parent = playerGui

-- 創建主按鈕面板
local mainPanel = Instance.new("Frame")
mainPanel.Name = "MainButtonPanel"
mainPanel.Size = UDim2.new(0, 140, 0, 220)
mainPanel.Position = UDim2.new(1, -150, 0.5, -110)
mainPanel.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
mainPanel.BorderSizePixel = 3
mainPanel.BorderColor3 = Color3.fromRGB(255, 255, 255)
mainPanel.Active = true
mainPanel.Draggable = false
mainPanel.Parent = screenGui

-- 添加圓角效果
local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 8)
corner.Parent = mainPanel

-- 主面板標題
local title = Instance.new("TextLabel")
title.Name = "Title"
title.Size = UDim2.new(1, 0, 0, 35)
title.Position = UDim2.new(0, 0, 0, 0)
title.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
title.BorderSizePixel = 0
title.Text = "裝備系統"
title.TextColor3 = Color3.fromRGB(255, 255, 255)
title.TextSize = 18
title.Font = Enum.Font.SourceSansBold
title.Parent = mainPanel

-- 裝備按鈕
local equipButton = Instance.new("TextButton")
equipButton.Name = "EquipmentButton"
equipButton.Size = UDim2.new(0, 120, 0, 75)
equipButton.Position = UDim2.new(0, 10, 0, 45)
equipButton.BackgroundColor3 = Color3.fromRGB(100, 150, 100)
equipButton.BorderSizePixel = 2
equipButton.BorderColor3 = Color3.fromRGB(255, 255, 255)
equipButton.Text = "⚔️\n裝備面板"
equipButton.TextColor3 = Color3.fromRGB(255, 255, 255)
equipButton.TextSize = 16
equipButton.Font = Enum.Font.SourceSansBold
equipButton.Active = true
equipButton.Parent = mainPanel

local equipCorner = Instance.new("UICorner")
equipCorner.CornerRadius = UDim.new(0, 6)
equipCorner.Parent = equipButton

-- 背包按鈕
local inventoryButton = Instance.new("TextButton")
inventoryButton.Name = "InventoryButton"
inventoryButton.Size = UDim2.new(0, 120, 0, 75)
inventoryButton.Position = UDim2.new(0, 10, 0, 130)
inventoryButton.BackgroundColor3 = Color3.fromRGB(100, 100, 150)
inventoryButton.BorderSizePixel = 2
inventoryButton.BorderColor3 = Color3.fromRGB(255, 255, 255)
inventoryButton.Text = "🎒\n背包面板"
inventoryButton.TextColor3 = Color3.fromRGB(255, 255, 255)
inventoryButton.TextSize = 16
inventoryButton.Font = Enum.Font.SourceSansBold
inventoryButton.Active = true
inventoryButton.Parent = mainPanel

local invCorner = Instance.new("UICorner")
invCorner.CornerRadius = UDim.new(0, 6)
invCorner.Parent = inventoryButton

-- 創建演示面板
local demoPanel = Instance.new("Frame")
demoPanel.Name = "DemoPanel"
demoPanel.Size = UDim2.new(0, 450, 0, 350)
demoPanel.Position = UDim2.new(0.5, -225, 0.5, -175)
demoPanel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
demoPanel.BorderSizePixel = 3
demoPanel.BorderColor3 = Color3.fromRGB(255, 255, 255)
demoPanel.Visible = false
demoPanel.Active = true
demoPanel.Draggable = true
demoPanel.Parent = screenGui

local demoCorner = Instance.new("UICorner")
demoCorner.CornerRadius = UDim.new(0, 10)
demoCorner.Parent = demoPanel

-- 演示面板標題
local demoTitle = Instance.new("TextLabel")
demoTitle.Name = "Title"
demoTitle.Size = UDim2.new(1, 0, 0, 45)
demoTitle.Position = UDim2.new(0, 0, 0, 0)
demoTitle.BackgroundColor3 = Color3.fromRGB(80, 80, 80)
demoTitle.BorderSizePixel = 0
demoTitle.Text = "演示面板"
demoTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
demoTitle.TextSize = 20
demoTitle.Font = Enum.Font.SourceSansBold
demoTitle.Parent = demoPanel

-- 關閉按鈕
local closeButton = Instance.new("TextButton")
closeButton.Name = "CloseButton"
closeButton.Size = UDim2.new(0, 35, 0, 35)
closeButton.Position = UDim2.new(1, -40, 0, 5)
closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
closeButton.BorderSizePixel = 1
closeButton.BorderColor3 = Color3.fromRGB(255, 255, 255)
closeButton.Text = "✕"
closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
closeButton.TextSize = 18
closeButton.Font = Enum.Font.SourceSansBold
closeButton.Active = true
closeButton.Parent = demoPanel

local closeCorner = Instance.new("UICorner")
closeCorner.CornerRadius = UDim.new(0, 4)
closeCorner.Parent = closeButton

-- 演示內容
local demoContent = Instance.new("TextLabel")
demoContent.Name = "Content"
demoContent.Size = UDim2.new(1, -20, 1, -55)
demoContent.Position = UDim2.new(0, 10, 0, 50)
demoContent.BackgroundTransparency = 1
demoContent.Text = "這是裝備系統演示面板\n\n✅ 移動設備友好設計\n✅ 觸摸和滑鼠支援\n✅ 清晰可見的UI元素\n✅ 適合所有平台\n\n點擊右側按鈕測試不同功能\n可以拖動這個面板移動位置"
demoContent.TextColor3 = Color3.fromRGB(255, 255, 255)
demoContent.TextSize = 16
demoContent.Font = Enum.Font.SourceSans
demoContent.TextWrapped = true
demoContent.TextXAlignment = Enum.TextXAlignment.Left
demoContent.TextYAlignment = Enum.TextYAlignment.Top
demoContent.Parent = demoPanel

-- 說明面板
local infoPanel = Instance.new("Frame")
infoPanel.Name = "InfoPanel"
infoPanel.Size = UDim2.new(0, 320, 0, 120)
infoPanel.Position = UDim2.new(0, 10, 1, -130)
infoPanel.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
infoPanel.BorderSizePixel = 2
infoPanel.BorderColor3 = Color3.fromRGB(255, 255, 255)
infoPanel.Active = true
infoPanel.Parent = screenGui

local infoCorner = Instance.new("UICorner")
infoCorner.CornerRadius = UDim.new(0, 8)
infoCorner.Parent = infoPanel

local infoText = Instance.new("TextLabel")
infoText.Name = "InfoText"
infoText.Size = UDim2.new(1, -10, 1, -10)
infoText.Position = UDim2.new(0, 5, 0, 5)
infoText.BackgroundTransparency = 1
infoText.Text = "📱 移動設備友好的裝備UI\n\n• 點擊右側 ⚔️ 按鈕 - 裝備面板\n• 點擊右側 🎒 按鈕 - 背包面板\n• 支援觸摸和滑鼠操作\n• 適合PC、手機、平板"
infoText.TextColor3 = Color3.fromRGB(255, 255, 255)
infoText.TextSize = 14
infoText.Font = Enum.Font.SourceSans
infoText.TextXAlignment = Enum.TextXAlignment.Left
infoText.TextYAlignment = Enum.TextYAlignment.Top
infoText.Parent = infoPanel

-- 按鈕點擊事件
equipButton.Activated:Connect(function()
    demoTitle.Text = "⚔️ 裝備面板"
    demoContent.Text = "裝備面板功能:\n\n• 顯示9個裝備槽位\n• 頭盔、胸甲、腿甲、靴子、手套\n• 主手武器、副手武器/盾牌\n• 戒指、項鍊、耳環\n• 點擊已裝備物品可卸下\n• 顯示裝備屬性和套裝效果\n• 支援拖拽操作\n\n這是演示版本，實際遊戲中會顯示真實裝備數據"
    demoPanel.Visible = true
    print("🎮 打開裝備面板演示")
end)

inventoryButton.Activated:Connect(function()
    demoTitle.Text = "🎒 背包面板"
    demoContent.Text = "背包面板功能:\n\n• 網格顯示所有背包物品\n• 裝備品質顏色區分\n• 強化等級數字顯示\n• 滾動瀏覽大量物品\n• 點擊物品可裝備\n• 支援物品排序和篩選\n• 顯示物品詳細資訊\n\n這是演示版本，實際遊戲中會顯示真實背包數據"
    demoPanel.Visible = true
    print("🎮 打開背包面板演示")
end)

closeButton.Activated:Connect(function()
    demoPanel.Visible = false
    print("🎮 關閉演示面板")
end)

-- 添加按鈕懸停效果
equipButton.MouseEnter:Connect(function()
    equipButton.BackgroundColor3 = Color3.fromRGB(120, 170, 120)
end)

equipButton.MouseLeave:Connect(function()
    equipButton.BackgroundColor3 = Color3.fromRGB(100, 150, 100)
end)

inventoryButton.MouseEnter:Connect(function()
    inventoryButton.BackgroundColor3 = Color3.fromRGB(120, 120, 170)
end)

inventoryButton.MouseLeave:Connect(function()
    inventoryButton.BackgroundColor3 = Color3.fromRGB(100, 100, 150)
end)

print("✅ 裝備UI創建完成！")
print("📍 UI位置:")
print("- 右側: 主按鈕面板")
print("- 左下: 操作說明")
print("- 中央: 演示面板 (點擊按鈕顯示)")

print("\n🎮 測試說明:")
print("1. 查看螢幕右側的裝備系統面板")
print("2. 點擊 ⚔️ 裝備面板按鈕")
print("3. 點擊 🎒 背包面板按鈕")
print("4. 可以拖動演示面板移動")
print("5. 點擊 ✕ 關閉演示面板")

-- 顯示成功訊息
local successMessage = Instance.new("TextLabel")
successMessage.Name = "SuccessMessage"
successMessage.Size = UDim2.new(0, 300, 0, 50)
successMessage.Position = UDim2.new(0.5, -150, 0, 50)
successMessage.BackgroundColor3 = Color3.fromRGB(0, 150, 0)
successMessage.BorderSizePixel = 2
successMessage.BorderColor3 = Color3.fromRGB(255, 255, 255)
successMessage.Text = "✅ 裝備UI載入成功！"
successMessage.TextColor3 = Color3.fromRGB(255, 255, 255)
successMessage.TextSize = 18
successMessage.Font = Enum.Font.SourceSansBold
successMessage.Parent = screenGui

local msgCorner = Instance.new("UICorner")
msgCorner.CornerRadius = UDim.new(0, 6)
msgCorner.Parent = successMessage

-- 3秒後隱藏成功訊息
game:GetService("Debris"):AddItem(successMessage, 3)
