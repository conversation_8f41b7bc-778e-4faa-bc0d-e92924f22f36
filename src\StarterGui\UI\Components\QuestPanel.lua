-- 任務面板組件 - 顯示每日、每週、每月任務
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local StateManager = require(script.Parent.Parent.StateManager)

local QuestPanel = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

function QuestPanel.new()
    return scope:New "Frame" {
        Name = "QuestPanelFrame",
        Size = UDim2.new(0.6, 0, 0.8, 0),
        Position = UDim2.new(0.2, 0, 0.1, 0),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Visible = scope:Computed(function(use)
            local ui = use(StateManager.UI)
            return ui.questMenuOpen
        end),
        
        [scope.Children] = {
            -- 標題欄
            scope:New "Frame" {
                Name = "TitleBar",
                Size = UDim2.new(1, 0, 0.1, 0),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(30, 30, 30),
                BorderSizePixel = 0,
                
                [scope.Children] = {
                    scope:New "TextLabel" {
                        Name = "Title",
                        Size = UDim2.new(0.8, 0, 1, 0),
                        Position = UDim2.new(0.1, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function(use)
                            local progress = use(StateManager.QuestProgress)
                            return string.format("任務中心 (%d/%d)", progress.completed, progress.total)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    },
                    
                    -- 關閉按鈕
                    scope:New "TextButton" {
                        Name = "CloseButton",
                        Size = UDim2.new(0.08, 0, 0.8, 0),
                        Position = UDim2.new(0.9, 0, 0.1, 0),
                        BackgroundColor3 = Color3.fromRGB(255, 0, 0),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "✕",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        
                        [scope.OnEvent "Activated"] = function()
                            StateManager:UpdateUI({ questMenuOpen = false })
                        end
                    }
                }
            },
            
            -- 標籤欄
            scope:New "Frame" {
                Name = "TabBar",
                Size = UDim2.new(1, 0, 0.08, 0),
                Position = UDim2.new(0, 0, 0.1, 0),
                BackgroundColor3 = Color3.fromRGB(35, 35, 35),
                BorderSizePixel = 0,
                
                [scope.Children] = {
                    scope:New "TextButton" {
                        Name = "DailyTab",
                        Size = UDim2.new(0.33, -2, 1, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(0, 150, 255),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "每日任務",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    },
                    
                    scope:New "TextButton" {
                        Name = "WeeklyTab",
                        Size = UDim2.new(0.33, -2, 1, 0),
                        Position = UDim2.new(0.33, 2, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "每週任務",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    },
                    
                    scope:New "TextButton" {
                        Name = "MonthlyTab",
                        Size = UDim2.new(0.33, -2, 1, 0),
                        Position = UDim2.new(0.66, 4, 0, 0),
                        BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = "每月任務",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham
                    }
                }
            },
            
            -- 任務列表
            scope:New "ScrollingFrame" {
                Name = "QuestList",
                Size = UDim2.new(1, -20, 0.82, -20),
                Position = UDim2.new(0, 10, 0.18, 10),
                BackgroundColor3 = Color3.fromRGB(50, 50, 50),
                BackgroundTransparency = 0.5,
                BorderSizePixel = 0,
                ScrollBarThickness = 8,
                ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100),
                
                [scope.Children] = {
                    scope:New "UIListLayout" {
                        Padding = UDim.new(0, 5),
                        SortOrder = Enum.SortOrder.LayoutOrder
                    },
                    
                    scope:New "UIPadding" {
                        PaddingAll = UDim.new(0, 10)
                    },
                    
                    -- 動態生成任務項目
                    [scope.Children] = scope:Computed(function(use)
                        local quests = use(StateManager.Quests)
                        local questItems = {}
                        
                        -- 顯示每日任務
                        for i, quest in ipairs(quests.dailyQuests) do
                            local questItem = QuestPanel.createQuestItem(quest, "daily", i)
                            table.insert(questItems, questItem)
                        end
                        
                        return questItems
                    end)
                }
            }
        }
    }
end

function QuestPanel.createQuestItem(questData, questType, index)
    return scope:New "Frame" {
        Name = "QuestItem_" .. index,
        Size = UDim2.new(1, 0, 0, 80),
        LayoutOrder = index,
        BackgroundColor3 = scope:Computed(function()
            if questData.completed then
                return Color3.fromRGB(0, 150, 0) -- 綠色表示完成
            else
                return Color3.fromRGB(60, 60, 60) -- 灰色表示未完成
            end
        end),
        BackgroundTransparency = 0.3,
        BorderSizePixel = 1,
        BorderColor3 = Color3.fromRGB(100, 100, 100),
        
        [scope.Children] = {
            -- 任務圖標
            scope:New "ImageLabel" {
                Name = "QuestIcon",
                Size = UDim2.new(0, 60, 0, 60),
                Position = UDim2.new(0, 10, 0, 10),
                BackgroundTransparency = 1,
                Image = scope:Computed(function()
                    -- 根據任務類型返回不同圖標
                    if questData.type == "defeat" then
                        return "rbxassetid://6031075938" -- 戰鬥圖標
                    elseif questData.type == "experience" then
                        return "rbxassetid://6031097225" -- 經驗圖標
                    elseif questData.type == "gacha" then
                        return "rbxassetid://6031094678" -- 扭蛋圖標
                    else
                        return "rbxassetid://6031075938" -- 預設圖標
                    end
                end),
                ScaleType = Enum.ScaleType.Fit
            },
            
            -- 任務信息
            scope:New "Frame" {
                Name = "QuestInfo",
                Size = UDim2.new(0.5, -80, 1, -20),
                Position = UDim2.new(0, 80, 0, 10),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    -- 任務名稱
                    scope:New "TextLabel" {
                        Name = "QuestName",
                        Size = UDim2.new(1, 0, 0.4, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = questData.name or "未知任務",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        TextXAlignment = Enum.TextXAlignment.Left
                    },
                    
                    -- 任務描述
                    scope:New "TextLabel" {
                        Name = "QuestDescription",
                        Size = UDim2.new(1, 0, 0.3, 0),
                        Position = UDim2.new(0, 0, 0.4, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function()
                            local desc = questData.description or "無描述"
                            return desc:gsub("{target}", tostring(questData.target or 0))
                        end),
                        TextColor3 = Color3.fromRGB(200, 200, 200),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        TextXAlignment = Enum.TextXAlignment.Left
                    },
                    
                    -- 進度條
                    scope:New "Frame" {
                        Name = "ProgressBar",
                        Size = UDim2.new(1, 0, 0.2, 0),
                        Position = UDim2.new(0, 0, 0.7, 0),
                        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
                        BorderSizePixel = 0,
                        
                        [scope.Children] = {
                            scope:New "Frame" {
                                Name = "ProgressFill",
                                Size = scope:Computed(function()
                                    local progress = questData.progress or 0
                                    local target = questData.target or 1
                                    local percentage = math.min(1, progress / target)
                                    return UDim2.new(percentage, 0, 1, 0)
                                end),
                                Position = UDim2.new(0, 0, 0, 0),
                                BackgroundColor3 = scope:Computed(function()
                                    if questData.completed then
                                        return Color3.fromRGB(0, 255, 0)
                                    else
                                        return Color3.fromRGB(0, 150, 255)
                                    end
                                end),
                                BorderSizePixel = 0
                            },
                            
                            scope:New "TextLabel" {
                                Name = "ProgressText",
                                Size = UDim2.new(1, 0, 1, 0),
                                Position = UDim2.new(0, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = scope:Computed(function()
                                    local progress = questData.progress or 0
                                    local target = questData.target or 1
                                    return string.format("%d/%d", progress, target)
                                end),
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.Gotham,
                                TextStrokeTransparency = 0,
                                TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                            }
                        }
                    }
                }
            },
            
            -- 獎勵顯示
            scope:New "Frame" {
                Name = "RewardInfo",
                Size = UDim2.new(0.25, -10, 1, -20),
                Position = UDim2.new(0.5, 0, 0, 10),
                BackgroundTransparency = 1,
                
                [scope.Children] = {
                    scope:New "TextLabel" {
                        Name = "RewardTitle",
                        Size = UDim2.new(1, 0, 0.3, 0),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundTransparency = 1,
                        Text = "獎勵:",
                        TextColor3 = Color3.fromRGB(255, 255, 0),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        TextXAlignment = Enum.TextXAlignment.Left
                    },
                    
                    scope:New "TextLabel" {
                        Name = "RewardText",
                        Size = UDim2.new(1, 0, 0.7, 0),
                        Position = UDim2.new(0, 0, 0.3, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function()
                            local rewards = questData.rewards or {}
                            local rewardText = ""
                            
                            if rewards.coins then
                                rewardText = rewardText .. "💰" .. rewards.coins .. " "
                            end
                            if rewards.gems then
                                rewardText = rewardText .. "💎" .. rewards.gems .. " "
                            end
                            if rewards.experience then
                                rewardText = rewardText .. "⭐" .. rewards.experience .. " "
                            end
                            
                            return rewardText
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        TextXAlignment = Enum.TextXAlignment.Left,
                        TextWrapped = true
                    }
                }
            },
            
            -- 領取按鈕
            scope:New "TextButton" {
                Name = "ClaimButton",
                Size = UDim2.new(0.2, -10, 0.6, 0),
                Position = UDim2.new(0.78, 0, 0.2, 0),
                BackgroundColor3 = scope:Computed(function()
                    if questData.completed and not questData.claimed then
                        return Color3.fromRGB(0, 255, 0) -- 可領取
                    elseif questData.claimed then
                        return Color3.fromRGB(100, 100, 100) -- 已領取
                    else
                        return Color3.fromRGB(150, 150, 150) -- 未完成
                    end
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = scope:Computed(function()
                    if questData.claimed then
                        return "已領取"
                    elseif questData.completed then
                        return "領取"
                    else
                        return "未完成"
                    end
                end),
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                Active = scope:Computed(function()
                    return questData.completed and not questData.claimed
                end),
                
                [scope.OnEvent "Activated"] = function()
                    if questData.completed and not questData.claimed then
                        print("領取任務獎勵:", questData.name)
                        -- 這裡應該調用 QuestService 來領取獎勵
                    end
                end
            }
        }
    }
end

function QuestPanel.cleanup()
    scope:doCleanup()
end

return QuestPanel
