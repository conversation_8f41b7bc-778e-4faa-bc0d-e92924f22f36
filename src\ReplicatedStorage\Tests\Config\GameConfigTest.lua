-- 遊戲配置測試
return function()
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
    
    describe("GameConfig", function()
        it("應該包含所有必要的配置項", function()
            -- 驗證主要配置分類存在
            expect(GameConfig.HERO).to.be.ok()
            expect(GameConfig.COMBAT).to.be.ok()
            expect(GameConfig.PET).to.be.ok()
            expect(GameConfig.ECONOMY).to.be.ok()
            expect(GameConfig.QUEST).to.be.ok()
            expect(GameConfig.VIP).to.be.ok()
        end)
        
        it("應該包含正確的英雄配置", function()
            local heroConfig = GameConfig.HERO
            
            -- 驗證英雄基本配置
            expect(heroConfig.BASE_HEALTH).to.be.a("number")
            expect(heroConfig.BASE_HEALTH).to.be.greaterThan(0)
            
            expect(heroConfig.BASE_ATTACK).to.be.a("number")
            expect(heroConfig.BASE_ATTACK).to.be.greaterThan(0)
            
            expect(heroConfig.BASE_DEFENSE).to.be.a("number")
            expect(heroConfig.BASE_DEFENSE).to.be.greaterThan(0)
            
            expect(heroConfig.MAX_LEVEL).to.be.a("number")
            expect(heroConfig.MAX_LEVEL).to.be.greaterThan(1)
            
            expect(heroConfig.HEALTH_PER_LEVEL).to.be.a("number")
            expect(heroConfig.HEALTH_PER_LEVEL).to.be.greaterThan(0)
            
            expect(heroConfig.STATS_PER_LEVEL).to.be.a("number")
            expect(heroConfig.STATS_PER_LEVEL).to.be.greaterThan(0)
            
            expect(heroConfig.SKILL_POINT_BONUS).to.be.a("number")
            expect(heroConfig.SKILL_POINT_BONUS).to.be.greaterThan(0)
        end)
        
        it("應該包含正確的戰鬥配置", function()
            local combatConfig = GameConfig.COMBAT
            
            -- 驗證戰鬥基本配置
            expect(combatConfig.ATTACK_COOLDOWN).to.be.a("number")
            expect(combatConfig.ATTACK_COOLDOWN).to.be.greaterThan(0)
            
            expect(combatConfig.CRIT_DAMAGE_MULTIPLIER).to.be.a("number")
            expect(combatConfig.CRIT_DAMAGE_MULTIPLIER).to.be.greaterThan(1)
            
            expect(combatConfig.DEFENSE_REDUCTION).to.be.a("number")
            expect(combatConfig.DEFENSE_REDUCTION).to.be.greaterThan(0)
            expect(combatConfig.DEFENSE_REDUCTION).to.be.lessThan(1)
            
            expect(combatConfig.MIN_DAMAGE).to.be.a("number")
            expect(combatConfig.MIN_DAMAGE).to.be.greaterThan(0)
        end)
        
        it("應該包含正確的寵物配置", function()
            local petConfig = GameConfig.PET
            
            -- 驗證寵物基本配置
            expect(petConfig.MAX_ACTIVE_PETS).to.be.a("number")
            expect(petConfig.MAX_ACTIVE_PETS).to.be.greaterThan(0)
            
            expect(petConfig.MAX_STORAGE).to.be.a("number")
            expect(petConfig.MAX_STORAGE).to.be.greaterThan(petConfig.MAX_ACTIVE_PETS)
            
            expect(petConfig.RARITY).to.be.a("table")
            
            -- 驗證稀有度配置
            local rarities = { "COMMON", "UNCOMMON", "RARE", "EPIC", "LEGENDARY", "MYTHIC" }
            for _, rarity in ipairs(rarities) do
                expect(petConfig.RARITY[rarity]).to.be.ok()
                expect(petConfig.RARITY[rarity].name).to.be.a("string")
                expect(petConfig.RARITY[rarity].color).to.be.ok()
                expect(petConfig.RARITY[rarity].weight).to.be.a("number")
                expect(petConfig.RARITY[rarity].weight).to.be.greaterThan(0)
            end
        end)
        
        it("應該包含正確的經濟配置", function()
            local economyConfig = GameConfig.ECONOMY
            
            -- 驗證經濟基本配置
            expect(economyConfig.STARTING_COINS).to.be.a("number")
            expect(economyConfig.STARTING_COINS).to.be.greaterThan(0)
            
            expect(economyConfig.STARTING_GEMS).to.be.a("number")
            expect(economyConfig.STARTING_GEMS).to.be.greaterThan(0)
            
            expect(economyConfig.MAX_COINS).to.be.a("number")
            expect(economyConfig.MAX_COINS).to.be.greaterThan(economyConfig.STARTING_COINS)
            
            expect(economyConfig.MAX_GEMS).to.be.a("number")
            expect(economyConfig.MAX_GEMS).to.be.greaterThan(economyConfig.STARTING_GEMS)
            
            expect(economyConfig.DAILY_COIN_BONUS).to.be.a("number")
            expect(economyConfig.DAILY_COIN_BONUS).to.be.greaterThan(0)
        end)
        
        it("應該包含正確的任務配置", function()
            local questConfig = GameConfig.QUEST
            
            -- 驗證任務基本配置
            expect(questConfig.MAX_DAILY_QUESTS).to.be.a("number")
            expect(questConfig.MAX_DAILY_QUESTS).to.be.greaterThan(0)
            
            expect(questConfig.MAX_WEEKLY_QUESTS).to.be.a("number")
            expect(questConfig.MAX_WEEKLY_QUESTS).to.be.greaterThan(0)
            
            expect(questConfig.MAX_MONTHLY_QUESTS).to.be.a("number")
            expect(questConfig.MAX_MONTHLY_QUESTS).to.be.greaterThan(0)
            
            expect(questConfig.DAILY_RESET_HOUR).to.be.a("number")
            expect(questConfig.DAILY_RESET_HOUR).to.be.greaterThanOrEqualTo(0)
            expect(questConfig.DAILY_RESET_HOUR).to.be.lessThan(24)
        end)
        
        it("應該包含正確的VIP配置", function()
            local vipConfig = GameConfig.VIP
            
            -- 驗證VIP基本配置
            expect(vipConfig.MAX_LEVEL).to.be.a("number")
            expect(vipConfig.MAX_LEVEL).to.be.greaterThan(0)
            
            expect(vipConfig.LEVELS).to.be.a("table")
            
            -- 驗證VIP等級配置
            for level = 1, vipConfig.MAX_LEVEL do
                local levelConfig = vipConfig.LEVELS[level]
                expect(levelConfig).to.be.ok()
                expect(levelConfig.requiredSpending).to.be.a("number")
                expect(levelConfig.requiredSpending).to.be.greaterThan(0)
                expect(levelConfig.benefits).to.be.a("table")
            end
        end)
        
        it("應該有合理的數值平衡", function()
            -- 驗證英雄成長平衡
            local heroConfig = GameConfig.HERO
            local maxLevelHealth = heroConfig.BASE_HEALTH + (heroConfig.MAX_LEVEL - 1) * heroConfig.HEALTH_PER_LEVEL
            expect(maxLevelHealth).to.be.greaterThan(heroConfig.BASE_HEALTH * 2) -- 最大等級血量至少是初始的2倍
            
            -- 驗證戰鬥平衡
            local combatConfig = GameConfig.COMBAT
            expect(combatConfig.ATTACK_COOLDOWN).to.be.lessThan(10) -- 攻擊冷卻不應該太長
            expect(combatConfig.CRIT_DAMAGE_MULTIPLIER).to.be.lessThan(5) -- 暴擊倍數不應該太高
            
            -- 驗證寵物平衡
            local petConfig = GameConfig.PET
            expect(petConfig.MAX_ACTIVE_PETS).to.be.lessThan(petConfig.MAX_STORAGE) -- 活躍寵物數量應該小於存儲上限
            
            -- 驗證稀有度權重總和
            local totalWeight = 0
            for _, rarityConfig in pairs(petConfig.RARITY) do
                totalWeight = totalWeight + rarityConfig.weight
            end
            expect(totalWeight).to.equal(100) -- 權重總和應該是100
        end)
        
        it("應該有正確的經驗值計算公式", function()
            local Utils = require(game.ReplicatedStorage.Shared.Utils)
            
            -- 測試經驗值計算
            local testCases = {
                { level = 1, expectedExp = 0 },
                { level = 2, expectedExp = 100 },
                { level = 3, expectedExp = 250 },
                { level = 5, expectedExp = 625 },
                { level = 10, expectedExp = 2500 }
            }
            
            for _, testCase in ipairs(testCases) do
                local calculatedExp = Utils.calculateExpForLevel(testCase.level)
                expect(calculatedExp).to.equal(testCase.expectedExp)
            end
        end)
        
        it("應該有正確的傷害計算公式", function()
            local combatConfig = GameConfig.COMBAT
            
            -- 測試傷害計算
            local attack = 100
            local defense = 20
            
            local damageReduction = defense * combatConfig.DEFENSE_REDUCTION
            local expectedDamage = math.max(combatConfig.MIN_DAMAGE, attack - damageReduction)
            
            expect(expectedDamage).to.be.greaterThan(0)
            expect(expectedDamage).to.be.lessThanOrEqualTo(attack)
        end)
        
        it("應該有正確的暴擊計算", function()
            local combatConfig = GameConfig.COMBAT
            
            -- 測試暴擊傷害
            local baseDamage = 100
            local critDamage = baseDamage * combatConfig.CRIT_DAMAGE_MULTIPLIER
            
            expect(critDamage).to.be.greaterThan(baseDamage)
            expect(critDamage).to.equal(baseDamage * combatConfig.CRIT_DAMAGE_MULTIPLIER)
        end)
        
        it("應該有正確的貨幣限制", function()
            local economyConfig = GameConfig.ECONOMY
            
            -- 驗證貨幣上限合理性
            expect(economyConfig.MAX_COINS).to.be.greaterThan(economyConfig.STARTING_COINS * 100)
            expect(economyConfig.MAX_GEMS).to.be.greaterThan(economyConfig.STARTING_GEMS * 50)
            
            -- 驗證每日獎勵合理性
            expect(economyConfig.DAILY_COIN_BONUS).to.be.lessThan(economyConfig.STARTING_COINS)
        end)
        
        it("應該有正確的任務重置時間", function()
            local questConfig = GameConfig.QUEST
            
            -- 驗證重置時間在合理範圍內
            expect(questConfig.DAILY_RESET_HOUR).to.be.greaterThanOrEqualTo(0)
            expect(questConfig.DAILY_RESET_HOUR).to.be.lessThan(24)
            
            -- 驗證任務數量合理性
            expect(questConfig.MAX_DAILY_QUESTS).to.be.lessThan(20)
            expect(questConfig.MAX_WEEKLY_QUESTS).to.be.lessThan(10)
            expect(questConfig.MAX_MONTHLY_QUESTS).to.be.lessThan(5)
        end)
        
        it("應該有正確的VIP等級遞增", function()
            local vipConfig = GameConfig.VIP
            
            -- 驗證VIP等級花費遞增
            local previousSpending = 0
            for level = 1, vipConfig.MAX_LEVEL do
                local levelConfig = vipConfig.LEVELS[level]
                expect(levelConfig.requiredSpending).to.be.greaterThan(previousSpending)
                previousSpending = levelConfig.requiredSpending
            end
        end)
    end)
end
