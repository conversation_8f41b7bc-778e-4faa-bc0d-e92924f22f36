-- 地圖配置系統 - 城堡主地圖 + 三個組隊副本
local MapConfig = {}

-- 地圖類型
MapConfig.MAP_TYPES = {
    MAIN = "main",        -- 主城堡地圖
    DUNGEON = "dungeon"   -- 組隊副本
}

-- 主城堡地圖配置
MapConfig.MAIN_CASTLE = {
    name = "魔法城堡",
    description = "冒險者的聚集地，所有冒險的起點",
    type = MapConfig.MAP_TYPES.MAIN,
    maxPlayers = 50,
    
    -- 功能區域
    areas = {
        SPAWN = {
            name = "出生點",
            position = Vector3.new(0, 10, 0),
            description = "新玩家的出生地點"
        },
        SHOP = {
            name = "商店區",
            position = Vector3.new(20, 10, 0),
            description = "購買裝備和道具"
        },
        GACHA = {
            name = "扭蛋區",
            position = Vector3.new(-20, 10, 0),
            description = "寵物扭蛋機"
        },
        TEAM_FORMATION = {
            name = "組隊區",
            position = Vector3.new(0, 10, 20),
            description = "組建隊伍準備副本"
        },
        DUNGEON_PORTALS = {
            name = "副本傳送門",
            position = Vector3.new(0, 10, -20),
            description = "進入各個副本的傳送門"
        },
        PET_MANAGEMENT = {
            name = "寵物管理",
            position = Vector3.new(30, 10, 0),
            description = "管理和培養寵物"
        }
    },
    
    -- 傳送門配置
    portals = {
        {
            name = "森林副本入口",
            targetDungeon = "FOREST",
            position = Vector3.new(-10, 10, -20),
            requiredLevel = 1,
            teamSizeRequired = {min = 3, max = 5}
        },
        {
            name = "冰雪副本入口", 
            targetDungeon = "ICE",
            position = Vector3.new(0, 10, -20),
            requiredLevel = 15,
            teamSizeRequired = {min = 4, max = 6}
        },
        {
            name = "熔岩副本入口",
            targetDungeon = "LAVA", 
            position = Vector3.new(10, 10, -20),
            requiredLevel = 25,
            teamSizeRequired = {min = 5, max = 8}
        }
    }
}

-- 副本配置
MapConfig.DUNGEONS = {
    FOREST = {
        name = "翡翠森林",
        description = "古老森林的深處，森林守護者在此沉睡",
        type = MapConfig.MAP_TYPES.DUNGEON,
        difficulty = "初級",
        requiredLevel = 1,
        maxLevel = 20,
        teamSize = {min = 3, max = 5},
        timeLimit = 1800, -- 30分鐘
        
        -- 地圖位置
        mapLocation = "Workspace.MAP.Forest",
        
        -- BOSS配置
        bosses = {
            -- 小BOSS
            {
                name = "狼王",
                type = "mini_boss",
                level = 8,
                health = 2000,
                position = Vector3.new(50, 5, 50),
                rewards = {
                    exp = 200,
                    coins = 150,
                    dropChance = {
                        common = 0.8,
                        uncommon = 0.15,
                        rare = 0.05
                    }
                }
            },
            {
                name = "熊王",
                type = "mini_boss", 
                level = 12,
                health = 3500,
                position = Vector3.new(-50, 5, -50),
                rewards = {
                    exp = 350,
                    coins = 250,
                    dropChance = {
                        common = 0.7,
                        uncommon = 0.2,
                        rare = 0.1
                    }
                }
            },
            -- 主BOSS
            {
                name = "森林守護者",
                type = "main_boss",
                level = 15,
                health = 8000,
                position = Vector3.new(0, 10, 0),
                rewards = {
                    exp = 800,
                    coins = 500,
                    gems = 10,
                    guaranteedDrop = true,
                    dropChance = {
                        uncommon = 0.4,
                        rare = 0.4,
                        epic = 0.15,
                        legendary = 0.05
                    }
                }
            }
        },
        
        -- 生成點
        spawnPoints = {
            Vector3.new(0, 5, -80),
            Vector3.new(-10, 5, -80),
            Vector3.new(10, 5, -80)
        },
        
        -- 環境設置
        environment = {
            lighting = "Forest",
            fogColor = Color3.fromRGB(100, 150, 100),
            fogStart = 100,
            fogEnd = 500,
            ambientSound = "ForestAmbient"
        }
    },
    
    ICE = {
        name = "極地冰窟",
        description = "永恆冰霜的領域，冰霜巨龍的巢穴",
        type = MapConfig.MAP_TYPES.DUNGEON,
        difficulty = "中級",
        requiredLevel = 15,
        maxLevel = 35,
        teamSize = {min = 4, max = 6},
        timeLimit = 2400, -- 40分鐘
        
        -- 地圖位置
        mapLocation = "Workspace.MAP.Ice",
        
        -- BOSS配置
        bosses = {
            -- 小BOSS
            {
                name = "冰魔法師",
                type = "mini_boss",
                level = 20,
                health = 4500,
                position = Vector3.new(60, 10, 30),
                rewards = {
                    exp = 450,
                    coins = 300,
                    dropChance = {
                        uncommon = 0.6,
                        rare = 0.3,
                        epic = 0.1
                    }
                }
            },
            {
                name = "雪怪王",
                type = "mini_boss",
                level = 25,
                health = 6000,
                position = Vector3.new(-60, 10, -30),
                rewards = {
                    exp = 600,
                    coins = 400,
                    dropChance = {
                        uncommon = 0.5,
                        rare = 0.35,
                        epic = 0.15
                    }
                }
            },
            -- 主BOSS
            {
                name = "冰霜巨龍",
                type = "main_boss",
                level = 30,
                health = 15000,
                position = Vector3.new(0, 20, 0),
                rewards = {
                    exp = 1500,
                    coins = 800,
                    gems = 25,
                    guaranteedDrop = true,
                    dropChance = {
                        rare = 0.3,
                        epic = 0.4,
                        legendary = 0.25,
                        mythic = 0.05
                    }
                }
            }
        },
        
        -- 生成點
        spawnPoints = {
            Vector3.new(0, 10, -100),
            Vector3.new(-15, 10, -100),
            Vector3.new(15, 10, -100),
            Vector3.new(-30, 10, -100),
            Vector3.new(30, 10, -100)
        },
        
        -- 環境設置
        environment = {
            lighting = "Arctic",
            fogColor = Color3.fromRGB(200, 220, 255),
            fogStart = 50,
            fogEnd = 300,
            ambientSound = "IceAmbient"
        }
    },
    
    LAVA = {
        name = "地獄熔爐",
        description = "烈焰與硫磺的世界，惡魔王的王座",
        type = MapConfig.MAP_TYPES.DUNGEON,
        difficulty = "高級",
        requiredLevel = 25,
        maxLevel = 50,
        teamSize = {min = 5, max = 8},
        timeLimit = 3600, -- 60分鐘
        
        -- 地圖位置
        mapLocation = "Workspace.MAP.Lava",
        
        -- BOSS配置
        bosses = {
            -- 小BOSS
            {
                name = "火焰巨人",
                type = "mini_boss",
                level = 35,
                health = 8000,
                position = Vector3.new(80, 15, 40),
                rewards = {
                    exp = 800,
                    coins = 500,
                    dropChance = {
                        rare = 0.4,
                        epic = 0.4,
                        legendary = 0.2
                    }
                }
            },
            {
                name = "岩漿元素",
                type = "mini_boss",
                level = 40,
                health = 10000,
                position = Vector3.new(-80, 15, -40),
                rewards = {
                    exp = 1000,
                    coins = 650,
                    dropChance = {
                        rare = 0.3,
                        epic = 0.45,
                        legendary = 0.25
                    }
                }
            },
            {
                name = "地獄犬王",
                type = "mini_boss",
                level = 42,
                health = 12000,
                position = Vector3.new(0, 15, 80),
                rewards = {
                    exp = 1200,
                    coins = 750,
                    dropChance = {
                        epic = 0.4,
                        legendary = 0.4,
                        mythic = 0.2
                    }
                }
            },
            -- 主BOSS
            {
                name = "熔岩惡魔王",
                type = "main_boss",
                level = 45,
                health = 25000,
                position = Vector3.new(0, 25, 0),
                rewards = {
                    exp = 2500,
                    coins = 1200,
                    gems = 50,
                    guaranteedDrop = true,
                    dropChance = {
                        epic = 0.2,
                        legendary = 0.4,
                        mythic = 0.35,
                        transcendent = 0.05
                    }
                }
            }
        },
        
        -- 生成點
        spawnPoints = {
            Vector3.new(0, 15, -120),
            Vector3.new(-20, 15, -120),
            Vector3.new(20, 15, -120),
            Vector3.new(-40, 15, -120),
            Vector3.new(40, 15, -120),
            Vector3.new(-60, 15, -120),
            Vector3.new(60, 15, -120)
        },
        
        -- 環境設置
        environment = {
            lighting = "Volcanic",
            fogColor = Color3.fromRGB(255, 100, 50),
            fogStart = 30,
            fogEnd = 200,
            ambientSound = "LavaAmbient"
        }
    }
}

-- 副本難度配置
MapConfig.DIFFICULTY_SETTINGS = {
    ["初級"] = {
        damageMultiplier = 1.0,
        healthMultiplier = 1.0,
        expMultiplier = 1.0,
        dropRateBonus = 0.0
    },
    ["中級"] = {
        damageMultiplier = 1.5,
        healthMultiplier = 1.8,
        expMultiplier = 1.5,
        dropRateBonus = 0.1
    },
    ["高級"] = {
        damageMultiplier = 2.0,
        healthMultiplier = 2.5,
        expMultiplier = 2.0,
        dropRateBonus = 0.2
    }
}

-- 組隊要求
MapConfig.TEAM_REQUIREMENTS = {
    FOREST = {
        minPlayers = 3,
        maxPlayers = 5,
        recommendedLevel = {min = 1, max = 20},
        roles = {
            tank = {min = 1, max = 2},
            dps = {min = 1, max = 3},
            support = {min = 0, max = 2}
        }
    },
    ICE = {
        minPlayers = 4,
        maxPlayers = 6,
        recommendedLevel = {min = 15, max = 35},
        roles = {
            tank = {min = 1, max = 2},
            dps = {min = 2, max = 4},
            support = {min = 1, max = 2}
        }
    },
    LAVA = {
        minPlayers = 5,
        maxPlayers = 8,
        recommendedLevel = {min = 25, max = 50},
        roles = {
            tank = {min = 2, max = 3},
            dps = {min = 2, max = 4},
            support = {min = 1, max = 3}
        }
    }
}

return MapConfig
