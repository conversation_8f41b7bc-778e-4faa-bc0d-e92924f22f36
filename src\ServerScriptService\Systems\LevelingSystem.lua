-- 升級系統 - 處理英雄和寵物的升級邏輯
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local PetConfig = require(game.ReplicatedStorage.Configuration.PetConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local LevelingSystem = {}

function LevelingSystem:step(world, state)
    -- 處理英雄升級
    self:processHeroLeveling(world, state)
    
    -- 處理寵物升級
    self:processPetLeveling(world, state)
end

function LevelingSystem:processHeroLeveling(world, state)
    for entityId, hero, player in world:query(
        Components.HeroComponent,
        Components.PlayerComponent
    ) do
        -- 檢查是否有足夠經驗值升級
        local requiredExp = Utils.calculateExpForLevel(hero.level + 1)
        
        if hero.experience >= requiredExp and hero.level < GameConfig.HERO.MAX_LEVEL then
            self:levelUpHero(world, entityId, hero, player)
        end
    end
end

function LevelingSystem:processPetLeveling(world, state)
    for entityId, pet, stats in world:query(
        Components.PetComponent,
        Components.PetStatsComponent
    ) do
        -- 檢查寵物是否有足夠經驗值升級
        local requiredExp = self:calculatePetExpForLevel(pet.level + 1)
        
        if pet.experience >= requiredExp and pet.level < GameConfig.PET.MAX_LEVEL then
            self:levelUpPet(world, entityId, pet, stats)
        end
    end
end

function LevelingSystem:levelUpHero(world, entityId, hero, player)
    local newLevel = hero.level + 1
    
    -- 計算新屬性 (韓式慢速成長)
    local newMaxHealth = GameConfig.HERO.BASE_HEALTH + (newLevel * GameConfig.HERO.HEALTH_PER_LEVEL)
    local newAttack = GameConfig.HERO.BASE_ATTACK + (newLevel * GameConfig.HERO.ATTACK_PER_LEVEL)
    local newDefense = GameConfig.HERO.BASE_DEFENSE + (newLevel * GameConfig.HERO.DEFENSE_PER_LEVEL)
    
    -- 計算剩餘經驗值
    local requiredExp = Utils.calculateExpForLevel(newLevel)
    local remainingExp = hero.experience - requiredExp
    
    -- 更新英雄組件
    world:insert(entityId, Components.HeroComponent({
        level = newLevel,
        experience = remainingExp,
        maxHealth = newMaxHealth,
        currentHealth = newMaxHealth, -- 升級時完全回復血量
        attack = newAttack,
        defense = newDefense,
        critRate = hero.critRate,
        critDamage = hero.critDamage
    }))
    
    -- 更新血量組件
    world:insert(entityId, Components.HealthComponent({
        current = newMaxHealth,
        maximum = newMaxHealth,
        regeneration = 1,
        lastRegenTime = tick()
    }))
    
    -- 檢查區域解鎖
    self:checkZoneUnlocks(world, entityId, newLevel)
    
    -- 通知升級事件
    self:notifyHeroLevelUp(world, entityId, player, newLevel, {
        maxHealth = newMaxHealth,
        attack = newAttack,
        defense = newDefense
    })
    
    print("🎉 英雄升級:", player.displayName, "等級:", newLevel)
end

function LevelingSystem:levelUpPet(world, entityId, pet, stats)
    local newLevel = pet.level + 1
    
    -- 獲取寵物配置
    local speciesConfig = PetConfig.Species[pet.speciesId]
    if not speciesConfig then
        warn("找不到寵物配置:", pet.speciesId)
        return
    end
    
    -- 計算新屬性
    local newStats = PetConfig:CalculatePetStats(pet.speciesId, newLevel, pet.rarity)
    if not newStats then
        warn("無法計算寵物屬性:", pet.speciesId, newLevel, pet.rarity)
        return
    end
    
    -- 計算剩餘經驗值
    local requiredExp = self:calculatePetExpForLevel(newLevel)
    local remainingExp = pet.experience - requiredExp
    
    -- 更新寵物組件
    world:insert(entityId, Components.PetComponent({
        speciesId = pet.speciesId,
        name = pet.name,
        rarity = pet.rarity,
        level = newLevel,
        experience = remainingExp,
        isShiny = pet.isShiny,
        obtainedAt = pet.obtainedAt,
        ownerId = pet.ownerId
    }))
    
    -- 更新寵物屬性組件
    world:insert(entityId, Components.PetStatsComponent({
        health = newStats.health,
        attack = newStats.attack,
        defense = newStats.defense,
        speed = newStats.speed,
        rarityMultiplier = GameConfig.PET.RARITY[string.upper(pet.rarity)].multiplier
    }))
    
    -- 通知寵物升級事件
    self:notifyPetLevelUp(world, entityId, pet, newLevel, newStats)
    
    print("🐾 寵物升級:", pet.name, "等級:", newLevel)
end

function LevelingSystem:calculatePetExpForLevel(level)
    -- 寵物經驗需求 (比英雄簡單一些)
    if level <= 10 then
        return math.floor(50 * math.pow(1.15, level - 1))
    elseif level <= 30 then
        return math.floor(500 * math.pow(1.25, level - 10))
    elseif level <= 60 then
        return math.floor(10000 * math.pow(1.35, level - 30))
    else
        return math.floor(500000 * math.pow(1.45, level - 60))
    end
end

function LevelingSystem:checkZoneUnlocks(world, entityId, heroLevel)
    local zone = world:get(entityId, Components.ZoneComponent)
    if not zone then return end
    
    local allowedZones = Utils.deepCopy(zone.allowedZones)
    local hasNewUnlock = false
    
    -- 檢查各區域解鎖條件
    for zoneName, zoneConfig in pairs(GameConfig.ZONES) do
        local zoneKey = string.lower(zoneName)
        if heroLevel >= zoneConfig.unlockLevel then
            -- 檢查是否已解鎖
            local alreadyUnlocked = false
            for _, unlockedZone in ipairs(allowedZones) do
                if unlockedZone == zoneKey then
                    alreadyUnlocked = true
                    break
                end
            end
            
            if not alreadyUnlocked then
                table.insert(allowedZones, zoneKey)
                hasNewUnlock = true
                print("🗺️ 新區域解鎖:", zoneConfig.name, "需要等級:", zoneConfig.unlockLevel)
            end
        end
    end
    
    -- 更新區域組件
    if hasNewUnlock then
        world:insert(entityId, Components.ZoneComponent({
            currentZone = zone.currentZone,
            lastZoneChange = zone.lastZoneChange,
            allowedZones = allowedZones
        }))
    end
end

function LevelingSystem:notifyHeroLevelUp(world, entityId, player, newLevel, newStats)
    -- 獲取 PlayerService 來發送事件
    local Knit = require(game.ReplicatedStorage.Packages.Knit)
    local PlayerService = Knit.GetService("PlayerService")
    
    if PlayerService then
        -- 找到對應的 Roblox Player 對象
        local robloxPlayer = game.Players:GetPlayerByUserId(player.userId)
        if robloxPlayer then
            PlayerService.Client.OnLevelUp:Fire(robloxPlayer, {
                type = "hero",
                newLevel = newLevel,
                newStats = newStats,
                timestamp = tick()
            })
        end
    end
end

function LevelingSystem:notifyPetLevelUp(world, entityId, pet, newLevel, newStats)
    -- 獲取 PetService 來發送事件
    local Knit = require(game.ReplicatedStorage.Packages.Knit)
    local success, PetService = pcall(function()
        return Knit.GetService("PetService")
    end)
    
    if success and PetService then
        -- 找到寵物主人
        local robloxPlayer = game.Players:GetPlayerByUserId(pet.ownerId)
        if robloxPlayer then
            PetService.Client.OnPetLevelUp:Fire(robloxPlayer, {
                petId = entityId,
                speciesId = pet.speciesId,
                name = pet.name,
                newLevel = newLevel,
                newStats = newStats,
                timestamp = tick()
            })
        end
    end
end

-- 給英雄增加經驗值
function LevelingSystem:giveHeroExperience(world, entityId, amount)
    local hero = world:get(entityId, Components.HeroComponent)
    if not hero then
        return false
    end
    
    world:insert(entityId, Components.HeroComponent({
        level = hero.level,
        experience = hero.experience + amount,
        maxHealth = hero.maxHealth,
        currentHealth = hero.currentHealth,
        attack = hero.attack,
        defense = hero.defense,
        critRate = hero.critRate,
        critDamage = hero.critDamage
    }))
    
    return true
end

-- 給寵物增加經驗值
function LevelingSystem:givePetExperience(world, entityId, amount)
    local pet = world:get(entityId, Components.PetComponent)
    if not pet then
        return false
    end
    
    world:insert(entityId, Components.PetComponent({
        speciesId = pet.speciesId,
        name = pet.name,
        rarity = pet.rarity,
        level = pet.level,
        experience = pet.experience + amount,
        isShiny = pet.isShiny,
        obtainedAt = pet.obtainedAt,
        ownerId = pet.ownerId
    }))
    
    return true
end

-- 計算升級所需經驗值
function LevelingSystem:getExpRequiredForHeroLevel(level)
    return Utils.calculateExpForLevel(level)
end

function LevelingSystem:getExpRequiredForPetLevel(level)
    return self:calculatePetExpForLevel(level)
end

-- 獲取升級進度百分比
function LevelingSystem:getHeroLevelProgress(hero)
    if hero.level >= GameConfig.HERO.MAX_LEVEL then
        return 1.0
    end
    
    local requiredExp = self:getExpRequiredForHeroLevel(hero.level + 1)
    return requiredExp > 0 and (hero.experience / requiredExp) or 0
end

function LevelingSystem:getPetLevelProgress(pet)
    if pet.level >= GameConfig.PET.MAX_LEVEL then
        return 1.0
    end
    
    local requiredExp = self:getExpRequiredForPetLevel(pet.level + 1)
    return requiredExp > 0 and (pet.experience / requiredExp) or 0
end

return LevelingSystem
