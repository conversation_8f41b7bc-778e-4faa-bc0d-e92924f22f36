-- 狀態管理系統 - 使用 Fusion 管理全局遊戲狀態
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)

local StateManager = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

-- 玩家數據狀態
StateManager.PlayerData = scope:Value({
    userId = 0,
    displayName = "",
    joinTime = 0,
    isOnline = false
})

-- 英雄狀態
StateManager.HeroStats = scope:Value({
    level = 1,
    experience = 0,
    maxHealth = 150,
    currentHealth = 150,
    attack = 15,
    defense = 8,
    critRate = 0.05,
    critDamage = 1.5
})

-- 貨幣狀態
StateManager.Currencies = scope:Value({
    coins = 1000,
    gems = 20,
    robux = 0,
    experience = 0
})

-- 寵物狀態
StateManager.Pets = scope:Value({
    activePets = {},
    allPets = {},
    selectedPet = nil
})

-- 戰鬥狀態
StateManager.Combat = scope:Value({
    isInCombat = false,
    target = nil,
    combatStartTime = 0,
    lastAttackTime = 0
})

-- UI 狀態
StateManager.UI = scope:Value({
    currentScreen = "main",
    isLoading = false,
    notifications = {},
    inventoryOpen = false,
    petMenuOpen = false,
    questMenuOpen = false,
    mapOpen = false,
    settingsOpen = false
})

-- 任務狀態
StateManager.Quests = scope:Value({
    dailyQuests = {},
    weeklyQuests = {},
    monthlyQuests = {},
    completedToday = 0
})

-- 圖鑑狀態
StateManager.Pokedex = scope:Value({
    encounteredSpecies = {},
    ownedSpecies = {},
    completionRate = 0,
    totalSpecies = 97
})

-- VIP 狀態
StateManager.VIP = scope:Value({
    level = 0,
    totalSpent = 0,
    premiumActive = false,
    premiumExpiry = 0,
    benefits = {}
})

-- 設置狀態
StateManager.Settings = scope:Value({
    soundEnabled = true,
    musicEnabled = true,
    notificationsEnabled = true,
    language = "zh-TW",
    graphics = "medium",
    masterVolume = 1.0,
    musicVolume = 0.7,
    sfxVolume = 0.8
})

-- 計算屬性
StateManager.HealthPercentage = scope:Computed(function(use)
    local hero = use(StateManager.HeroStats)
    if hero.maxHealth and hero.maxHealth > 0 then
        return hero.currentHealth / hero.maxHealth
    end
    return 1
end)

StateManager.ExpPercentage = scope:Computed(function(use)
    local hero = use(StateManager.HeroStats)
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
    
    if hero.level >= GameConfig.HERO.MAX_LEVEL then
        return 1
    end
    
    local Utils = require(game.ReplicatedStorage.Shared.Utils)
    local requiredExp = Utils.calculateExpForLevel(hero.level + 1)
    return requiredExp > 0 and (hero.experience / requiredExp) or 0
end)

StateManager.FormattedCurrencies = scope:Computed(function(use)
    local currencies = use(StateManager.Currencies)
    local Utils = require(game.ReplicatedStorage.Shared.Utils)
    
    return {
        coins = Utils.formatNumber(currencies.coins),
        gems = Utils.formatNumber(currencies.gems),
        robux = Utils.formatNumber(currencies.robux)
    }
end)

StateManager.ActivePetCount = scope:Computed(function(use)
    local pets = use(StateManager.Pets)
    return #pets.activePets
end)

StateManager.TotalPetCount = scope:Computed(function(use)
    local pets = use(StateManager.Pets)
    return #pets.allPets
end)

StateManager.QuestProgress = scope:Computed(function(use)
    local quests = use(StateManager.Quests)
    local totalQuests = #quests.dailyQuests + #quests.weeklyQuests + #quests.monthlyQuests
    local completedQuests = 0
    
    for _, quest in ipairs(quests.dailyQuests) do
        if quest.completed then
            completedQuests = completedQuests + 1
        end
    end
    
    for _, quest in ipairs(quests.weeklyQuests) do
        if quest.completed then
            completedQuests = completedQuests + 1
        end
    end
    
    for _, quest in ipairs(quests.monthlyQuests) do
        if quest.completed then
            completedQuests = completedQuests + 1
        end
    end
    
    return {
        completed = completedQuests,
        total = totalQuests,
        percentage = totalQuests > 0 and (completedQuests / totalQuests) or 0
    }
end)

-- 更新方法
function StateManager:UpdatePlayerData(newData)
    local current = Fusion.peek(self.PlayerData)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newData) do
        merged[key] = value
    end
    
    self.PlayerData:set(merged)
end

function StateManager:UpdateHeroStats(newStats)
    local current = Fusion.peek(self.HeroStats)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newStats) do
        merged[key] = value
    end
    
    self.HeroStats:set(merged)
end

function StateManager:UpdateCurrencies(newCurrencies)
    local current = Fusion.peek(self.Currencies)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newCurrencies) do
        merged[key] = value
    end
    
    self.Currencies:set(merged)
end

function StateManager:UpdatePets(newPets)
    local current = Fusion.peek(self.Pets)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newPets) do
        merged[key] = value
    end
    
    self.Pets:set(merged)
end

function StateManager:UpdateCombat(newCombat)
    local current = Fusion.peek(self.Combat)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newCombat) do
        merged[key] = value
    end
    
    self.Combat:set(merged)
end

function StateManager:UpdateUI(newUI)
    local current = Fusion.peek(self.UI)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newUI) do
        merged[key] = value
    end
    
    self.UI:set(merged)
end

function StateManager:UpdateQuests(newQuests)
    local current = Fusion.peek(self.Quests)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newQuests) do
        merged[key] = value
    end
    
    self.Quests:set(merged)
end

function StateManager:UpdatePokedex(newPokedex)
    local current = Fusion.peek(self.Pokedex)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newPokedex) do
        merged[key] = value
    end
    
    self.Pokedex:set(merged)
end

function StateManager:UpdateVIP(newVIP)
    local current = Fusion.peek(self.VIP)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newVIP) do
        merged[key] = value
    end
    
    self.VIP:set(merged)
end

function StateManager:UpdateSettings(newSettings)
    local current = Fusion.peek(self.Settings)
    local merged = {}
    
    for key, value in pairs(current) do
        merged[key] = value
    end
    
    for key, value in pairs(newSettings) do
        merged[key] = value
    end
    
    self.Settings:set(merged)
end

-- 獲取當前值的方法
function StateManager:GetPlayerData()
    return Fusion.peek(self.PlayerData)
end

function StateManager:GetHeroStats()
    return Fusion.peek(self.HeroStats)
end

function StateManager:GetCurrencies()
    return Fusion.peek(self.Currencies)
end

function StateManager:GetPets()
    return Fusion.peek(self.Pets)
end

function StateManager:GetCombat()
    return Fusion.peek(self.Combat)
end

function StateManager:GetUI()
    return Fusion.peek(self.UI)
end

function StateManager:GetQuests()
    return Fusion.peek(self.Quests)
end

function StateManager:GetPokedex()
    return Fusion.peek(self.Pokedex)
end

function StateManager:GetVIP()
    return Fusion.peek(self.VIP)
end

function StateManager:GetSettings()
    return Fusion.peek(self.Settings)
end

-- 清理方法
function StateManager:Cleanup()
    scope:doCleanup()
end

return StateManager
