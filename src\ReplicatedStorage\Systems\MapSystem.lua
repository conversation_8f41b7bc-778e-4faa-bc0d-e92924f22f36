-- 地圖系統 - 城堡主地圖 + 三個組隊副本統一管理
local MapSystem = {}

-- 依賴
local MapConfig = require(game.ReplicatedStorage.Configuration.MapConfig)
local TeamManager = require(game.ReplicatedStorage.Shared.TeamManager)
local DungeonManager = require(game.ReplicatedStorage.Shared.DungeonManager)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 系統狀態
MapSystem.isInitialized = false
MapSystem.currentPlayers = {} -- 玩家當前位置追蹤
MapSystem.activeDungeons = {} -- 活躍副本列表

-- 地圖位置類型
MapSystem.LOCATION_TYPES = {
    CASTLE = "castle",
    DUNGEON = "dungeon"
}

-- 初始化地圖系統
function MapSystem:Initialize()
    if self.isInitialized then
        warn("⚠️ 地圖系統已經初始化")
        return true
    end
    
    print("🗺️ 初始化地圖系統...")
    
    -- 初始化子系統
    local success = true
    
    -- 初始化組隊管理器
    local teamSuccess = pcall(function()
        TeamManager:Initialize()
    end)
    if not teamSuccess then
        warn("❌ 組隊管理器初始化失敗")
        success = false
    end
    
    -- 初始化副本管理器
    local dungeonSuccess = pcall(function()
        DungeonManager:Initialize()
    end)
    if not dungeonSuccess then
        warn("❌ 副本管理器初始化失敗")
        success = false
    end
    
    if success then
        self.isInitialized = true
        self:SetupEventHandlers()
        
        print("✅ 地圖系統初始化完成")
        print("🏰 主城堡:", MapConfig.MAIN_CASTLE.name)
        print("🏰 可用副本:")
        for dungeonId, dungeon in pairs(MapConfig.DUNGEONS) do
            print(string.format("  - %s (%s)", dungeon.name, dungeon.difficulty))
        end
        
        -- 觸發系統初始化事件
        EventManager:Fire(EventManager.EventTypes.SYSTEM_INITIALIZED, {
            system = "MapSystem",
            dungeonCount = self:GetDungeonCount()
        })
    else
        warn("❌ 地圖系統初始化失敗")
    end
    
    return success
end

-- 設置事件處理器
function MapSystem:SetupEventHandlers()
    -- 監聽玩家加入事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_JOINED, function(data)
        self:OnPlayerJoined(data.player)
    end)
    
    -- 監聽玩家離開事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_LEFT, function(data)
        self:OnPlayerLeft(data.player)
    end)
    
    -- 監聽副本開始事件
    EventManager:Connect(EventManager.EventTypes.DUNGEON_STARTED, function(data)
        self:OnDungeonStarted(data)
    end)
    
    -- 監聽副本完成事件
    EventManager:Connect(EventManager.EventTypes.DUNGEON_COMPLETED, function(data)
        self:OnDungeonCompleted(data)
    end)
end

-- 玩家加入事件處理
function MapSystem:OnPlayerJoined(player)
    -- 將玩家放置在主城堡
    self:TeleportToMainCastle(player)
    
    print(string.format("🏰 %s 進入了主城堡", player.Name))
end

-- 玩家離開事件處理
function MapSystem:OnPlayerLeft(player)
    -- 清理玩家數據
    self.currentPlayers[player.UserId] = nil
    
    -- 如果玩家在隊伍中，讓其離開隊伍
    TeamManager:LeaveTeam(player)
    
    print(string.format("👋 %s 離開了遊戲", player.Name))
end

-- 副本開始事件處理
function MapSystem:OnDungeonStarted(data)
    self.activeDungeons[data.instanceId] = {
        instanceId = data.instanceId,
        dungeonType = data.dungeon,
        teamId = data.teamId,
        startTime = tick()
    }
    
    print(string.format("🏰 副本 %s 開始 (實例: %s)", data.dungeon, data.instanceId))
end

-- 副本完成事件處理
function MapSystem:OnDungeonCompleted(data)
    -- 移除活躍副本記錄
    self.activeDungeons[data.instanceId] = nil
    
    print(string.format("🏆 副本實例 %s 完成", data.instanceId))
end

-- 傳送到主城堡
function MapSystem:TeleportToMainCastle(player)
    if not self.isInitialized then
        return false, "地圖系統未初始化"
    end
    
    -- 更新玩家位置記錄
    self.currentPlayers[player.UserId] = {
        location = self.LOCATION_TYPES.CASTLE,
        area = "SPAWN",
        joinTime = tick()
    }
    
    -- 實際傳送邏輯 (這裡應該設置玩家位置)
    local spawnArea = MapConfig.MAIN_CASTLE.areas.SPAWN
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        player.Character.HumanoidRootPart.CFrame = CFrame.new(spawnArea.position)
    end
    
    -- 觸發傳送事件
    EventManager:Fire(EventManager.EventTypes.PLAYER_TELEPORTED, {
        player = player,
        destination = "MainCastle",
        area = "SPAWN"
    })
    
    return true, nil
end

-- 創建隊伍
function MapSystem:CreateTeam(player, targetDungeon)
    if not self.isInitialized then
        return nil, "地圖系統未初始化"
    end
    
    -- 檢查玩家是否在主城堡
    local playerLocation = self.currentPlayers[player.UserId]
    if not playerLocation or playerLocation.location ~= self.LOCATION_TYPES.CASTLE then
        return nil, "只能在主城堡創建隊伍"
    end
    
    -- 檢查副本是否存在
    if not MapConfig.DUNGEONS[targetDungeon] then
        return nil, "副本不存在"
    end
    
    return TeamManager:CreateTeam(player, targetDungeon)
end

-- 加入隊伍
function MapSystem:JoinTeam(player, teamId)
    if not self.isInitialized then
        return false, "地圖系統未初始化"
    end
    
    -- 檢查玩家是否在主城堡
    local playerLocation = self.currentPlayers[player.UserId]
    if not playerLocation or playerLocation.location ~= self.LOCATION_TYPES.CASTLE then
        return false, "只能在主城堡加入隊伍"
    end
    
    return TeamManager:JoinTeam(player, teamId)
end

-- 開始副本
function MapSystem:StartDungeon(player)
    if not self.isInitialized then
        return false, "地圖系統未初始化"
    end
    
    -- 獲取玩家的隊伍
    local team = TeamManager:GetPlayerTeam(player)
    if not team then
        return false, "玩家不在任何隊伍中"
    end
    
    -- 檢查是否是隊長
    if team.leader.UserId ~= player.UserId then
        return false, "只有隊長可以開始副本"
    end
    
    -- 檢查隊伍狀態
    if team.status ~= TeamManager.TEAM_STATUS.READY then
        return false, "隊伍未準備就緒"
    end
    
    -- 創建副本實例
    local instanceId, error = DungeonManager:CreateInstance(team.id, team.targetDungeon, team.members)
    if not instanceId then
        return false, error
    end
    
    -- 開始副本
    local success, startError = DungeonManager:StartInstance(instanceId)
    if not success then
        return false, startError
    end
    
    -- 傳送隊伍成員到副本
    for _, member in ipairs(team.members) do
        self:TeleportToDungeon(member, team.targetDungeon, instanceId)
    end
    
    -- 更新隊伍狀態
    TeamManager:StartDungeon(team.id)
    
    return true, instanceId
end

-- 傳送到副本
function MapSystem:TeleportToDungeon(player, dungeonType, instanceId)
    local dungeonConfig = MapConfig.DUNGEONS[dungeonType]
    if not dungeonConfig then
        return false, "副本不存在"
    end
    
    -- 更新玩家位置記錄
    self.currentPlayers[player.UserId] = {
        location = self.LOCATION_TYPES.DUNGEON,
        dungeonType = dungeonType,
        instanceId = instanceId,
        joinTime = tick()
    }
    
    -- 實際傳送邏輯
    local spawnPoint = dungeonConfig.spawnPoints[1] -- 使用第一個生成點
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        player.Character.HumanoidRootPart.CFrame = CFrame.new(spawnPoint)
    end
    
    -- 觸發傳送事件
    EventManager:Fire(EventManager.EventTypes.PLAYER_TELEPORTED, {
        player = player,
        destination = dungeonType,
        instanceId = instanceId
    })
    
    print(string.format("🏰 %s 傳送到副本 %s (實例: %s)", player.Name, dungeonType, instanceId))
    
    return true, nil
end

-- 獲取可用隊伍列表
function MapSystem:GetAvailableTeams(player, targetDungeon)
    if not self.isInitialized then
        return {}
    end
    
    return TeamManager:GetAvailableTeams(player, targetDungeon)
end

-- 獲取玩家當前位置
function MapSystem:GetPlayerLocation(player)
    return self.currentPlayers[player.UserId]
end

-- 獲取副本數量
function MapSystem:GetDungeonCount()
    local count = 0
    for _ in pairs(MapConfig.DUNGEONS) do
        count = count + 1
    end
    return count
end

-- 獲取活躍副本數量
function MapSystem:GetActiveDungeonCount()
    local count = 0
    for _ in pairs(self.activeDungeons) do
        count = count + 1
    end
    return count
end

-- 獲取主城堡玩家數量
function MapSystem:GetCastlePlayerCount()
    local count = 0
    for _, location in pairs(self.currentPlayers) do
        if location.location == self.LOCATION_TYPES.CASTLE then
            count = count + 1
        end
    end
    return count
end

-- 獲取系統統計
function MapSystem:GetSystemStats()
    if not self.isInitialized then
        return {}
    end
    
    local teamStats = TeamManager:GetStats()
    local dungeonStats = DungeonManager:GetStats()
    
    return {
        totalPlayers = self:GetTotalPlayerCount(),
        castlePlayers = self:GetCastlePlayerCount(),
        dungeonPlayers = self:GetDungeonPlayerCount(),
        activeDungeons = self:GetActiveDungeonCount(),
        totalTeams = teamStats.totalTeams,
        readyTeams = teamStats.readyTeams,
        completedDungeons = dungeonStats.completedInstances,
        failedDungeons = dungeonStats.failedInstances
    }
end

-- 獲取總玩家數量
function MapSystem:GetTotalPlayerCount()
    local count = 0
    for _ in pairs(self.currentPlayers) do
        count = count + 1
    end
    return count
end

-- 獲取副本中玩家數量
function MapSystem:GetDungeonPlayerCount()
    local count = 0
    for _, location in pairs(self.currentPlayers) do
        if location.location == self.LOCATION_TYPES.DUNGEON then
            count = count + 1
        end
    end
    return count
end

-- 調試信息
function MapSystem:Debug()
    print("🗺️ 地圖系統調試信息:")
    print("  初始化狀態:", self.isInitialized)
    
    local stats = self:GetSystemStats()
    print("  總玩家數:", stats.totalPlayers)
    print("  主城堡玩家:", stats.castlePlayers)
    print("  副本中玩家:", stats.dungeonPlayers)
    print("  活躍副本數:", stats.activeDungeons)
    print("  總隊伍數:", stats.totalTeams)
    print("  準備就緒隊伍:", stats.readyTeams)
    
    print("  可用副本:")
    for dungeonId, dungeon in pairs(MapConfig.DUNGEONS) do
        print(string.format("    %s: %s (%s)", dungeonId, dungeon.name, dungeon.difficulty))
    end
    
    -- 子系統調試
    TeamManager:Debug()
    DungeonManager:Debug()
end

return MapSystem
