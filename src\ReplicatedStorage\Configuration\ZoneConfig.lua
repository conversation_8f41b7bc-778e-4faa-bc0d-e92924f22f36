-- 區域配置 - 定義遊戲中的所有區域
local ZoneConfig = {}

-- 區域類型定義
ZoneConfig.ZoneTypes = {
    SAFE = "safe",           -- 安全區域
    HUNTING = "hunting",     -- 狩獵區域
    PVP = "pvp",            -- PvP 區域
    EVENT = "event",        -- 特殊事件區域
    BOSS = "boss",          -- Boss 區域
    TELEPORT = "teleport"   -- 傳送區域
}

-- 區域定義
ZoneConfig.Zones = {
    -- 城堡安全區域
    castle = {
        id = "castle",
        name = "魔法城堡",
        type = ZoneConfig.ZoneTypes.SAFE,
        description = "安全的起始區域，無法進行戰鬥",
        level = 1,
        
        -- 區域設置
        settings = {
            allowPvP = false,
            allowCombat = false,
            allowPetSummoning = true,
            spawnProtection = true,
            musicTrack = "castle",
            ambientSounds = {},
            lighting = {
                brightness = 1.0,
                colorShift = Color3.fromRGB(255, 255, 255)
            }
        },
        
        -- 區域獎勵和特殊功能
        features = {
            shops = { "basic", "pet", "upgrade" },
            npcs = { "trainer", "merchant", "quest_giver" },
            teleporters = { "forest", "ice", "lava" },
            spawns = { "player_spawn" }
        },
        
        -- 區域邊界 (將由 ZonePlus 使用)
        boundaries = {
            type = "part", -- 或 "region3"
            partName = "CastleZone" -- 在 Workspace 中的 Part 名稱
        }
    },
    
    -- 森林狩獵區域
    forest = {
        id = "forest",
        name = "翡翠森林",
        type = ZoneConfig.ZoneTypes.HUNTING,
        description = "適合新手的狩獵區域",
        level = 1,
        
        settings = {
            allowPvP = false,
            allowCombat = true,
            allowPetSummoning = true,
            spawnProtection = false,
            musicTrack = "forest",
            ambientSounds = { "wind", "birds" },
            lighting = {
                brightness = 0.8,
                colorShift = Color3.fromRGB(200, 255, 200)
            }
        },
        
        -- 怪物生成配置
        monsters = {
            spawnRate = 0.5, -- 每秒生成機率
            maxCount = 20,   -- 最大怪物數量
            species = {
                { id = "forest_slime", weight = 40, level = { 1, 3 } },
                { id = "forest_wolf", weight = 30, level = { 2, 5 } },
                { id = "forest_bear", weight = 20, level = { 4, 8 } },
                { id = "forest_treant", weight = 10, level = { 6, 10 } }
            }
        },
        
        -- 寵物生成配置
        pets = {
            spawnRate = 0.1, -- 每秒生成機率
            maxCount = 5,
            species = {
                { id = "forest_fairy", weight = 50, rarity = "common" },
                { id = "forest_sprite", weight = 30, rarity = "uncommon" },
                { id = "forest_unicorn", weight = 15, rarity = "rare" },
                { id = "forest_dragon", weight = 5, rarity = "legendary" }
            }
        },
        
        features = {
            teleporters = { "castle" },
            resources = { "wood", "herbs" },
            secrets = { "hidden_cave" }
        },
        
        boundaries = {
            type = "part",
            partName = "ForestZone"
        }
    },
    
    -- 冰雪狩獵區域
    ice = {
        id = "ice",
        name = "冰霜高原",
        type = ZoneConfig.ZoneTypes.HUNTING,
        description = "中級狩獵區域，怪物更強",
        level = 10,
        
        settings = {
            allowPvP = false,
            allowCombat = true,
            allowPetSummoning = true,
            spawnProtection = false,
            musicTrack = "ice",
            ambientSounds = { "wind", "ice_crack" },
            lighting = {
                brightness = 0.9,
                colorShift = Color3.fromRGB(200, 220, 255)
            }
        },
        
        monsters = {
            spawnRate = 0.6,
            maxCount = 25,
            species = {
                { id = "ice_golem", weight = 35, level = { 8, 15 } },
                { id = "frost_wolf", weight = 30, level = { 10, 18 } },
                { id = "ice_giant", weight = 25, level = { 15, 25 } },
                { id = "blizzard_dragon", weight = 10, level = { 20, 30 } }
            }
        },
        
        pets = {
            spawnRate = 0.08,
            maxCount = 4,
            species = {
                { id = "ice_penguin", weight = 45, rarity = "common" },
                { id = "frost_fox", weight = 30, rarity = "uncommon" },
                { id = "ice_phoenix", weight = 20, rarity = "rare" },
                { id = "crystal_dragon", weight = 5, rarity = "legendary" }
            }
        },
        
        features = {
            teleporters = { "castle", "forest" },
            resources = { "ice_crystal", "frozen_herbs" },
            secrets = { "ice_cave", "frozen_temple" }
        },
        
        boundaries = {
            type = "part",
            partName = "IceZone"
        }
    },
    
    -- 熔岩狩獵區域
    lava = {
        id = "lava",
        name = "烈焰火山",
        type = ZoneConfig.ZoneTypes.HUNTING,
        description = "高級狩獵區域，極度危險",
        level = 25,
        
        settings = {
            allowPvP = false,
            allowCombat = true,
            allowPetSummoning = true,
            spawnProtection = false,
            musicTrack = "lava",
            ambientSounds = { "fire", "lava_bubble" },
            lighting = {
                brightness = 0.7,
                colorShift = Color3.fromRGB(255, 200, 150)
            }
        },
        
        monsters = {
            spawnRate = 0.8,
            maxCount = 30,
            species = {
                { id = "lava_elemental", weight = 30, level = { 20, 35 } },
                { id = "fire_demon", weight = 25, level = { 25, 40 } },
                { id = "magma_giant", weight = 25, level = { 30, 45 } },
                { id = "inferno_dragon", weight = 20, level = { 35, 50 } }
            }
        },
        
        pets = {
            spawnRate = 0.05,
            maxCount = 3,
            species = {
                { id = "fire_salamander", weight = 40, rarity = "uncommon" },
                { id = "lava_hound", weight = 30, rarity = "rare" },
                { id = "phoenix", weight = 25, rarity = "epic" },
                { id = "inferno_dragon", weight = 5, rarity = "mythic" }
            }
        },
        
        features = {
            teleporters = { "castle", "ice" },
            resources = { "fire_crystal", "molten_ore" },
            secrets = { "volcano_core", "fire_temple" }
        },
        
        boundaries = {
            type = "part",
            partName = "LavaZone"
        }
    },
    
    -- PvP 競技場
    pvp_arena = {
        id = "pvp_arena",
        name = "競技場",
        type = ZoneConfig.ZoneTypes.PVP,
        description = "玩家對戰區域",
        level = 5,
        
        settings = {
            allowPvP = true,
            allowCombat = true,
            allowPetSummoning = true,
            spawnProtection = false,
            musicTrack = "combat",
            ambientSounds = { "crowd_cheer" },
            lighting = {
                brightness = 1.0,
                colorShift = Color3.fromRGB(255, 255, 255)
            }
        },
        
        pvp = {
            matchmaking = true,
            teamSize = { 1, 2, 4 }, -- 支援的隊伍大小
            rewards = {
                winner = { coins = 500, gems = 10, experience = 100 },
                participant = { coins = 100, experience = 25 }
            }
        },
        
        features = {
            teleporters = { "castle" },
            spawns = { "team_a_spawn", "team_b_spawn" }
        },
        
        boundaries = {
            type = "part",
            partName = "PvPArena"
        }
    },
    
    -- 特殊事件區域
    event_plaza = {
        id = "event_plaza",
        name = "活動廣場",
        type = ZoneConfig.ZoneTypes.EVENT,
        description = "特殊活動舉辦地",
        level = 1,
        
        settings = {
            allowPvP = false,
            allowCombat = false,
            allowPetSummoning = true,
            spawnProtection = true,
            musicTrack = "event",
            ambientSounds = { "celebration" },
            lighting = {
                brightness = 1.2,
                colorShift = Color3.fromRGB(255, 255, 200)
            }
        },
        
        events = {
            types = { "pet_parade", "treasure_hunt", "boss_raid" },
            schedule = "weekend", -- 週末活動
            rewards = {
                participation = { coins = 200, gems = 5 },
                completion = { coins = 1000, gems = 25, experience = 500 }
            }
        },
        
        features = {
            teleporters = { "castle" },
            npcs = { "event_coordinator", "reward_vendor" }
        },
        
        boundaries = {
            type = "part",
            partName = "EventPlaza"
        }
    }
}

-- 區域等級需求
ZoneConfig.LevelRequirements = {
    castle = 1,
    forest = 1,
    ice = 10,
    lava = 25,
    pvp_arena = 5,
    event_plaza = 1
}

-- 區域解鎖條件
ZoneConfig.UnlockConditions = {
    castle = {}, -- 總是解鎖
    forest = {},
    ice = {
        level = 10,
        completedQuests = { "forest_mastery" }
    },
    lava = {
        level = 25,
        completedQuests = { "ice_mastery" },
        defeatedBosses = { "ice_king" }
    },
    pvp_arena = {
        level = 5,
        completedQuests = { "combat_training" }
    },
    event_plaza = {}
}

-- 傳送費用
ZoneConfig.TeleportCosts = {
    castle = { coins = 0 },
    forest = { coins = 50 },
    ice = { coins = 100 },
    lava = { coins = 200 },
    pvp_arena = { coins = 25 },
    event_plaza = { coins = 0 }
}

-- 獲取區域配置
function ZoneConfig:GetZone(zoneId)
    return self.Zones[zoneId]
end

-- 獲取所有區域
function ZoneConfig:GetAllZones()
    return self.Zones
end

-- 獲取特定類型的區域
function ZoneConfig:GetZonesByType(zoneType)
    local zones = {}
    for id, zone in pairs(self.Zones) do
        if zone.type == zoneType then
            zones[id] = zone
        end
    end
    return zones
end

-- 檢查玩家是否可以進入區域
function ZoneConfig:CanPlayerEnterZone(playerLevel, completedQuests, defeatedBosses, zoneId)
    local zone = self.Zones[zoneId]
    if not zone then
        return false, "區域不存在"
    end
    
    local conditions = self.UnlockConditions[zoneId]
    if not conditions then
        return true
    end
    
    -- 檢查等級需求
    if conditions.level and playerLevel < conditions.level then
        return false, string.format("需要等級 %d", conditions.level)
    end
    
    -- 檢查任務需求
    if conditions.completedQuests then
        for _, questId in ipairs(conditions.completedQuests) do
            if not completedQuests[questId] then
                return false, string.format("需要完成任務: %s", questId)
            end
        end
    end
    
    -- 檢查 Boss 擊敗需求
    if conditions.defeatedBosses then
        for _, bossId in ipairs(conditions.defeatedBosses) do
            if not defeatedBosses[bossId] then
                return false, string.format("需要擊敗 Boss: %s", bossId)
            end
        end
    end
    
    return true
end

-- 獲取傳送費用
function ZoneConfig:GetTeleportCost(zoneId)
    return self.TeleportCosts[zoneId] or { coins = 0 }
end

return ZoneConfig
