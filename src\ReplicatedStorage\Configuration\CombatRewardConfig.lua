-- 戰鬥獎勵配置系統 - 打怪只掉落武器和材料
local CombatRewardConfig = {}

-- ⚠️ 重要設計原則 ⚠️
-- 打怪只掉落：武器、裝備、材料、金幣、經驗值
-- 寵物只能從扭蛋獲得，不能從打怪獲得

-- 裝備稀有度配置
CombatRewardConfig.EQUIPMENT_RARITY = {
    COMMON = {
        name = "普通",
        color = Color3.fromRGB(150, 150, 150),
        statMultiplier = 1.0,
        description = "基礎裝備"
    },
    UNCOMMON = {
        name = "稀有",
        color = Color3.fromRGB(100, 200, 100),
        statMultiplier = 1.3,
        description = "品質不錯的裝備"
    },
    RARE = {
        name = "史詩",
        color = Color3.fromRGB(100, 100, 200),
        statMultiplier = 1.6,
        description = "優秀的裝備"
    },
    EPIC = {
        name = "神話",
        color = Color3.fromRGB(200, 100, 200),
        statMultiplier = 2.0,
        description = "強大的裝備"
    },
    LEGENDARY = {
        name = "傳說",
        color = Color3.fromRGB(255, 200, 0),
        statMultiplier = 2.5,
        description = "傳說級裝備"
    }
}

-- 武器類型配置
CombatRewardConfig.WEAPON_TYPES = {
    SWORD = {
        name = "劍",
        description = "近戰武器，平衡的攻擊力和速度",
        suitableClasses = {"WARRIOR"},
        baseStats = {
            attack = 25,
            speed = 1.0,
            range = 3
        }
    },
    STAFF = {
        name = "法杖",
        description = "魔法武器，提升魔法攻擊力",
        suitableClasses = {"MAGE"},
        baseStats = {
            magicAttack = 30,
            mana = 20,
            range = 8
        }
    },
    BOW = {
        name = "弓",
        description = "遠程武器，高攻擊速度",
        suitableClasses = {"ARCHER"},
        baseStats = {
            attack = 20,
            speed = 1.5,
            range = 12
        }
    },
    WAND = {
        name = "魔杖",
        description = "治療武器，提升治療效果",
        suitableClasses = {"CLERIC"},
        baseStats = {
            healPower = 25,
            mana = 15,
            range = 6
        }
    }
}

-- 材料類型配置
CombatRewardConfig.MATERIAL_TYPES = {
    -- 基礎材料
    BASIC_MATERIALS = {
        IRON_ORE = {
            name = "鐵礦石",
            description = "基礎的金屬材料",
            rarity = "COMMON",
            uses = {"weapon_crafting", "armor_crafting"}
        },
        WOOD = {
            name = "木材",
            description = "常見的建築材料",
            rarity = "COMMON",
            uses = {"weapon_crafting", "tool_crafting"}
        },
        LEATHER = {
            name = "皮革",
            description = "動物皮製成的材料",
            rarity = "COMMON",
            uses = {"armor_crafting", "accessory_crafting"}
        },
        CLOTH = {
            name = "布料",
            description = "織物材料",
            rarity = "COMMON",
            uses = {"armor_crafting", "bag_crafting"}
        }
    },
    
    -- 稀有材料
    RARE_MATERIALS = {
        SILVER_ORE = {
            name = "銀礦石",
            description = "珍貴的金屬材料",
            rarity = "UNCOMMON",
            uses = {"weapon_enhancement", "jewelry_crafting"}
        },
        MAGIC_CRYSTAL = {
            name = "魔法水晶",
            description = "蘊含魔力的水晶",
            rarity = "UNCOMMON",
            uses = {"staff_crafting", "enchantment"}
        },
        BEAST_HIDE = {
            name = "野獸皮",
            description = "強韌的野獸皮革",
            rarity = "UNCOMMON",
            uses = {"armor_enhancement", "shield_crafting"}
        }
    },
    
    -- 史詩材料
    EPIC_MATERIALS = {
        GOLD_ORE = {
            name = "金礦石",
            description = "稀有的貴金屬",
            rarity = "RARE",
            uses = {"legendary_crafting", "equipment_enhancement"}
        },
        DRAGON_SCALE = {
            name = "龍鱗",
            description = "龍族的鱗片，極其堅硬",
            rarity = "RARE",
            uses = {"armor_crafting", "shield_enhancement"}
        },
        PHOENIX_FEATHER = {
            name = "鳳凰羽毛",
            description = "傳說中的鳳凰羽毛",
            rarity = "RARE",
            uses = {"staff_enhancement", "resurrection_item"}
        }
    },
    
    -- 傳說材料
    LEGENDARY_MATERIALS = {
        MITHRIL = {
            name = "秘銀",
            description = "傳說中的金屬",
            rarity = "LEGENDARY",
            uses = {"legendary_weapon", "artifact_crafting"}
        },
        VOID_CRYSTAL = {
            name = "虛空水晶",
            description = "來自虛空的神秘水晶",
            rarity = "LEGENDARY",
            uses = {"ultimate_enhancement", "portal_crafting"}
        }
    }
}

-- 野外怪物掉落配置
CombatRewardConfig.WILD_MONSTER_DROPS = {
    -- 森林區域怪物掉落
    FOREST_WILD = {
        COMMON = {
            experience = {min = 10, max = 25},
            coins = {min = 5, max = 15},
            materials = {
                WOOD = 0.4,           -- 40% 木材
                LEATHER = 0.3,        -- 30% 皮革
                IRON_ORE = 0.2        -- 20% 鐵礦石
            },
            equipment = {
                dropChance = 0.1,     -- 10% 裝備掉落
                types = {"SWORD", "BOW"},
                rarityWeights = {
                    COMMON = 80,
                    UNCOMMON = 15,
                    RARE = 5
                }
            }
        },
        UNCOMMON = {
            experience = {min = 30, max = 50},
            coins = {min = 15, max = 30},
            materials = {
                WOOD = 0.3,
                LEATHER = 0.3,
                SILVER_ORE = 0.2,     -- 20% 銀礦石
                BEAST_HIDE = 0.2      -- 20% 野獸皮
            },
            equipment = {
                dropChance = 0.15,    -- 15% 裝備掉落
                types = {"SWORD", "BOW", "STAFF"},
                rarityWeights = {
                    COMMON = 60,
                    UNCOMMON = 30,
                    RARE = 10
                }
            }
        },
        RARE = {
            experience = {min = 60, max = 100},
            coins = {min = 30, max = 60},
            materials = {
                SILVER_ORE = 0.4,
                MAGIC_CRYSTAL = 0.3,
                GOLD_ORE = 0.2,       -- 20% 金礦石
                DRAGON_SCALE = 0.1    -- 10% 龍鱗
            },
            equipment = {
                dropChance = 0.25,    -- 25% 裝備掉落
                types = {"SWORD", "BOW", "STAFF", "WAND"},
                rarityWeights = {
                    UNCOMMON = 50,
                    RARE = 40,
                    EPIC = 10
                }
            }
        }
    },
    
    -- 冰雪區域怪物掉落
    ICE_WILD = {
        COMMON = {
            experience = {min = 40, max = 80},
            coins = {min = 20, max = 40},
            materials = {
                IRON_ORE = 0.4,
                MAGIC_CRYSTAL = 0.3,
                SILVER_ORE = 0.3
            },
            equipment = {
                dropChance = 0.12,
                types = {"STAFF", "WAND"},
                rarityWeights = {
                    COMMON = 70,
                    UNCOMMON = 25,
                    RARE = 5
                }
            }
        },
        UNCOMMON = {
            experience = {min = 100, max = 150},
            coins = {min = 40, max = 80},
            materials = {
                MAGIC_CRYSTAL = 0.4,
                SILVER_ORE = 0.3,
                GOLD_ORE = 0.2,
                PHOENIX_FEATHER = 0.1
            },
            equipment = {
                dropChance = 0.18,
                types = {"STAFF", "WAND", "SWORD"},
                rarityWeights = {
                    UNCOMMON = 60,
                    RARE = 30,
                    EPIC = 10
                }
            }
        },
        RARE = {
            experience = {min = 200, max = 300},
            coins = {min = 80, max = 150},
            materials = {
                GOLD_ORE = 0.4,
                DRAGON_SCALE = 0.3,
                PHOENIX_FEATHER = 0.2,
                MITHRIL = 0.1
            },
            equipment = {
                dropChance = 0.3,
                types = {"STAFF", "WAND", "SWORD", "BOW"},
                rarityWeights = {
                    RARE = 50,
                    EPIC = 40,
                    LEGENDARY = 10
                }
            }
        }
    },
    
    -- 熔岩區域怪物掉落
    LAVA_WILD = {
        UNCOMMON = {
            experience = {min = 150, max = 250},
            coins = {min = 60, max = 120},
            materials = {
                GOLD_ORE = 0.4,
                DRAGON_SCALE = 0.3,
                PHOENIX_FEATHER = 0.2,
                VOID_CRYSTAL = 0.1
            },
            equipment = {
                dropChance = 0.2,
                types = {"SWORD", "STAFF"},
                rarityWeights = {
                    UNCOMMON = 40,
                    RARE = 40,
                    EPIC = 20
                }
            }
        },
        RARE = {
            experience = {min = 300, max = 500},
            coins = {min = 120, max = 200},
            materials = {
                DRAGON_SCALE = 0.4,
                PHOENIX_FEATHER = 0.3,
                MITHRIL = 0.2,
                VOID_CRYSTAL = 0.1
            },
            equipment = {
                dropChance = 0.35,
                types = {"SWORD", "STAFF", "BOW", "WAND"},
                rarityWeights = {
                    RARE = 40,
                    EPIC = 45,
                    LEGENDARY = 15
                }
            }
        },
        EPIC = {
            experience = {min = 600, max = 1000},
            coins = {min = 200, max = 350},
            materials = {
                MITHRIL = 0.5,
                VOID_CRYSTAL = 0.3,
                PHOENIX_FEATHER = 0.2
            },
            equipment = {
                dropChance = 0.5,
                types = {"SWORD", "STAFF", "BOW", "WAND"},
                rarityWeights = {
                    EPIC = 60,
                    LEGENDARY = 40
                }
            }
        },
        LEGENDARY = {
            experience = {min = 1200, max = 2000},
            coins = {min = 350, max = 600},
            materials = {
                MITHRIL = 0.6,
                VOID_CRYSTAL = 0.4
            },
            equipment = {
                dropChance = 0.8,
                types = {"SWORD", "STAFF", "BOW", "WAND"},
                rarityWeights = {
                    EPIC = 30,
                    LEGENDARY = 70
                }
            }
        }
    }
}

-- 副本BOSS掉落配置
CombatRewardConfig.DUNGEON_BOSS_DROPS = {
    -- 森林副本
    FOREST_DUNGEON = {
        mini_boss = {
            experience = {min = 200, max = 400},
            coins = {min = 100, max = 200},
            gems = {min = 2, max = 5},
            guaranteedMaterial = true,
            materials = {
                SILVER_ORE = 0.4,
                MAGIC_CRYSTAL = 0.3,
                BEAST_HIDE = 0.3
            },
            equipment = {
                dropChance = 0.6,
                rarityWeights = {
                    UNCOMMON = 50,
                    RARE = 40,
                    EPIC = 10
                }
            }
        },
        main_boss = {
            experience = {min = 500, max = 1000},
            coins = {min = 200, max = 500},
            gems = {min = 5, max = 15},
            guaranteedEquipment = true,
            materials = {
                GOLD_ORE = 0.5,
                DRAGON_SCALE = 0.3,
                PHOENIX_FEATHER = 0.2
            },
            equipment = {
                dropChance = 1.0,  -- 100% 掉落
                rarityWeights = {
                    RARE = 40,
                    EPIC = 50,
                    LEGENDARY = 10
                }
            }
        }
    }
}

-- 獎勵驗證系統
CombatRewardConfig.REWARD_VALIDATION = {
    -- 允許的戰鬥獎勵類型
    allowedRewardTypes = {
        "experience",
        "coins", 
        "gems",
        "equipment",
        "materials",
        "consumables"
    },
    
    -- 禁止的戰鬥獎勵類型
    forbiddenRewardTypes = {
        "pets",           -- ❌ 禁止從戰鬥獲得寵物
        "pet_eggs",       -- ❌ 禁止從戰鬥獲得寵物蛋
        "gacha_tickets"   -- ❌ 禁止從戰鬥獲得扭蛋券
    },
    
    -- 驗證獎勵類型
    validateRewardType = function(rewardType)
        local allowed = CombatRewardConfig.REWARD_VALIDATION.allowedRewardTypes
        local forbidden = CombatRewardConfig.REWARD_VALIDATION.forbiddenRewardTypes
        
        -- 檢查是否在禁止列表中
        for _, forbiddenType in ipairs(forbidden) do
            if rewardType == forbiddenType then
                warn(string.format("❌ 非法戰鬥獎勵: %s (寵物只能從扭蛋獲得)", rewardType))
                return false
            end
        end
        
        -- 檢查是否在允許列表中
        for _, allowedType in ipairs(allowed) do
            if rewardType == allowedType then
                return true
            end
        end
        
        warn(string.format("⚠️ 未知獎勵類型: %s", rewardType))
        return false
    end
}

-- 系統初始化檢查
function CombatRewardConfig:Initialize()
    print("⚔️ 初始化戰鬥獎勵配置系統...")
    
    print("✅ 戰鬥獎勵配置系統初始化完成")
    print("📋 重要提醒: 打怪只掉落武器、裝備、材料、金幣、經驗值")
    print("📋 重要提醒: 寵物只能從扭蛋獲得，不能從打怪獲得")
    
    return true
end

return CombatRewardConfig
