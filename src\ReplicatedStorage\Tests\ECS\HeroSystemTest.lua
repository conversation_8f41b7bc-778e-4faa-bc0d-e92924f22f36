-- 英雄系統測試
return function()
    local Matter = require(game.ReplicatedStorage.Packages.Matter)
    local Components = require(game.ReplicatedStorage.Components)
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
    local Utils = require(game.ReplicatedStorage.Shared.Utils)
    
    describe("HeroSystem", function()
        local world
        local heroSystem
        
        beforeEach(function()
            -- 創建測試世界
            world = Matter.World.new()
            
            -- 創建英雄系統實例
            local HeroSystem = require(game.ServerScriptService.Systems.HeroSystem)
            heroSystem = HeroSystem.new()
        end)
        
        afterEach(function()
            if world then
                world:clear()
            end
        end)
        
        it("應該正確創建英雄實體", function()
            -- 創建英雄實體
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                }),
                Components.HealthComponent({
                    current = 150,
                    maximum = 150,
                    regeneration = 1,
                    lastRegenTime = tick()
                }),
                Components.StatsComponent({
                    attack = 15,
                    defense = 8,
                    critRate = 0.05,
                    critDamage = 1.5
                })
            )
            
            -- 驗證實體存在
            expect(heroEntity).to.be.ok()
            
            -- 驗證組件
            local hero = world:get(heroEntity, Components.HeroComponent)
            expect(hero).to.be.ok()
            expect(hero.level).to.equal(1)
            expect(hero.experience).to.equal(0)
            
            local health = world:get(heroEntity, Components.HealthComponent)
            expect(health).to.be.ok()
            expect(health.current).to.equal(150)
            expect(health.maximum).to.equal(150)
            
            local stats = world:get(heroEntity, Components.StatsComponent)
            expect(stats).to.be.ok()
            expect(stats.attack).to.equal(15)
            expect(stats.defense).to.equal(8)
        end)
        
        it("應該正確處理經驗值獲得", function()
            -- 創建英雄實體
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                })
            )
            
            -- 模擬獲得經驗值
            local expGain = 100
            local hero = world:get(heroEntity, Components.HeroComponent)
            
            world:insert(heroEntity, Components.HeroComponent({
                level = hero.level,
                experience = hero.experience + expGain,
                skillPoints = hero.skillPoints
            }))
            
            -- 驗證經驗值更新
            local updatedHero = world:get(heroEntity, Components.HeroComponent)
            expect(updatedHero.experience).to.equal(100)
        end)
        
        it("應該正確處理等級提升", function()
            -- 創建接近升級的英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 90, -- 接近升級
                    skillPoints = 0
                })
            )
            
            -- 計算升級所需經驗
            local requiredExp = Utils.calculateExpForLevel(2)
            expect(requiredExp).to.equal(100) -- 根據配置，等級2需要100經驗
            
            -- 給予足夠的經驗值升級
            local hero = world:get(heroEntity, Components.HeroComponent)
            local newExp = requiredExp + 10 -- 超過升級要求
            
            -- 模擬升級邏輯
            local newLevel = 2
            local remainingExp = newExp - requiredExp
            
            world:insert(heroEntity, Components.HeroComponent({
                level = newLevel,
                experience = remainingExp,
                skillPoints = hero.skillPoints + 1 -- 升級獲得技能點
            }))
            
            -- 驗證升級結果
            local upgradedHero = world:get(heroEntity, Components.HeroComponent)
            expect(upgradedHero.level).to.equal(2)
            expect(upgradedHero.experience).to.equal(10) -- 剩餘經驗
            expect(upgradedHero.skillPoints).to.equal(1) -- 獲得技能點
        end)
        
        it("應該正確計算屬性加成", function()
            -- 創建英雄實體
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 5,
                    experience = 0,
                    skillPoints = 0
                }),
                Components.StatsComponent({
                    attack = 15,
                    defense = 8,
                    critRate = 0.05,
                    critDamage = 1.5
                })
            )
            
            -- 計算等級加成
            local hero = world:get(heroEntity, Components.HeroComponent)
            local stats = world:get(heroEntity, Components.StatsComponent)
            
            local levelBonus = (hero.level - 1) * GameConfig.HERO.STATS_PER_LEVEL
            local expectedAttack = 15 + levelBonus
            local expectedDefense = 8 + levelBonus
            
            -- 模擬屬性更新
            world:insert(heroEntity, Components.StatsComponent({
                attack = expectedAttack,
                defense = expectedDefense,
                critRate = stats.critRate,
                critDamage = stats.critDamage
            }))
            
            -- 驗證屬性計算
            local updatedStats = world:get(heroEntity, Components.StatsComponent)
            expect(updatedStats.attack).to.equal(expectedAttack)
            expect(updatedStats.defense).to.equal(expectedDefense)
        end)
        
        it("應該正確處理技能點分配", function()
            -- 創建有技能點的英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 3,
                    experience = 0,
                    skillPoints = 2
                }),
                Components.StatsComponent({
                    attack = 15,
                    defense = 8,
                    critRate = 0.05,
                    critDamage = 1.5
                })
            )
            
            -- 模擬分配技能點到攻擊力
            local hero = world:get(heroEntity, Components.HeroComponent)
            local stats = world:get(heroEntity, Components.StatsComponent)
            
            local pointsToSpend = 1
            local attackBonus = pointsToSpend * GameConfig.HERO.SKILL_POINT_BONUS
            
            world:insert(heroEntity, Components.HeroComponent({
                level = hero.level,
                experience = hero.experience,
                skillPoints = hero.skillPoints - pointsToSpend
            }))
            
            world:insert(heroEntity, Components.StatsComponent({
                attack = stats.attack + attackBonus,
                defense = stats.defense,
                critRate = stats.critRate,
                critDamage = stats.critDamage
            }))
            
            -- 驗證技能點分配
            local updatedHero = world:get(heroEntity, Components.HeroComponent)
            local updatedStats = world:get(heroEntity, Components.StatsComponent)
            
            expect(updatedHero.skillPoints).to.equal(1) -- 剩餘技能點
            expect(updatedStats.attack).to.equal(15 + attackBonus)
        end)
        
        it("應該正確處理最大等級限制", function()
            -- 創建接近最大等級的英雄
            local maxLevel = GameConfig.HERO.MAX_LEVEL
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = maxLevel,
                    experience = 0,
                    skillPoints = 0
                })
            )
            
            -- 嘗試給予經驗值
            local hero = world:get(heroEntity, Components.HeroComponent)
            local expGain = 1000
            
            -- 在最大等級時，經驗值不應該增加
            world:insert(heroEntity, Components.HeroComponent({
                level = hero.level, -- 等級不變
                experience = hero.experience, -- 經驗值不變
                skillPoints = hero.skillPoints
            }))
            
            -- 驗證等級限制
            local updatedHero = world:get(heroEntity, Components.HeroComponent)
            expect(updatedHero.level).to.equal(maxLevel)
            expect(updatedHero.experience).to.equal(0)
        end)
        
        it("應該正確處理血量隨等級增長", function()
            -- 創建英雄實體
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                }),
                Components.HealthComponent({
                    current = 150,
                    maximum = 150,
                    regeneration = 1,
                    lastRegenTime = tick()
                })
            )
            
            -- 模擬升級到等級5
            local newLevel = 5
            local healthPerLevel = GameConfig.HERO.HEALTH_PER_LEVEL
            local baseHealth = 150
            local expectedMaxHealth = baseHealth + (newLevel - 1) * healthPerLevel
            
            world:insert(heroEntity, Components.HeroComponent({
                level = newLevel,
                experience = 0,
                skillPoints = newLevel - 1
            }))
            
            world:insert(heroEntity, Components.HealthComponent({
                current = expectedMaxHealth,
                maximum = expectedMaxHealth,
                regeneration = 1,
                lastRegenTime = tick()
            }))
            
            -- 驗證血量增長
            local health = world:get(heroEntity, Components.HealthComponent)
            expect(health.maximum).to.equal(expectedMaxHealth)
            expect(health.current).to.equal(expectedMaxHealth)
        end)
    end)
end
