-- 音效控制器 - 管理遊戲音效和音樂
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local SoundService = game:GetService("SoundService")
local TweenService = game:GetService("TweenService")

local AudioController = Knit.CreateController({
    Name = "AudioController"
})

function AudioController:KnitStart()
    print("🔊 AudioController 啟動")
    
    -- 初始化音效系統
    self:initializeAudioSystem()
    
    -- 設置音效事件監聽
    self:setupAudioEventListeners()
    
    -- 載入音效資源
    self:loadAudioAssets()
    
    -- 開始播放背景音樂
    self:playBackgroundMusic("castle")
end

-- 初始化音效系統
function AudioController:initializeAudioSystem()
    -- 音效設置
    self.settings = {
        masterVolume = 1.0,
        musicVolume = 0.7,
        sfxVolume = 0.8,
        musicEnabled = true,
        sfxEnabled = true
    }
    
    -- 當前播放狀態
    self.currentMusic = nil
    self.currentZone = "castle"
    
    -- 音效池
    self.soundPools = {
        ui = {},
        combat = {},
        ambient = {},
        pet = {}
    }
    
    -- 創建音效組
    self.soundGroups = {
        Music = Instance.new("SoundGroup"),
        SFX = Instance.new("SoundGroup"),
        UI = Instance.new("SoundGroup"),
        Ambient = Instance.new("SoundGroup")
    }
    
    -- 設置音效組父級
    for name, group in pairs(self.soundGroups) do
        group.Name = name
        group.Parent = SoundService
    end
    
    -- 設置音量
    self:updateVolumes()
end

-- 設置音效事件監聽
function AudioController:setupAudioEventListeners()
    -- 監聽戰鬥事件
    local CombatService = Knit.GetService("CombatService")
    if CombatService then
        CombatService.OnCombatStarted:Connect(function(data)
            self:playSound("combat_start", "combat")
        end)
        
        CombatService.OnDamageDealt:Connect(function(data)
            self:playSound("attack_hit", "combat")
        end)
        
        CombatService.OnMonsterDefeated:Connect(function(data)
            self:playSound("victory", "combat")
        end)
        
        CombatService.OnPlayerDefeated:Connect(function(data)
            self:playSound("defeat", "combat")
        end)
    end
    
    -- 監聽寵物事件
    local PetService = Knit.GetService("PetService")
    if PetService then
        PetService.OnPetObtained:Connect(function(data)
            if data.petData.rarity == "legendary" then
                self:playSound("legendary_pet", "pet")
            elseif data.petData.rarity == "mythic" then
                self:playSound("mythic_pet", "pet")
            else
                self:playSound("pet_obtained", "pet")
            end
        end)
        
        PetService.OnPetLevelUp:Connect(function(data)
            self:playSound("level_up", "pet")
        end)
    end
    
    -- 監聽UI事件
    local UIController = Knit.GetController("UIController")
    if UIController then
        -- UI音效將在UI控制器中觸發
    end
end

-- 載入音效資源
function AudioController:loadAudioAssets()
    -- 背景音樂
    self.musicTracks = {
        castle = self:createSound("rbxassetid://1234567890", "Music", true, 0.5), -- 替換為真實音效ID
        forest = self:createSound("rbxassetid://1234567891", "Music", true, 0.5),
        ice = self:createSound("rbxassetid://1234567892", "Music", true, 0.5),
        lava = self:createSound("rbxassetid://1234567893", "Music", true, 0.5),
        combat = self:createSound("rbxassetid://1234567894", "Music", true, 0.6)
    }
    
    -- 戰鬥音效
    self.combatSounds = {
        combat_start = self:createSound("rbxassetid://2234567890", "SFX", false, 0.7),
        attack_hit = self:createSound("rbxassetid://2234567891", "SFX", false, 0.6),
        victory = self:createSound("rbxassetid://2234567892", "SFX", false, 0.8),
        defeat = self:createSound("rbxassetid://2234567893", "SFX", false, 0.7)
    }
    
    -- 寵物音效
    self.petSounds = {
        pet_obtained = self:createSound("rbxassetid://3234567890", "SFX", false, 0.7),
        legendary_pet = self:createSound("rbxassetid://3234567891", "SFX", false, 0.9),
        mythic_pet = self:createSound("rbxassetid://3234567892", "SFX", false, 0.8),
        level_up = self:createSound("rbxassetid://3234567893", "SFX", false, 0.6)
    }
    
    -- UI音效
    self.uiSounds = {
        button_click = self:createSound("rbxassetid://4234567890", "UI", false, 0.5),
        button_hover = self:createSound("rbxassetid://4234567891", "UI", false, 0.3),
        menu_open = self:createSound("rbxassetid://4234567892", "UI", false, 0.6),
        menu_close = self:createSound("rbxassetid://4234567893", "UI", false, 0.5),
        notification = self:createSound("rbxassetid://4234567894", "UI", false, 0.7),
        error = self:createSound("rbxassetid://4234567895", "UI", false, 0.8)
    }
    
    -- 環境音效
    self.ambientSounds = {
        wind = self:createSound("rbxassetid://5234567890", "Ambient", true, 0.3),
        fire = self:createSound("rbxassetid://5234567891", "Ambient", true, 0.4),
        water = self:createSound("rbxassetid://5234567892", "Ambient", true, 0.3)
    }
end

-- 創建音效對象
function AudioController:createSound(soundId, groupName, looped, volume)
    local sound = Instance.new("Sound")
    sound.SoundId = soundId
    sound.Looped = looped or false
    sound.Volume = volume or 0.5
    sound.SoundGroup = self.soundGroups[groupName]
    sound.Parent = SoundService
    
    return sound
end

-- 播放背景音樂
function AudioController:playBackgroundMusic(zone)
    if not self.settings.musicEnabled then
        return
    end
    
    local newTrack = self.musicTracks[zone]
    if not newTrack then
        return
    end
    
    -- 如果已有音樂在播放，淡出
    if self.currentMusic and self.currentMusic.IsPlaying then
        self:fadeOutSound(self.currentMusic, 1.0):andThen(function()
            self.currentMusic:Stop()
            self:fadeInSound(newTrack, 1.0)
        end)
    else
        self:fadeInSound(newTrack, 1.0)
    end
    
    self.currentMusic = newTrack
    self.currentZone = zone
    
    print("🎵 播放背景音樂:", zone)
end

-- 播放音效
function AudioController:playSound(soundName, category)
    if not self.settings.sfxEnabled then
        return
    end
    
    local soundTable = nil
    
    if category == "combat" then
        soundTable = self.combatSounds
    elseif category == "pet" then
        soundTable = self.petSounds
    elseif category == "ui" then
        soundTable = self.uiSounds
    elseif category == "ambient" then
        soundTable = self.ambientSounds
    end
    
    if soundTable and soundTable[soundName] then
        local sound = soundTable[soundName]
        sound:Play()
        print("🔊 播放音效:", soundName)
    end
end

-- 停止音效
function AudioController:stopSound(soundName, category)
    local soundTable = nil
    
    if category == "combat" then
        soundTable = self.combatSounds
    elseif category == "pet" then
        soundTable = self.petSounds
    elseif category == "ui" then
        soundTable = self.uiSounds
    elseif category == "ambient" then
        soundTable = self.ambientSounds
    end
    
    if soundTable and soundTable[soundName] then
        soundTable[soundName]:Stop()
    end
end

-- 淡入音效
function AudioController:fadeInSound(sound, duration)
    local originalVolume = sound.Volume
    sound.Volume = 0
    sound:Play()
    
    local tween = TweenService:Create(sound, 
        TweenInfo.new(duration, Enum.EasingStyle.Linear),
        { Volume = originalVolume }
    )
    
    tween:Play()
    
    return Promise.new(function(resolve)
        tween.Completed:Connect(resolve)
    end)
end

-- 淡出音效
function AudioController:fadeOutSound(sound, duration)
    local tween = TweenService:Create(sound,
        TweenInfo.new(duration, Enum.EasingStyle.Linear),
        { Volume = 0 }
    )
    
    tween:Play()
    
    return Promise.new(function(resolve)
        tween.Completed:Connect(function()
            sound:Stop()
            resolve()
        end)
    end)
end

-- 更新音量設置
function AudioController:updateVolumes()
    self.soundGroups.Music.Volume = self.settings.masterVolume * self.settings.musicVolume
    self.soundGroups.SFX.Volume = self.settings.masterVolume * self.settings.sfxVolume
    self.soundGroups.UI.Volume = self.settings.masterVolume * self.settings.sfxVolume
    self.soundGroups.Ambient.Volume = self.settings.masterVolume * self.settings.sfxVolume * 0.5
end

-- 設置主音量
function AudioController:setMasterVolume(volume)
    self.settings.masterVolume = math.clamp(volume, 0, 1)
    self:updateVolumes()
end

-- 設置音樂音量
function AudioController:setMusicVolume(volume)
    self.settings.musicVolume = math.clamp(volume, 0, 1)
    self:updateVolumes()
end

-- 設置音效音量
function AudioController:setSFXVolume(volume)
    self.settings.sfxVolume = math.clamp(volume, 0, 1)
    self:updateVolumes()
end

-- 啟用/禁用音樂
function AudioController:setMusicEnabled(enabled)
    self.settings.musicEnabled = enabled
    
    if not enabled and self.currentMusic then
        self.currentMusic:Stop()
    elseif enabled then
        self:playBackgroundMusic(self.currentZone)
    end
end

-- 啟用/禁用音效
function AudioController:setSFXEnabled(enabled)
    self.settings.sfxEnabled = enabled
end

-- 切換區域音樂
function AudioController:changeZoneMusic(newZone)
    if newZone ~= self.currentZone then
        self:playBackgroundMusic(newZone)
    end
end

-- 播放戰鬥音樂
function AudioController:startCombatMusic()
    self:playBackgroundMusic("combat")
end

-- 恢復區域音樂
function AudioController:endCombatMusic()
    self:playBackgroundMusic(self.currentZone)
end

-- 獲取音效設置
function AudioController:getSettings()
    return self.settings
end

-- 播放UI音效的便捷方法
function AudioController:playUISound(soundName)
    self:playSound(soundName, "ui")
end

-- 播放通知音效
function AudioController:playNotification(type)
    if type == "success" then
        self:playUISound("notification")
    elseif type == "error" then
        self:playUISound("error")
    else
        self:playUISound("notification")
    end
end

return AudioController
