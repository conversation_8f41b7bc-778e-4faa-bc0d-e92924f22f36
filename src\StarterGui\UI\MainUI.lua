-- 主 UI 管理器 - 整合所有 Fusion UI 組件
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local StateManager = require(script.Parent.StateManager)

-- 引入 UI 組件
local StatusBar = require(script.Parent.Components.StatusBar)
local PetList = require(script.Parent.Components.PetList)
local QuestPanel = require(script.Parent.Components.QuestPanel)
local NotificationSystem = require(script.Parent.Components.NotificationSystem)

local MainUI = {}

-- 創建 Fusion scope
local scope = Fusion.scoped(Fusion)

function MainUI.new()
    local Players = game:GetService("Players")
    local player = Players.LocalPlayer
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- 創建主 UI 容器
    local mainUI = scope:New "ScreenGui" {
        Name = "PetSim99MainUI",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        Parent = playerGui,
        
        [scope.Children] = {
            -- 狀態欄
            StatusBar.new(),
            
            -- 寵物清單
            PetList.new(),
            
            -- 任務面板
            QuestPanel.new(),
            
            -- 通知系統
            NotificationSystem.new(),
            
            -- 底部導航欄
            MainUI.createNavigationBar(),
            
            -- 快捷操作欄
            MainUI.createQuickActionBar(),
            
            -- 設置面板
            MainUI.createSettingsPanel(),
            
            -- 載入畫面
            MainUI.createLoadingScreen()
        }
    }
    
    print("🖼️ 主 UI 已創建")
    return mainUI
end

function MainUI.createNavigationBar()
    return scope:New "Frame" {
        Name = "NavigationBar",
        Size = UDim2.new(1, 0, 0, 60),
        Position = UDim2.new(0, 0, 1, -60),
        BackgroundColor3 = Color3.fromRGB(30, 30, 30),
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        
        [scope.Children] = {
            scope:New "UIListLayout" {
                FillDirection = Enum.FillDirection.Horizontal,
                HorizontalAlignment = Enum.HorizontalAlignment.Center,
                VerticalAlignment = Enum.VerticalAlignment.Center,
                Padding = UDim.new(0, 10)
            },
            
            -- 寵物按鈕
            scope:New "TextButton" {
                Name = "PetsButton",
                Size = UDim2.new(0, 100, 0, 50),
                BackgroundColor3 = scope:Computed(function(use)
                    local ui = use(StateManager.UI)
                    return ui.petMenuOpen and Color3.fromRGB(0, 150, 255) or Color3.fromRGB(60, 60, 60)
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "🐾 寵物",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    local current = StateManager:GetUI()
                    StateManager:UpdateUI({ petMenuOpen = not current.petMenuOpen })
                end
            },
            
            -- 任務按鈕
            scope:New "TextButton" {
                Name = "QuestsButton",
                Size = UDim2.new(0, 100, 0, 50),
                BackgroundColor3 = scope:Computed(function(use)
                    local ui = use(StateManager.UI)
                    return ui.questMenuOpen and Color3.fromRGB(0, 150, 255) or Color3.fromRGB(60, 60, 60)
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "📋 任務",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    },
                    
                    -- 任務進度指示器
                    scope:New "TextLabel" {
                        Name = "QuestProgress",
                        Size = UDim2.new(0, 20, 0, 20),
                        Position = UDim2.new(1, -25, 0, 5),
                        BackgroundColor3 = Color3.fromRGB(255, 0, 0),
                        BackgroundTransparency = 0.3,
                        BorderSizePixel = 0,
                        Text = scope:Computed(function(use)
                            local progress = use(StateManager.QuestProgress)
                            return tostring(progress.completed)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        Visible = scope:Computed(function(use)
                            local progress = use(StateManager.QuestProgress)
                            return progress.completed > 0
                        end),
                        
                        [scope.Children] = {
                            scope:New "UICorner" {
                                CornerRadius = UDim.new(0.5, 0)
                            }
                        }
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    local current = StateManager:GetUI()
                    StateManager:UpdateUI({ questMenuOpen = not current.questMenuOpen })
                end
            },
            
            -- 背包按鈕
            scope:New "TextButton" {
                Name = "InventoryButton",
                Size = UDim2.new(0, 100, 0, 50),
                BackgroundColor3 = scope:Computed(function(use)
                    local ui = use(StateManager.UI)
                    return ui.inventoryOpen and Color3.fromRGB(0, 150, 255) or Color3.fromRGB(60, 60, 60)
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "🎒 背包",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    local current = StateManager:GetUI()
                    StateManager:UpdateUI({ inventoryOpen = not current.inventoryOpen })
                end
            },
            
            -- 地圖按鈕
            scope:New "TextButton" {
                Name = "MapButton",
                Size = UDim2.new(0, 100, 0, 50),
                BackgroundColor3 = scope:Computed(function(use)
                    local ui = use(StateManager.UI)
                    return ui.mapOpen and Color3.fromRGB(0, 150, 255) or Color3.fromRGB(60, 60, 60)
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "🗺️ 地圖",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    local current = StateManager:GetUI()
                    StateManager:UpdateUI({ mapOpen = not current.mapOpen })
                end
            },
            
            -- 設置按鈕
            scope:New "TextButton" {
                Name = "SettingsButton",
                Size = UDim2.new(0, 100, 0, 50),
                BackgroundColor3 = scope:Computed(function(use)
                    local ui = use(StateManager.UI)
                    return ui.settingsOpen and Color3.fromRGB(0, 150, 255) or Color3.fromRGB(60, 60, 60)
                end),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "⚙️ 設置",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    local current = StateManager:GetUI()
                    StateManager:UpdateUI({ settingsOpen = not current.settingsOpen })
                end
            }
        }
    }
end

function MainUI.createQuickActionBar()
    return scope:New "Frame" {
        Name = "QuickActionBar",
        Size = UDim2.new(0, 300, 0, 60),
        Position = UDim2.new(0.5, -150, 1, -130),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        
        [scope.Children] = {
            scope:New "UICorner" {
                CornerRadius = UDim.new(0, 10)
            },
            
            scope:New "UIListLayout" {
                FillDirection = Enum.FillDirection.Horizontal,
                HorizontalAlignment = Enum.HorizontalAlignment.Center,
                VerticalAlignment = Enum.VerticalAlignment.Center,
                Padding = UDim.new(0, 10)
            },
            
            -- 扭蛋按鈕
            scope:New "TextButton" {
                Name = "GachaButton",
                Size = UDim2.new(0, 80, 0, 50),
                BackgroundColor3 = Color3.fromRGB(255, 215, 0),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "🎲 扭蛋",
                TextColor3 = Color3.fromRGB(0, 0, 0),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    print("🎲 打開扭蛋界面")
                    -- 這裡可以打開扭蛋界面
                end
            },
            
            -- 商店按鈕
            scope:New "TextButton" {
                Name = "ShopButton",
                Size = UDim2.new(0, 80, 0, 50),
                BackgroundColor3 = Color3.fromRGB(0, 200, 0),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "🛒 商店",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    print("🛒 打開商店界面")
                    -- 這裡可以打開商店界面
                end
            },
            
            -- 圖鑑按鈕
            scope:New "TextButton" {
                Name = "PokedexButton",
                Size = UDim2.new(0, 80, 0, 50),
                BackgroundColor3 = Color3.fromRGB(150, 0, 150),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "📖 圖鑑",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [scope.Children] = {
                    scope:New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    },
                    
                    -- 完成度指示器
                    scope:New "TextLabel" {
                        Name = "CompletionRate",
                        Size = UDim2.new(1, 0, 0.3, 0),
                        Position = UDim2.new(0, 0, 0.7, 0),
                        BackgroundTransparency = 1,
                        Text = scope:Computed(function(use)
                            local pokedex = use(StateManager.Pokedex)
                            return string.format("%.1f%%", pokedex.completionRate * 100)
                        end),
                        TextColor3 = Color3.fromRGB(255, 255, 0),
                        TextScaled = true,
                        Font = Enum.Font.Gotham,
                        TextStrokeTransparency = 0,
                        TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
                    }
                },
                
                [scope.OnEvent "Activated"] = function()
                    print("📖 打開圖鑑界面")
                    -- 這裡可以打開圖鑑界面
                end
            }
        }
    }
end

function MainUI.createSettingsPanel()
    return scope:New "Frame" {
        Name = "SettingsPanel",
        Size = UDim2.new(0.4, 0, 0.6, 0),
        Position = UDim2.new(0.3, 0, 0.2, 0),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Visible = scope:Computed(function(use)
            local ui = use(StateManager.UI)
            return ui.settingsOpen
        end),
        
        [scope.Children] = {
            scope:New "UICorner" {
                CornerRadius = UDim.new(0, 10)
            },
            
            -- 設置內容將在後續添加
            scope:New "TextLabel" {
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundTransparency = 1,
                Text = "⚙️ 設置面板\n(開發中)",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.Gotham
            }
        }
    }
end

function MainUI.createLoadingScreen()
    return scope:New "Frame" {
        Name = "LoadingScreen",
        Size = UDim2.new(1, 0, 1, 0),
        Position = UDim2.new(0, 0, 0, 0),
        BackgroundColor3 = Color3.fromRGB(0, 0, 0),
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Visible = scope:Computed(function(use)
            local ui = use(StateManager.UI)
            return ui.isLoading
        end),
        ZIndex = 1000,
        
        [scope.Children] = {
            scope:New "TextLabel" {
                Size = UDim2.new(0.5, 0, 0.2, 0),
                Position = UDim2.new(0.25, 0, 0.4, 0),
                BackgroundTransparency = 1,
                Text = "載入中...",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold
            }
        }
    }
end

function MainUI.cleanup()
    StatusBar.cleanup()
    PetList.cleanup()
    QuestPanel.cleanup()
    NotificationSystem.cleanup()
    scope:doCleanup()
end

return MainUI
