-- 副本管理系統
local DungeonManager = {}

-- 依賴
local MapConfig = require(game.ReplicatedStorage.Configuration.MapConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 副本實例數據
DungeonManager.instances = {}
DungeonManager.instanceCounter = 0

-- 副本狀態
DungeonManager.INSTANCE_STATUS = {
    PREPARING = "preparing",   -- 準備中
    ACTIVE = "active",         -- 進行中
    BOSS_FIGHT = "boss_fight", -- BOSS戰
    COMPLETED = "completed",   -- 已完成
    FAILED = "failed"          -- 失敗
}

-- 初始化副本管理系統
function DungeonManager:Initialize()
    print("🏰 初始化副本管理系統...")
    
    -- 重置數據
    self.instances = {}
    self.instanceCounter = 0
    
    print("✅ 副本管理系統初始化完成")
end

-- 創建副本實例
function DungeonManager:CreateInstance(teamId, dungeonType, teamMembers)
    if not teamId or not dungeonType or not teamMembers then
        return nil, "無效的參數"
    end
    
    local dungeonConfig = MapConfig.DUNGEONS[dungeonType]
    if not dungeonConfig then
        return nil, "副本類型不存在"
    end
    
    -- 創建新實例
    self.instanceCounter = self.instanceCounter + 1
    local instanceId = string.format("%s_%d", dungeonType, self.instanceCounter)
    
    local instance = {
        id = instanceId,
        teamId = teamId,
        dungeonType = dungeonType,
        config = dungeonConfig,
        members = teamMembers,
        status = self.INSTANCE_STATUS.PREPARING,
        createdTime = tick(),
        startTime = nil,
        endTime = nil,
        
        -- BOSS狀態
        bossStates = {},
        defeatedBosses = {},
        currentBoss = nil,
        
        -- 進度追蹤
        progress = {
            miniBossesDefeated = 0,
            mainBossDefeated = false,
            totalBosses = #dungeonConfig.bosses
        },
        
        -- 獎勵統計
        rewards = {
            totalExp = 0,
            totalCoins = 0,
            totalGems = 0,
            itemsDropped = {}
        },
        
        -- 統計數據
        stats = {
            damageDealt = {},
            damageTaken = {},
            healingDone = {},
            deaths = {}
        }
    }
    
    -- 初始化BOSS狀態
    for i, boss in ipairs(dungeonConfig.bosses) do
        instance.bossStates[boss.name] = {
            isAlive = true,
            currentHealth = boss.health,
            maxHealth = boss.health,
            isEngaged = false,
            engageTime = nil
        }
    end
    
    -- 初始化玩家統計
    for _, member in ipairs(teamMembers) do
        instance.stats.damageDealt[member.UserId] = 0
        instance.stats.damageTaken[member.UserId] = 0
        instance.stats.healingDone[member.UserId] = 0
        instance.stats.deaths[member.UserId] = 0
    end
    
    self.instances[instanceId] = instance
    
    -- 觸發副本創建事件
    EventManager:Fire(EventManager.EventTypes.DUNGEON_INSTANCE_CREATED, {
        instanceId = instanceId,
        teamId = teamId,
        dungeonType = dungeonType,
        memberCount = #teamMembers
    })
    
    print(string.format("🏰 創建副本實例 %s (類型: %s, 隊伍: %s)", instanceId, dungeonType, teamId))
    
    return instanceId, nil
end

-- 開始副本
function DungeonManager:StartInstance(instanceId)
    local instance = self.instances[instanceId]
    if not instance then
        return false, "副本實例不存在"
    end
    
    if instance.status ~= self.INSTANCE_STATUS.PREPARING then
        return false, "副本不在準備狀態"
    end
    
    -- 更新狀態
    instance.status = self.INSTANCE_STATUS.ACTIVE
    instance.startTime = tick()
    
    -- 生成所有BOSS
    self:SpawnBosses(instance)
    
    -- 觸發副本開始事件
    EventManager:Fire(EventManager.EventTypes.DUNGEON_STARTED, {
        instanceId = instanceId,
        dungeonType = instance.dungeonType,
        members = instance.members
    })
    
    print(string.format("🏰 副本實例 %s 開始", instanceId))
    
    -- 設置時間限制檢查
    if instance.config.timeLimit then
        task.spawn(function()
            task.wait(instance.config.timeLimit)
            if instance.status == self.INSTANCE_STATUS.ACTIVE or instance.status == self.INSTANCE_STATUS.BOSS_FIGHT then
                self:FailInstance(instanceId, "時間限制")
            end
        end)
    end
    
    return true, nil
end

-- 生成BOSS
function DungeonManager:SpawnBosses(instance)
    for _, boss in ipairs(instance.config.bosses) do
        -- 這裡應該在實際地圖中生成BOSS
        -- 現在只是記錄BOSS已生成
        print(string.format("👾 生成BOSS: %s (等級 %d, 血量 %d)", boss.name, boss.level, boss.health))
        
        -- 觸發BOSS生成事件
        EventManager:Fire(EventManager.EventTypes.BOSS_SPAWNED, {
            instanceId = instance.id,
            bossName = boss.name,
            bossLevel = boss.level,
            position = boss.position
        })
    end
end

-- BOSS戰鬥開始
function DungeonManager:EngageBoss(instanceId, bossName, player)
    local instance = self.instances[instanceId]
    if not instance then
        return false, "副本實例不存在"
    end
    
    local bossState = instance.bossStates[bossName]
    if not bossState then
        return false, "BOSS不存在"
    end
    
    if not bossState.isAlive then
        return false, "BOSS已死亡"
    end
    
    if bossState.isEngaged then
        return false, "BOSS已在戰鬥中"
    end
    
    -- 開始BOSS戰
    bossState.isEngaged = true
    bossState.engageTime = tick()
    instance.currentBoss = bossName
    instance.status = self.INSTANCE_STATUS.BOSS_FIGHT
    
    -- 觸發BOSS戰開始事件
    EventManager:Fire(EventManager.EventTypes.BOSS_ENGAGED, {
        instanceId = instanceId,
        bossName = bossName,
        engagedBy = player
    })
    
    print(string.format("⚔️ %s 與BOSS %s 開始戰鬥", player.Name, bossName))
    
    return true, nil
end

-- BOSS受到傷害
function DungeonManager:DamageBoss(instanceId, bossName, damage, attacker)
    local instance = self.instances[instanceId]
    if not instance then
        return false, "副本實例不存在"
    end
    
    local bossState = instance.bossStates[bossName]
    if not bossState or not bossState.isAlive then
        return false, "BOSS不存在或已死亡"
    end
    
    -- 應用傷害
    bossState.currentHealth = math.max(0, bossState.currentHealth - damage)
    
    -- 記錄玩家傷害統計
    if attacker then
        instance.stats.damageDealt[attacker.UserId] = (instance.stats.damageDealt[attacker.UserId] or 0) + damage
    end
    
    -- 觸發BOSS受傷事件
    EventManager:Fire(EventManager.EventTypes.BOSS_DAMAGED, {
        instanceId = instanceId,
        bossName = bossName,
        damage = damage,
        attacker = attacker,
        remainingHealth = bossState.currentHealth
    })
    
    -- 檢查BOSS是否死亡
    if bossState.currentHealth <= 0 then
        self:DefeatBoss(instanceId, bossName)
    end
    
    return true, nil
end

-- 擊敗BOSS
function DungeonManager:DefeatBoss(instanceId, bossName)
    local instance = self.instances[instanceId]
    if not instance then
        return false
    end
    
    local bossState = instance.bossStates[bossName]
    if not bossState then
        return false
    end
    
    -- 更新BOSS狀態
    bossState.isAlive = false
    bossState.isEngaged = false
    table.insert(instance.defeatedBosses, bossName)
    
    -- 找到BOSS配置
    local bossConfig = nil
    for _, boss in ipairs(instance.config.bosses) do
        if boss.name == bossName then
            bossConfig = boss
            break
        end
    end
    
    if bossConfig then
        -- 分發獎勵
        self:DistributeRewards(instance, bossConfig)
        
        -- 更新進度
        if bossConfig.type == "mini_boss" then
            instance.progress.miniBossesDefeated = instance.progress.miniBossesDefeated + 1
        elseif bossConfig.type == "main_boss" then
            instance.progress.mainBossDefeated = true
        end
    end
    
    -- 觸發BOSS擊敗事件
    EventManager:Fire(EventManager.EventTypes.BOSS_DEFEATED, {
        instanceId = instanceId,
        bossName = bossName,
        bossType = bossConfig and bossConfig.type or "unknown"
    })
    
    print(string.format("🏆 BOSS %s 被擊敗", bossName))
    
    -- 檢查副本是否完成
    if instance.progress.mainBossDefeated then
        self:CompleteInstance(instanceId)
    else
        -- 回到活躍狀態
        instance.status = self.INSTANCE_STATUS.ACTIVE
        instance.currentBoss = nil
    end
    
    return true
end

-- 分發獎勵
function DungeonManager:DistributeRewards(instance, bossConfig)
    local rewards = bossConfig.rewards
    
    -- 計算每個玩家的獎勵
    for _, member in ipairs(instance.members) do
        local playerRewards = {
            exp = rewards.exp,
            coins = rewards.coins,
            gems = rewards.gems or 0
        }
        
        -- 更新總獎勵統計
        instance.rewards.totalExp = instance.rewards.totalExp + playerRewards.exp
        instance.rewards.totalCoins = instance.rewards.totalCoins + playerRewards.coins
        instance.rewards.totalGems = instance.rewards.totalGems + playerRewards.gems
        
        -- 觸發獎勵獲得事件
        EventManager:Fire(EventManager.EventTypes.PLAYER_RECEIVED_REWARDS, {
            player = member,
            instanceId = instance.id,
            bossName = bossConfig.name,
            rewards = playerRewards
        })
        
        print(string.format("🎁 %s 獲得獎勵: %d經驗, %d金幣, %d寶石", 
              member.Name, playerRewards.exp, playerRewards.coins, playerRewards.gems))
    end
    
    -- 處理物品掉落
    if rewards.dropChance then
        self:HandleItemDrops(instance, bossConfig)
    end
end

-- 處理物品掉落
function DungeonManager:HandleItemDrops(instance, bossConfig)
    local dropChance = bossConfig.rewards.dropChance
    
    for rarity, chance in pairs(dropChance) do
        if math.random() <= chance then
            -- 這裡應該從對應稀有度的物品池中隨機選擇
            local droppedItem = {
                name = string.format("%s的%s物品", bossConfig.name, rarity),
                rarity = rarity,
                source = bossConfig.name
            }
            
            table.insert(instance.rewards.itemsDropped, droppedItem)
            
            -- 觸發物品掉落事件
            EventManager:Fire(EventManager.EventTypes.ITEM_DROPPED, {
                instanceId = instance.id,
                item = droppedItem,
                bossName = bossConfig.name
            })
            
            print(string.format("💎 掉落物品: %s (%s級)", droppedItem.name, rarity))
        end
    end
end

-- 完成副本
function DungeonManager:CompleteInstance(instanceId)
    local instance = self.instances[instanceId]
    if not instance then
        return false
    end
    
    instance.status = self.INSTANCE_STATUS.COMPLETED
    instance.endTime = tick()
    
    local duration = instance.endTime - instance.startTime
    
    -- 觸發副本完成事件
    EventManager:Fire(EventManager.EventTypes.DUNGEON_COMPLETED, {
        instanceId = instanceId,
        teamId = instance.teamId,
        duration = duration,
        rewards = instance.rewards,
        stats = instance.stats
    })
    
    print(string.format("🏆 副本實例 %s 完成 (耗時: %.1f秒)", instanceId, duration))
    
    return true
end

-- 副本失敗
function DungeonManager:FailInstance(instanceId, reason)
    local instance = self.instances[instanceId]
    if not instance then
        return false
    end
    
    instance.status = self.INSTANCE_STATUS.FAILED
    instance.endTime = tick()
    
    -- 觸發副本失敗事件
    EventManager:Fire(EventManager.EventTypes.DUNGEON_FAILED, {
        instanceId = instanceId,
        teamId = instance.teamId,
        reason = reason
    })
    
    print(string.format("💀 副本實例 %s 失敗 (原因: %s)", instanceId, reason))
    
    return true
end

-- 獲取副本實例信息
function DungeonManager:GetInstanceInfo(instanceId)
    return self.instances[instanceId]
end

-- 獲取統計信息
function DungeonManager:GetStats()
    local stats = {
        totalInstances = 0,
        activeInstances = 0,
        completedInstances = 0,
        failedInstances = 0,
        instancesByType = {}
    }
    
    for _, instance in pairs(self.instances) do
        stats.totalInstances = stats.totalInstances + 1
        
        if instance.status == self.INSTANCE_STATUS.ACTIVE or instance.status == self.INSTANCE_STATUS.BOSS_FIGHT then
            stats.activeInstances = stats.activeInstances + 1
        elseif instance.status == self.INSTANCE_STATUS.COMPLETED then
            stats.completedInstances = stats.completedInstances + 1
        elseif instance.status == self.INSTANCE_STATUS.FAILED then
            stats.failedInstances = stats.failedInstances + 1
        end
        
        stats.instancesByType[instance.dungeonType] = (stats.instancesByType[instance.dungeonType] or 0) + 1
    end
    
    return stats
end

-- 調試信息
function DungeonManager:Debug()
    print("🏰 副本管理系統調試信息:")
    
    local stats = self:GetStats()
    print("  總副本實例:", stats.totalInstances)
    print("  活躍實例:", stats.activeInstances)
    print("  已完成實例:", stats.completedInstances)
    print("  失敗實例:", stats.failedInstances)
    
    print("  各類型實例數:")
    for dungeonType, count in pairs(stats.instancesByType) do
        print(string.format("    %s: %d", dungeonType, count))
    end
end

return DungeonManager
