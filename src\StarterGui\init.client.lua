-- 客戶端主啟動腳本
-- 初始化 Knit 控制器和 Fusion UI

print("🎨 PetSim99Lua 客戶端啟動中...")

-- 引入依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)

-- 引入配置
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)

print("✅ 依賴已載入")

-- 添加所有 Knit 控制器
local controllersFolder = script.Controllers
for _, controllerModule in pairs(controllersFolder:GetChildren()) do
    if controllerModule:IsA("ModuleScript") then
        require(controllerModule)
        print("🎮 已載入控制器:", controllerModule.Name)
    end
end

-- 啟動 Knit
Knit.Start():andThen(function()
    print("🌐 Knit 客戶端已啟動")
    
    -- 初始化 UI
    local UIController = Knit.GetController("UIController")
    if UIController then
        UIController:InitializeUI()
    end
    
    -- 連接到服務端
    local PlayerService = Knit.GetService("PlayerService")
    if PlayerService then
        -- 請求玩家數據
        PlayerService:GetPlayerData():andThen(function(data)
            if data then
                print("📊 玩家數據已載入")
            end
        end):catch(function(err)
            warn("❌ 玩家數據載入失敗:", err)
        end)
    end
    
end):catch(function(err)
    error("❌ Knit 客戶端啟動失敗: " .. tostring(err))
end)

-- 錯誤處理
local function handleError(err)
    warn("🚨 客戶端錯誤:", err)
    warn(debug.traceback())
end

-- 全局錯誤捕獲
local success, err = pcall(function()
    -- 主要初始化邏輯已在上面完成
end)

if not success then
    handleError(err)
end

print("🎮 PetSim99Lua 客戶端啟動完成！")
print("📱 遊戲版本:", GameConfig.VERSION)
