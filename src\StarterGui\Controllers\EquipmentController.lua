-- 裝備控制器 - 客戶端界面處理
local EquipmentController = {}

-- 依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Fusion = require(game.ReplicatedStorage.Packages.Fusion)
local EquipmentConfig = require(game.ReplicatedStorage.Configuration.EquipmentConfig)

-- Fusion 組件
local New = Fusion.New
local State = Fusion.State
local Computed = Fusion.Computed

-- 創建 Knit 控制器
EquipmentController = Knit.CreateController({
    Name = "EquipmentController"
})

-- 狀態管理
EquipmentController.playerEquipment = State({})
EquipmentController.playerInventory = State({})
EquipmentController.equipmentStats = State({})
EquipmentController.isLoading = State(true)
EquipmentController.selectedEquipment = State(nil)
EquipmentController.showEquipmentPanel = State(false)
EquipmentController.showInventoryPanel = State(false)

-- UI 元素
EquipmentController.mainUI = nil
EquipmentController.equipmentService = nil

-- 控制器初始化
function EquipmentController:KnitInit()
    print("⚔️ 初始化裝備控制器...")
    
    -- 獲取裝備服務
    self.equipmentService = Knit.GetService("EquipmentService")
    
    -- 設置事件監聽
    self:SetupEventListeners()
end

-- 控制器啟動
function EquipmentController:KnitStart()
    print("✅ 裝備控制器啟動完成")
    
    -- 載入裝備數據
    self:LoadEquipmentData()
    
    -- 創建 UI
    self:CreateUI()
end

-- 設置事件監聽
function EquipmentController:SetupEventListeners()
    -- 監聽裝備事件
    self.equipmentService.OnEquipmentEquipped:Connect(function(data)
        self:OnEquipmentEquipped(data)
    end)
    
    self.equipmentService.OnEquipmentUnequipped:Connect(function(data)
        self:OnEquipmentUnequipped(data)
    end)
    
    self.equipmentService.OnEquipmentEnhanced:Connect(function(data)
        self:OnEquipmentEnhanced(data)
    end)
    
    self.equipmentService.OnEquipmentObtained:Connect(function(data)
        self:OnEquipmentObtained(data)
    end)
    
    self.equipmentService.OnInventoryUpdated:Connect(function()
        self:RefreshInventoryData()
    end)
    
    -- 監聽鍵盤輸入
    local UserInputService = game:GetService("UserInputService")
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.E then
            -- E鍵切換裝備面板
            self.showEquipmentPanel:set(not self.showEquipmentPanel:get())
        elseif input.KeyCode == Enum.KeyCode.I then
            -- I鍵切換背包面板
            self.showInventoryPanel:set(not self.showInventoryPanel:get())
        end
    end)
end

-- 載入裝備數據
function EquipmentController:LoadEquipmentData()
    self.isLoading:set(true)
    
    -- 載入已裝備的裝備
    self.equipmentService:GetPlayerEquipment():andThen(function(equipment)
        self.playerEquipment:set(equipment)
    end):catch(function(error)
        warn("載入裝備數據失敗:", error)
    end)
    
    -- 載入背包
    self.equipmentService:GetPlayerInventory():andThen(function(inventory)
        self.playerInventory:set(inventory)
    end):catch(function(error)
        warn("載入背包數據失敗:", error)
    end)
    
    -- 載入裝備屬性
    self.equipmentService:GetEquipmentStats():andThen(function(stats)
        self.equipmentStats:set(stats)
        self.isLoading:set(false)
    end):catch(function(error)
        warn("載入裝備屬性失敗:", error)
        self.isLoading:set(false)
    end)
end

-- 刷新背包數據
function EquipmentController:RefreshInventoryData()
    self.equipmentService:GetPlayerInventory():andThen(function(inventory)
        self.playerInventory:set(inventory)
    end)
    
    self.equipmentService:GetPlayerEquipment():andThen(function(equipment)
        self.playerEquipment:set(equipment)
    end)
    
    self.equipmentService:GetEquipmentStats():andThen(function(stats)
        self.equipmentStats:set(stats)
    end)
end

-- 裝備物品
function EquipmentController:EquipItem(instanceId)
    self.equipmentService:EquipItem(instanceId):andThen(function(result)
        if result.success then
            self:ShowNotification("裝備成功！", "success")
        else
            self:ShowNotification("裝備失敗: " .. result.error, "error")
        end
    end):catch(function(error)
        self:ShowNotification("裝備失敗: " .. tostring(error), "error")
    end)
end

-- 卸下裝備
function EquipmentController:UnequipItem(slot)
    self.equipmentService:UnequipItem(slot):andThen(function(result)
        if result.success then
            self:ShowNotification("卸下成功！", "success")
        else
            self:ShowNotification("卸下失敗: " .. result.error, "error")
        end
    end):catch(function(error)
        self:ShowNotification("卸下失敗: " .. tostring(error), "error")
    end)
end

-- 強化裝備
function EquipmentController:EnhanceEquipment(instanceId)
    self.equipmentService:EnhanceEquipment(instanceId):andThen(function(result)
        if result.success then
            self:ShowNotification("強化成功！", "success")
        else
            self:ShowNotification("強化失敗: " .. result.message, "error")
        end
    end):catch(function(error)
        self:ShowNotification("強化失敗: " .. tostring(error), "error")
    end)
end

-- 創建主 UI
function EquipmentController:CreateUI()
    local playerGui = game.Players.LocalPlayer:WaitForChild("PlayerGui")
    
    self.mainUI = New "ScreenGui" {
        Name = "EquipmentUI",
        Parent = playerGui,
        ResetOnSpawn = false,
        
        [Fusion.Children] = {
            -- 裝備面板
            self:CreateEquipmentPanel(),
            
            -- 背包面板
            self:CreateInventoryPanel(),
            
            -- 裝備屬性面板
            self:CreateStatsPanel(),
            
            -- 通知系統
            self:CreateNotificationPanel(),
            
            -- 快捷鍵提示
            self:CreateHotkeyHints()
        }
    }
end

-- 創建裝備面板
function EquipmentController:CreateEquipmentPanel()
    return New "Frame" {
        Name = "EquipmentPanel",
        Size = UDim2.new(0, 400, 0, 500),
        Position = UDim2.new(0, 10, 0, 10),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        
        Visible = Computed(function()
            return self.showEquipmentPanel:get() and not self.isLoading:get()
        end),
        
        [Fusion.Children] = {
            -- 標題
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 30),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "裝備 (E)",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            },
            
            -- 關閉按鈕
            New "TextButton" {
                Name = "CloseButton",
                Size = UDim2.new(0, 25, 0, 25),
                Position = UDim2.new(1, -30, 0, 2.5),
                BackgroundColor3 = Color3.fromRGB(200, 50, 50),
                BorderSizePixel = 0,
                Text = "×",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold,
                
                [Fusion.OnEvent "Activated"] = function()
                    self.showEquipmentPanel:set(false)
                end
            },
            
            -- 裝備槽位
            self:CreateEquipmentSlots()
        }
    }
end

-- 創建裝備槽位
function EquipmentController:CreateEquipmentSlots()
    return New "Frame" {
        Name = "EquipmentSlots",
        Size = UDim2.new(1, -20, 1, -40),
        Position = UDim2.new(0, 10, 0, 35),
        BackgroundTransparency = 1,
        
        [Fusion.Children] = Computed(function()
            local equipment = self.playerEquipment:get()
            local slots = {}
            
            -- 定義槽位布局
            local slotLayout = {
                {slot = "HELMET", position = UDim2.new(0.5, -40, 0, 10), name = "頭盔"},
                {slot = "MAIN_HAND", position = UDim2.new(0, 10, 0.3, 0), name = "主手"},
                {slot = "CHEST", position = UDim2.new(0.5, -40, 0.2, 0), name = "胸甲"},
                {slot = "OFF_HAND", position = UDim2.new(1, -90, 0.3, 0), name = "副手"},
                {slot = "LEGS", position = UDim2.new(0.5, -40, 0.4, 0), name = "腿甲"},
                {slot = "BOOTS", position = UDim2.new(0.5, -40, 0.6, 0), name = "靴子"},
                {slot = "RING", position = UDim2.new(0, 10, 0.7, 0), name = "戒指"},
                {slot = "NECKLACE", position = UDim2.new(0.5, -40, 0.8, 0), name = "項鍊"},
                {slot = "EARRING", position = UDim2.new(1, -90, 0.7, 0), name = "耳環"}
            }
            
            for _, slotInfo in ipairs(slotLayout) do
                local equippedItem = equipment[slotInfo.slot]
                
                slots[slotInfo.slot] = New "Frame" {
                    Name = slotInfo.slot,
                    Size = UDim2.new(0, 80, 0, 80),
                    Position = slotInfo.position,
                    BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                    BorderSizePixel = 1,
                    BorderColor3 = Color3.fromRGB(100, 100, 100),
                    
                    [Fusion.Children] = {
                        -- 槽位標籤
                        New "TextLabel" {
                            Name = "SlotLabel",
                            Size = UDim2.new(1, 0, 0, 15),
                            Position = UDim2.new(0, 0, 1, 0),
                            BackgroundTransparency = 1,
                            Text = slotInfo.name,
                            TextColor3 = Color3.fromRGB(200, 200, 200),
                            TextScaled = true,
                            Font = Enum.Font.SourceSans
                        },
                        
                        -- 裝備圖標或空槽位
                        equippedItem and New "TextLabel" {
                            Name = "EquipmentIcon",
                            Size = UDim2.new(1, -10, 1, -10),
                            Position = UDim2.new(0, 5, 0, 5),
                            BackgroundColor3 = EquipmentConfig.QUALITY[equippedItem.quality].color,
                            BorderSizePixel = 0,
                            Text = equippedItem.icon,
                            TextColor3 = Color3.fromRGB(255, 255, 255),
                            TextScaled = true,
                            Font = Enum.Font.SourceSans,
                            
                            [Fusion.Children] = {
                                -- 強化等級顯示
                                equippedItem.enhanceLevel > 0 and New "TextLabel" {
                                    Name = "EnhanceLevel",
                                    Size = UDim2.new(0, 20, 0, 15),
                                    Position = UDim2.new(1, -20, 0, 0),
                                    BackgroundColor3 = Color3.fromRGB(255, 200, 0),
                                    BorderSizePixel = 0,
                                    Text = "+" .. equippedItem.enhanceLevel,
                                    TextColor3 = Color3.fromRGB(0, 0, 0),
                                    TextScaled = true,
                                    Font = Enum.Font.SourceSansBold
                                } or nil,
                                
                                -- 點擊卸下按鈕
                                New "TextButton" {
                                    Name = "UnequipButton",
                                    Size = UDim2.new(1, 0, 1, 0),
                                    BackgroundTransparency = 1,
                                    Text = "",
                                    
                                    [Fusion.OnEvent "Activated"] = function()
                                        self:UnequipItem(slotInfo.slot)
                                    end
                                }
                            }
                        } or New "TextLabel" {
                            Name = "EmptySlot",
                            Size = UDim2.new(1, -10, 1, -10),
                            Position = UDim2.new(0, 5, 0, 5),
                            BackgroundColor3 = Color3.fromRGB(80, 80, 80),
                            BorderSizePixel = 0,
                            Text = "空",
                            TextColor3 = Color3.fromRGB(150, 150, 150),
                            TextScaled = true,
                            Font = Enum.Font.SourceSans
                        }
                    }
                }
            end
            
            return slots
        end)
    }
end

-- 創建背包面板
function EquipmentController:CreateInventoryPanel()
    return New "Frame" {
        Name = "InventoryPanel",
        Size = UDim2.new(0, 500, 0, 400),
        Position = UDim2.new(0, 420, 0, 10),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        
        Visible = Computed(function()
            return self.showInventoryPanel:get() and not self.isLoading:get()
        end),
        
        [Fusion.Children] = {
            -- 標題
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 30),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "背包 (I)",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            },
            
            -- 關閉按鈕
            New "TextButton" {
                Name = "CloseButton",
                Size = UDim2.new(0, 25, 0, 25),
                Position = UDim2.new(1, -30, 0, 2.5),
                BackgroundColor3 = Color3.fromRGB(200, 50, 50),
                BorderSizePixel = 0,
                Text = "×",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold,
                
                [Fusion.OnEvent "Activated"] = function()
                    self.showInventoryPanel:set(false)
                end
            },
            
            -- 背包物品
            self:CreateInventoryGrid()
        }
    }
end

-- 創建背包網格
function EquipmentController:CreateInventoryGrid()
    return New "ScrollingFrame" {
        Name = "InventoryGrid",
        Size = UDim2.new(1, -20, 1, -40),
        Position = UDim2.new(0, 10, 0, 35),
        BackgroundTransparency = 1,
        BorderSizePixel = 0,
        ScrollBarThickness = 8,
        
        [Fusion.Children] = {
            New "UIGridLayout" {
                CellSize = UDim2.new(0, 80, 0, 80),
                CellPadding = UDim2.new(0, 5, 0, 5),
                SortOrder = Enum.SortOrder.LayoutOrder
            },
            
            Computed(function()
                local inventory = self.playerInventory:get()
                local items = {}
                local index = 1
                
                for instanceId, equipment in pairs(inventory) do
                    items[instanceId] = New "Frame" {
                        Name = instanceId,
                        Size = UDim2.new(0, 80, 0, 80),
                        BackgroundColor3 = EquipmentConfig.QUALITY[equipment.quality].color,
                        BorderSizePixel = 1,
                        BorderColor3 = Color3.fromRGB(100, 100, 100),
                        LayoutOrder = index,
                        
                        [Fusion.Children] = {
                            -- 裝備圖標
                            New "TextLabel" {
                                Name = "Icon",
                                Size = UDim2.new(1, -10, 0.7, 0),
                                Position = UDim2.new(0, 5, 0, 5),
                                BackgroundTransparency = 1,
                                Text = equipment.icon,
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.SourceSans
                            },
                            
                            -- 裝備名稱
                            New "TextLabel" {
                                Name = "Name",
                                Size = UDim2.new(1, -5, 0.3, 0),
                                Position = UDim2.new(0, 2.5, 0.7, 0),
                                BackgroundTransparency = 1,
                                Text = equipment.name,
                                TextColor3 = Color3.fromRGB(255, 255, 255),
                                TextScaled = true,
                                Font = Enum.Font.SourceSans,
                                TextWrapped = true
                            },
                            
                            -- 強化等級
                            equipment.enhanceLevel > 0 and New "TextLabel" {
                                Name = "EnhanceLevel",
                                Size = UDim2.new(0, 20, 0, 15),
                                Position = UDim2.new(1, -20, 0, 0),
                                BackgroundColor3 = Color3.fromRGB(255, 200, 0),
                                BorderSizePixel = 0,
                                Text = "+" .. equipment.enhanceLevel,
                                TextColor3 = Color3.fromRGB(0, 0, 0),
                                TextScaled = true,
                                Font = Enum.Font.SourceSansBold
                            } or nil,
                            
                            -- 點擊裝備按鈕
                            New "TextButton" {
                                Name = "EquipButton",
                                Size = UDim2.new(1, 0, 1, 0),
                                BackgroundTransparency = 1,
                                Text = "",
                                
                                [Fusion.OnEvent "Activated"] = function()
                                    self:EquipItem(instanceId)
                                end
                            }
                        }
                    }
                    
                    index = index + 1
                end
                
                return items
            end)
        }
    }
end

-- 創建屬性面板
function EquipmentController:CreateStatsPanel()
    return New "Frame" {
        Name = "StatsPanel",
        Size = UDim2.new(0, 200, 0, 300),
        Position = UDim2.new(1, -210, 0, 10),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BorderSizePixel = 0,
        
        Visible = Computed(function()
            return (self.showEquipmentPanel:get() or self.showInventoryPanel:get()) and not self.isLoading:get()
        end),
        
        [Fusion.Children] = {
            -- 標題
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(1, 0, 0, 30),
                BackgroundColor3 = Color3.fromRGB(60, 60, 60),
                BorderSizePixel = 0,
                Text = "裝備屬性",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold
            },
            
            -- 屬性列表
            New "ScrollingFrame" {
                Name = "StatsList",
                Size = UDim2.new(1, -10, 1, -35),
                Position = UDim2.new(0, 5, 0, 30),
                BackgroundTransparency = 1,
                BorderSizePixel = 0,
                ScrollBarThickness = 6,
                
                [Fusion.Children] = {
                    New "UIListLayout" {
                        SortOrder = Enum.SortOrder.LayoutOrder,
                        Padding = UDim.new(0, 2)
                    },
                    
                    Computed(function()
                        local stats = self.equipmentStats:get()
                        local statLabels = {}
                        
                        if stats.equipmentStats then
                            local index = 1
                            for statName, value in pairs(stats.equipmentStats) do
                                if value > 0 then
                                    statLabels[statName] = New "TextLabel" {
                                        Name = statName,
                                        Size = UDim2.new(1, 0, 0, 20),
                                        BackgroundTransparency = 1,
                                        Text = string.format("%s: +%d", statName, value),
                                        TextColor3 = Color3.fromRGB(100, 200, 100),
                                        TextScaled = true,
                                        Font = Enum.Font.SourceSans,
                                        TextXAlignment = Enum.TextXAlignment.Left,
                                        LayoutOrder = index
                                    }
                                    index = index + 1
                                end
                            end
                        end
                        
                        return statLabels
                    end)
                }
            }
        }
    }
end

-- 創建通知面板
function EquipmentController:CreateNotificationPanel()
    return New "Frame" {
        Name = "NotificationPanel",
        Size = UDim2.new(0, 300, 0, 50),
        Position = UDim2.new(0.5, -150, 0, 150),
        BackgroundTransparency = 1,
        
        [Fusion.Children] = {
            New "TextLabel" {
                Name = "NotificationText",
                Size = UDim2.new(1, 0, 1, 0),
                BackgroundColor3 = Color3.fromRGB(0, 0, 0),
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Text = "",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSansBold,
                Visible = false
            }
        }
    }
end

-- 創建快捷鍵提示
function EquipmentController:CreateHotkeyHints()
    return New "Frame" {
        Name = "HotkeyHints",
        Size = UDim2.new(0, 150, 0, 60),
        Position = UDim2.new(0, 10, 1, -70),
        BackgroundColor3 = Color3.fromRGB(0, 0, 0),
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        
        [Fusion.Children] = {
            New "TextLabel" {
                Name = "Hints",
                Size = UDim2.new(1, -10, 1, -10),
                Position = UDim2.new(0, 5, 0, 5),
                BackgroundTransparency = 1,
                Text = "E - 裝備面板\nI - 背包面板",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.SourceSans,
                TextXAlignment = Enum.TextXAlignment.Left,
                TextYAlignment = Enum.TextYAlignment.Top
            }
        }
    }
end

-- 顯示通知
function EquipmentController:ShowNotification(message, type)
    local notificationPanel = self.mainUI:FindFirstChild("NotificationPanel")
    if notificationPanel then
        local textLabel = notificationPanel:FindFirstChild("NotificationText")
        if textLabel then
            textLabel.Text = message
            textLabel.Visible = true
            
            -- 根據類型設置顏色
            if type == "success" then
                textLabel.BackgroundColor3 = Color3.fromRGB(0, 100, 0)
            elseif type == "error" then
                textLabel.BackgroundColor3 = Color3.fromRGB(100, 0, 0)
            else
                textLabel.BackgroundColor3 = Color3.fromRGB(0, 0, 100)
            end
            
            -- 3秒後隱藏
            task.spawn(function()
                task.wait(3)
                textLabel.Visible = false
            end)
        end
    end
end

-- 事件處理函數
function EquipmentController:OnEquipmentEquipped(data)
    self:ShowNotification(string.format("裝備了 %s", data.equipment.name), "success")
    self:RefreshInventoryData()
end

function EquipmentController:OnEquipmentUnequipped(data)
    self:ShowNotification(string.format("卸下了 %s", data.equipment.name), "info")
    self:RefreshInventoryData()
end

function EquipmentController:OnEquipmentEnhanced(data)
    if data.success then
        self:ShowNotification(string.format("%s 強化到 +%d", data.equipment.name, data.newLevel), "success")
    else
        self:ShowNotification(string.format("%s 強化失敗", data.equipment.name), "error")
    end
    self:RefreshInventoryData()
end

function EquipmentController:OnEquipmentObtained(data)
    self:ShowNotification(string.format("獲得了 %s", data.equipment.name), "success")
    self:RefreshInventoryData()
end

return EquipmentController
