-- 區域服務 - 管理遊戲區域和玩家位置
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local ZonePlus = require(game.ReplicatedStorage.Packages.ZonePlus)
local Components = require(game.ReplicatedStorage.Components)
local ZoneConfig = require(game.ReplicatedStorage.Configuration.ZoneConfig)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local ZoneService = Knit.CreateService({
    Name = "ZoneService",
    Client = {
        -- 客戶端可調用的方法
        GetCurrentZone = Knit.CreateSignal(),
        GetAvailableZones = Knit.CreateSignal(),
        TeleportToZone = Knit.CreateSignal(),
        GetZoneInfo = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnZoneEntered = Knit.CreateSignal(),
        OnZoneExited = Knit.CreateSignal(),
        OnZoneChanged = Knit.CreateSignal(),
        OnTeleportCompleted = Knit.CreateSignal(),
        OnZoneUnlocked = Knit.CreateSignal()
    }
})

function ZoneService:KnitInit()
    self._worldManager = _G.WorldManager
    self._world = _G.ECSWorld
    
    -- 區域實例存儲
    self._zones = {}
    self._playerZones = {} -- 玩家當前所在區域
    
    print("🗺️ ZoneService 初始化完成")
end

function ZoneService:KnitStart()
    print("🎮 ZoneService 啟動")
    
    -- 初始化所有區域
    self:_initializeZones()
    
    -- 設置區域事件監聽
    self:_setupZoneEventListeners()
    
    -- 啟動區域管理系統
    self:_startZoneManagement()
end

-- 初始化所有區域
function ZoneService:_initializeZones()
    local workspace = game:GetService("Workspace")
    
    for zoneId, zoneConfig in pairs(ZoneConfig:GetAllZones()) do
        local success, zone = pcall(function()
            return self:_createZone(zoneId, zoneConfig)
        end)
        
        if success and zone then
            self._zones[zoneId] = zone
            print("✅ 區域已初始化:", zoneConfig.name)
        else
            warn("❌ 區域初始化失敗:", zoneId, zone)
        end
    end
    
    print(string.format("🗺️ 已初始化 %d 個區域", Utils.tableLength(self._zones)))
end

-- 創建單個區域
function ZoneService:_createZone(zoneId, zoneConfig)
    local workspace = game:GetService("Workspace")
    
    -- 尋找區域邊界
    local zonePart = nil
    if zoneConfig.boundaries.type == "part" then
        zonePart = workspace:FindFirstChild(zoneConfig.boundaries.partName)
        if not zonePart then
            warn("⚠️ 找不到區域 Part:", zoneConfig.boundaries.partName)
            return nil
        end
    end
    
    -- 創建 ZonePlus 區域
    local zone = ZonePlus.new(zonePart)
    
    -- 設置區域屬性
    zone.zoneId = zoneId
    zone.config = zoneConfig
    
    return zone
end

-- 設置區域事件監聽
function ZoneService:_setupZoneEventListeners()
    for zoneId, zone in pairs(self._zones) do
        -- 玩家進入區域
        zone.playerEntered:Connect(function(player)
            self:_onPlayerEnteredZone(player, zoneId)
        end)
        
        -- 玩家離開區域
        zone.playerExited:Connect(function(player)
            self:_onPlayerExitedZone(player, zoneId)
        end)
    end
end

-- 玩家進入區域事件
function ZoneService:_onPlayerEnteredZone(player, zoneId)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig then
        return
    end
    
    -- 更新玩家當前區域
    local previousZone = self._playerZones[player.UserId]
    self._playerZones[player.UserId] = zoneId
    
    -- 更新玩家實體的區域組件
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if playerEntity then
        self._world:insert(playerEntity, Components.ZoneComponent({
            currentZone = zoneId,
            previousZone = previousZone,
            enterTime = tick()
        }))
    end
    
    -- 應用區域設置
    self:_applyZoneSettings(player, zoneConfig)
    
    -- 通知客戶端
    self.Client.OnZoneEntered:Fire(player, {
        zoneId = zoneId,
        zoneName = zoneConfig.name,
        zoneType = zoneConfig.type,
        previousZone = previousZone,
        timestamp = tick()
    })
    
    -- 觸發區域特定邏輯
    self:_triggerZoneLogic(player, zoneId, "enter")
    
    print("🚪 玩家進入區域:", player.Name, "->", zoneConfig.name)
end

-- 玩家離開區域事件
function ZoneService:_onPlayerExitedZone(player, zoneId)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig then
        return
    end
    
    -- 通知客戶端
    self.Client.OnZoneExited:Fire(player, {
        zoneId = zoneId,
        zoneName = zoneConfig.name,
        zoneType = zoneConfig.type,
        timestamp = tick()
    })
    
    -- 觸發區域特定邏輯
    self:_triggerZoneLogic(player, zoneId, "exit")
    
    print("🚪 玩家離開區域:", player.Name, "<-", zoneConfig.name)
end

-- 應用區域設置
function ZoneService:_applyZoneSettings(player, zoneConfig)
    local settings = zoneConfig.settings
    
    -- 更新音樂
    if settings.musicTrack then
        local AudioController = self._worldManager:getSystem("AudioController")
        if AudioController then
            -- 這裡可以觸發音樂變更事件
        end
    end
    
    -- 更新戰鬥設置
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if playerEntity then
        local combat = self._world:get(playerEntity, Components.CombatComponent)
        if combat then
            -- 如果在安全區域且正在戰鬥，結束戰鬥
            if not settings.allowCombat and combat.isInCombat then
                local CombatSystem = self._worldManager:getSystem("CombatSystem")
                if CombatSystem then
                    CombatSystem:endCombat(self._world, playerEntity, "safe_zone")
                end
            end
        end
    end
end

-- 觸發區域特定邏輯
function ZoneService:_triggerZoneLogic(player, zoneId, action)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig then
        return
    end
    
    if action == "enter" then
        -- 進入區域時的邏輯
        if zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
            self:_startMonsterSpawning(zoneId)
            self:_startPetSpawning(zoneId)
        elseif zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
            self:_handlePvPZoneEntry(player, zoneId)
        elseif zoneConfig.type == ZoneConfig.ZoneTypes.EVENT then
            self:_handleEventZoneEntry(player, zoneId)
        end
        
    elseif action == "exit" then
        -- 離開區域時的邏輯
        if zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
            self:_handlePvPZoneExit(player, zoneId)
        end
    end
end

-- 啟動怪物生成
function ZoneService:_startMonsterSpawning(zoneId)
    local MonsterSystem = self._worldManager:getSystem("MonsterSystem")
    if MonsterSystem then
        MonsterSystem:startSpawningInZone(self._world, zoneId)
    end
end

-- 啟動寵物生成
function ZoneService:_startPetSpawning(zoneId)
    local PetSystem = self._worldManager:getSystem("PetSystem")
    if PetSystem then
        PetSystem:startWildPetSpawning(self._world, zoneId)
    end
end

-- 處理 PvP 區域進入
function ZoneService:_handlePvPZoneEntry(player, zoneId)
    print("⚔️ 玩家進入 PvP 區域:", player.Name)
    -- 這裡可以添加 PvP 匹配邏輯
end

-- 處理 PvP 區域離開
function ZoneService:_handlePvPZoneExit(player, zoneId)
    print("🏃 玩家離開 PvP 區域:", player.Name)
    -- 這裡可以添加 PvP 清理邏輯
end

-- 處理事件區域進入
function ZoneService:_handleEventZoneEntry(player, zoneId)
    print("🎉 玩家進入事件區域:", player.Name)
    -- 這裡可以添加事件邏輯
end

-- 啟動區域管理系統
function ZoneService:_startZoneManagement()
    local RunService = game:GetService("RunService")
    
    -- 定期檢查區域狀態
    RunService.Heartbeat:Connect(function()
        if tick() % 5 < 0.1 then -- 每5秒檢查一次
            self:_updateZoneStates()
        end
    end)
end

-- 更新區域狀態
function ZoneService:_updateZoneStates()
    for zoneId, zone in pairs(self._zones) do
        local zoneConfig = ZoneConfig:GetZone(zoneId)
        if zoneConfig and zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
            self:_updateHuntingZone(zoneId, zoneConfig)
        end
    end
end

-- 更新狩獵區域
function ZoneService:_updateHuntingZone(zoneId, zoneConfig)
    -- 檢查怪物數量
    local MonsterSystem = self._worldManager:getSystem("MonsterSystem")
    if MonsterSystem then
        MonsterSystem:maintainZoneMonsters(self._world, zoneId)
    end
    
    -- 檢查野生寵物數量
    local PetSystem = self._worldManager:getSystem("PetSystem")
    if PetSystem then
        PetSystem:maintainWildPets(self._world, zoneId)
    end
end

-- 客戶端方法：獲取當前區域
function ZoneService.Client:GetCurrentZone(player)
    return self.Server:GetCurrentZone(player)
end

function ZoneService:GetCurrentZone(player)
    local currentZone = self._playerZones[player.UserId]
    if currentZone then
        local zoneConfig = ZoneConfig:GetZone(currentZone)
        return {
            success = true,
            zoneId = currentZone,
            zoneConfig = zoneConfig
        }
    else
        return {
            success = false,
            error = "玩家不在任何區域中"
        }
    end
end

-- 客戶端方法：獲取可用區域
function ZoneService.Client:GetAvailableZones(player)
    return self.Server:GetAvailableZones(player)
end

function ZoneService:GetAvailableZones(player)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local hero = self._world:get(playerEntity, Components.HeroComponent)
    if not hero then
        return { success = false, error = "英雄數據未找到" }
    end
    
    local availableZones = {}
    local lockedZones = {}
    
    for zoneId, zoneConfig in pairs(ZoneConfig:GetAllZones()) do
        local canEnter, reason = ZoneConfig:CanPlayerEnterZone(
            hero.level,
            {}, -- TODO: 獲取完成的任務
            {}, -- TODO: 獲取擊敗的 Boss
            zoneId
        )
        
        if canEnter then
            table.insert(availableZones, {
                zoneId = zoneId,
                config = zoneConfig,
                teleportCost = ZoneConfig:GetTeleportCost(zoneId)
            })
        else
            table.insert(lockedZones, {
                zoneId = zoneId,
                config = zoneConfig,
                lockReason = reason
            })
        end
    end
    
    return {
        success = true,
        availableZones = availableZones,
        lockedZones = lockedZones
    }
end

-- 客戶端方法：傳送到區域
function ZoneService.Client:TeleportToZone(player, zoneId)
    return self.Server:TeleportToZone(player, zoneId)
end

function ZoneService:TeleportToZone(player, zoneId)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig then
        return { success = false, error = "區域不存在" }
    end
    
    -- 檢查玩家是否可以進入區域
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local hero = self._world:get(playerEntity, Components.HeroComponent)
    if not hero then
        return { success = false, error = "英雄數據未找到" }
    end
    
    local canEnter, reason = ZoneConfig:CanPlayerEnterZone(
        hero.level, {}, {}, zoneId
    )
    
    if not canEnter then
        return { success = false, error = reason }
    end
    
    -- 檢查傳送費用
    local teleportCost = ZoneConfig:GetTeleportCost(zoneId)
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    
    if currency and teleportCost.coins > 0 then
        if currency.coins < teleportCost.coins then
            return {
                success = false,
                error = "金幣不足",
                required = teleportCost.coins,
                current = currency.coins
            }
        end
        
        -- 扣除傳送費用
        self._world:insert(playerEntity, Components.CurrencyComponent({
            coins = currency.coins - teleportCost.coins,
            gems = currency.gems,
            robux = currency.robux,
            experience = currency.experience
        }))
    end
    
    -- 執行傳送
    local success = self:_teleportPlayer(player, zoneId)
    
    if success then
        self.Client.OnTeleportCompleted:Fire(player, {
            zoneId = zoneId,
            zoneName = zoneConfig.name,
            cost = teleportCost,
            timestamp = tick()
        })
        
        return { success = true, zoneId = zoneId }
    else
        return { success = false, error = "傳送失敗" }
    end
end

-- 執行玩家傳送
function ZoneService:_teleportPlayer(player, zoneId)
    local character = player.Character
    if not character then
        return false
    end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then
        return false
    end
    
    -- 尋找傳送點
    local spawnPoint = self:_findSpawnPoint(zoneId)
    if spawnPoint then
        humanoidRootPart.CFrame = spawnPoint
        return true
    end
    
    return false
end

-- 尋找區域生成點
function ZoneService:_findSpawnPoint(zoneId)
    local workspace = game:GetService("Workspace")
    local spawnName = zoneId .. "_spawn"
    local spawn = workspace:FindFirstChild(spawnName)
    
    if spawn and spawn:IsA("SpawnLocation") then
        return spawn.CFrame + Vector3.new(0, 5, 0)
    elseif spawn and spawn:IsA("Part") then
        return spawn.CFrame + Vector3.new(0, 5, 0)
    end
    
    -- 如果找不到專用生成點，使用區域中心
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if zoneConfig and zoneConfig.boundaries.partName then
        local zonePart = workspace:FindFirstChild(zoneConfig.boundaries.partName)
        if zonePart then
            return zonePart.CFrame + Vector3.new(0, 10, 0)
        end
    end
    
    return nil
end

-- 客戶端方法：獲取區域信息
function ZoneService.Client:GetZoneInfo(player, zoneId)
    return self.Server:GetZoneInfo(player, zoneId)
end

function ZoneService:GetZoneInfo(player, zoneId)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig then
        return { success = false, error = "區域不存在" }
    end
    
    -- 獲取區域中的玩家數量
    local zone = self._zones[zoneId]
    local playerCount = 0
    if zone then
        playerCount = #zone:getPlayers()
    end
    
    return {
        success = true,
        zoneInfo = {
            config = zoneConfig,
            playerCount = playerCount,
            teleportCost = ZoneConfig:GetTeleportCost(zoneId)
        }
    }
end

-- 獲取玩家當前區域
function ZoneService:getPlayerZone(userId)
    return self._playerZones[userId]
end

-- 獲取區域中的所有玩家
function ZoneService:getPlayersInZone(zoneId)
    local zone = self._zones[zoneId]
    if zone then
        return zone:getPlayers()
    end
    return {}
end

-- 檢查玩家是否在特定區域
function ZoneService:isPlayerInZone(userId, zoneId)
    return self._playerZones[userId] == zoneId
end

return ZoneService
