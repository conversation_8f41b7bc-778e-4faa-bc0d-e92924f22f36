-- 升級系統測試
return function()
    local Matter = require(game.ReplicatedStorage.Packages.Matter)
    local Components = require(game.ReplicatedStorage.Components)
    local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
    local Utils = require(game.ReplicatedStorage.Shared.Utils)
    
    describe("LevelingSystem", function()
        local world
        local levelingSystem
        
        beforeEach(function()
            -- 創建測試世界
            world = Matter.World.new()
            
            -- 創建升級系統實例
            local LevelingSystem = require(game.ServerScriptService.Systems.LevelingSystem)
            levelingSystem = LevelingSystem.new()
        end)
        
        afterEach(function()
            if world then
                world:clear()
            end
        end)
        
        it("應該正確計算升級所需經驗", function()
            -- 測試不同等級的經驗需求
            local testCases = {
                { level = 1, expectedExp = 0 },    -- 等級1不需要經驗
                { level = 2, expectedExp = 100 },  -- 等級2需要100經驗
                { level = 3, expectedExp = 250 },  -- 等級3需要250經驗
                { level = 5, expectedExp = 625 },  -- 等級5需要625經驗
                { level = 10, expectedExp = 2500 } -- 等級10需要2500經驗
            }
            
            for _, testCase in ipairs(testCases) do
                local requiredExp = Utils.calculateExpForLevel(testCase.level)
                expect(requiredExp).to.equal(testCase.expectedExp)
            end
        end)
        
        it("應該正確處理經驗值獲得", function()
            -- 創建英雄實體
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                })
            )
            
            -- 給予經驗值
            local expGain = 50
            local hero = world:get(heroEntity, Components.HeroComponent)
            
            world:insert(heroEntity, Components.HeroComponent({
                level = hero.level,
                experience = hero.experience + expGain,
                skillPoints = hero.skillPoints
            }))
            
            -- 驗證經驗值更新
            local updatedHero = world:get(heroEntity, Components.HeroComponent)
            expect(updatedHero.experience).to.equal(50)
            expect(updatedHero.level).to.equal(1) -- 還未達到升級要求
        end)
        
        it("應該正確處理單次升級", function()
            -- 創建接近升級的英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 80,
                    skillPoints = 0
                }),
                Components.HealthComponent({
                    current = 150,
                    maximum = 150,
                    regeneration = 1,
                    lastRegenTime = tick()
                }),
                Components.StatsComponent({
                    attack = 15,
                    defense = 8,
                    critRate = 0.05,
                    critDamage = 1.5
                })
            )
            
            -- 給予足夠的經驗值升級
            local expGain = 30 -- 總經驗變為110，超過100
            local hero = world:get(heroEntity, Components.HeroComponent)
            local newTotalExp = hero.experience + expGain
            
            -- 檢查是否需要升級
            local currentLevel = hero.level
            local requiredExpForNextLevel = Utils.calculateExpForLevel(currentLevel + 1)
            
            if newTotalExp >= requiredExpForNextLevel then
                -- 升級
                local newLevel = currentLevel + 1
                local remainingExp = newTotalExp - requiredExpForNextLevel
                
                world:insert(heroEntity, Components.HeroComponent({
                    level = newLevel,
                    experience = remainingExp,
                    skillPoints = hero.skillPoints + 1
                }))
                
                -- 更新屬性
                local stats = world:get(heroEntity, Components.StatsComponent)
                local health = world:get(heroEntity, Components.HealthComponent)
                
                local levelBonus = GameConfig.HERO.STATS_PER_LEVEL
                local healthBonus = GameConfig.HERO.HEALTH_PER_LEVEL
                
                world:insert(heroEntity, Components.StatsComponent({
                    attack = stats.attack + levelBonus,
                    defense = stats.defense + levelBonus,
                    critRate = stats.critRate,
                    critDamage = stats.critDamage
                }))
                
                world:insert(heroEntity, Components.HealthComponent({
                    current = health.maximum + healthBonus,
                    maximum = health.maximum + healthBonus,
                    regeneration = health.regeneration,
                    lastRegenTime = health.lastRegenTime
                }))
            end
            
            -- 驗證升級結果
            local upgradedHero = world:get(heroEntity, Components.HeroComponent)
            local upgradedStats = world:get(heroEntity, Components.StatsComponent)
            local upgradedHealth = world:get(heroEntity, Components.HealthComponent)
            
            expect(upgradedHero.level).to.equal(2)
            expect(upgradedHero.experience).to.equal(10) -- 110 - 100 = 10
            expect(upgradedHero.skillPoints).to.equal(1)
            expect(upgradedStats.attack).to.equal(15 + GameConfig.HERO.STATS_PER_LEVEL)
            expect(upgradedHealth.maximum).to.equal(150 + GameConfig.HERO.HEALTH_PER_LEVEL)
        end)
        
        it("應該正確處理多次升級", function()
            -- 創建低等級英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                })
            )
            
            -- 給予大量經驗值，足以升多級
            local massiveExpGain = 1000 -- 足以升到等級4+
            local hero = world:get(heroEntity, Components.HeroComponent)
            
            local currentLevel = hero.level
            local currentExp = hero.experience + massiveExpGain
            local newSkillPoints = hero.skillPoints
            
            -- 計算最終等級
            while true do
                local requiredExp = Utils.calculateExpForLevel(currentLevel + 1)
                if currentExp >= requiredExp then
                    currentExp = currentExp - requiredExp
                    currentLevel = currentLevel + 1
                    newSkillPoints = newSkillPoints + 1
                    
                    -- 防止無限循環
                    if currentLevel >= GameConfig.HERO.MAX_LEVEL then
                        currentExp = 0
                        break
                    end
                else
                    break
                end
            end
            
            world:insert(heroEntity, Components.HeroComponent({
                level = currentLevel,
                experience = currentExp,
                skillPoints = newSkillPoints
            }))
            
            -- 驗證多次升級
            local upgradedHero = world:get(heroEntity, Components.HeroComponent)
            expect(upgradedHero.level).to.be.greaterThan(1)
            expect(upgradedHero.skillPoints).to.equal(upgradedHero.level - 1)
        end)
        
        it("應該正確處理最大等級限制", function()
            -- 創建最大等級英雄
            local maxLevel = GameConfig.HERO.MAX_LEVEL
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = maxLevel,
                    experience = 0,
                    skillPoints = maxLevel - 1
                })
            )
            
            -- 嘗試給予經驗值
            local expGain = 1000
            local hero = world:get(heroEntity, Components.HeroComponent)
            
            -- 在最大等級時，經驗值不應該增加
            if hero.level >= maxLevel then
                -- 不增加經驗值
                world:insert(heroEntity, Components.HeroComponent({
                    level = hero.level,
                    experience = hero.experience, -- 保持不變
                    skillPoints = hero.skillPoints
                }))
            end
            
            -- 驗證最大等級限制
            local updatedHero = world:get(heroEntity, Components.HeroComponent)
            expect(updatedHero.level).to.equal(maxLevel)
            expect(updatedHero.experience).to.equal(0)
        end)
        
        it("應該正確計算等級差異經驗加成", function()
            -- 測試不同等級差異的經驗加成
            local playerLevel = 5
            local testCases = {
                { monsterLevel = 3, expectedMultiplier = 0.8 }, -- 低等級怪物
                { monsterLevel = 5, expectedMultiplier = 1.0 }, -- 同等級怪物
                { monsterLevel = 7, expectedMultiplier = 1.2 }, -- 高等級怪物
                { monsterLevel = 10, expectedMultiplier = 1.5 } -- 高等級怪物
            }
            
            for _, testCase in ipairs(testCases) do
                local levelDifference = testCase.monsterLevel - playerLevel
                local multiplier = 1.0 + (levelDifference * 0.1)
                
                -- 限制倍數範圍
                multiplier = math.max(0.5, math.min(2.0, multiplier))
                
                expect(math.abs(multiplier - testCase.expectedMultiplier)).to.be.lessThan(0.01)
            end
        end)
        
        it("應該正確處理技能點獲得", function()
            -- 創建英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                })
            )
            
            -- 模擬升級獲得技能點
            local levelsGained = 3
            local hero = world:get(heroEntity, Components.HeroComponent)
            
            world:insert(heroEntity, Components.HeroComponent({
                level = hero.level + levelsGained,
                experience = 0,
                skillPoints = hero.skillPoints + levelsGained
            }))
            
            -- 驗證技能點獲得
            local updatedHero = world:get(heroEntity, Components.HeroComponent)
            expect(updatedHero.skillPoints).to.equal(levelsGained)
            expect(updatedHero.level).to.equal(1 + levelsGained)
        end)
        
        it("應該正確處理屬性成長", function()
            -- 創建英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 1,
                    experience = 0,
                    skillPoints = 0
                }),
                Components.StatsComponent({
                    attack = 15,
                    defense = 8,
                    critRate = 0.05,
                    critDamage = 1.5
                }),
                Components.HealthComponent({
                    current = 150,
                    maximum = 150,
                    regeneration = 1,
                    lastRegenTime = tick()
                })
            )
            
            -- 模擬升級
            local levelsGained = 5
            local statsPerLevel = GameConfig.HERO.STATS_PER_LEVEL
            local healthPerLevel = GameConfig.HERO.HEALTH_PER_LEVEL
            
            local stats = world:get(heroEntity, Components.StatsComponent)
            local health = world:get(heroEntity, Components.HealthComponent)
            
            world:insert(heroEntity, Components.StatsComponent({
                attack = stats.attack + (levelsGained * statsPerLevel),
                defense = stats.defense + (levelsGained * statsPerLevel),
                critRate = stats.critRate,
                critDamage = stats.critDamage
            }))
            
            world:insert(heroEntity, Components.HealthComponent({
                current = health.maximum + (levelsGained * healthPerLevel),
                maximum = health.maximum + (levelsGained * healthPerLevel),
                regeneration = health.regeneration,
                lastRegenTime = health.lastRegenTime
            }))
            
            -- 驗證屬性成長
            local upgradedStats = world:get(heroEntity, Components.StatsComponent)
            local upgradedHealth = world:get(heroEntity, Components.HealthComponent)
            
            expect(upgradedStats.attack).to.equal(15 + (levelsGained * statsPerLevel))
            expect(upgradedStats.defense).to.equal(8 + (levelsGained * statsPerLevel))
            expect(upgradedHealth.maximum).to.equal(150 + (levelsGained * healthPerLevel))
        end)
        
        it("應該正確處理經驗值溢出", function()
            -- 創建接近升級的英雄
            local heroEntity = world:spawn(
                Components.HeroComponent({
                    level = 2,
                    experience = 200, -- 等級2需要250經驗，還差50
                    skillPoints = 1
                })
            )
            
            -- 給予超過升級需求的經驗值
            local expGain = 100 -- 超過50的需求
            local hero = world:get(heroEntity, Components.HeroComponent)
            
            local totalExp = hero.experience + expGain -- 300
            local requiredForLevel3 = Utils.calculateExpForLevel(3) -- 250
            
            if totalExp >= requiredForLevel3 then
                local remainingExp = totalExp - requiredForLevel3 -- 300 - 250 = 50
                
                world:insert(heroEntity, Components.HeroComponent({
                    level = 3,
                    experience = remainingExp,
                    skillPoints = hero.skillPoints + 1
                }))
            end
            
            -- 驗證經驗值溢出處理
            local upgradedHero = world:get(heroEntity, Components.HeroComponent)
            expect(upgradedHero.level).to.equal(3)
            expect(upgradedHero.experience).to.equal(50) -- 溢出的經驗值
        end)
    end)
end
