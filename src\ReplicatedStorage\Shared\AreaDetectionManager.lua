-- 區域檢測管理系統
local AreaDetectionManager = {}

-- 依賴
local WorldMapConfig = require(game.ReplicatedStorage.Configuration.WorldMapConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 玩家位置追蹤
AreaDetectionManager.playerAreas = {} -- playerId -> currentArea
AreaDetectionManager.playerPositions = {} -- playerId -> lastPosition
AreaDetectionManager.isInitialized = false
AreaDetectionManager.updateInterval = 2 -- 每2秒檢查一次位置

-- 初始化區域檢測系統
function AreaDetectionManager:Initialize()
    if self.isInitialized then
        warn("⚠️ 區域檢測系統已經初始化")
        return true
    end
    
    print("🗺️ 初始化區域檢測系統...")
    
    -- 重置數據
    self.playerAreas = {}
    self.playerPositions = {}
    
    -- 設置事件處理器
    self:SetupEventHandlers()
    
    -- 開始位置檢測循環
    self:StartPositionTracking()
    
    self.isInitialized = true
    print("✅ 區域檢測系統初始化完成")
    
    return true
end

-- 設置事件處理器
function AreaDetectionManager:SetupEventHandlers()
    -- 監聽玩家加入事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_JOINED, function(data)
        self:OnPlayerJoined(data.player)
    end)
    
    -- 監聽玩家離開事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_LEFT, function(data)
        self:OnPlayerLeft(data.player)
    end)
end

-- 玩家加入事件處理
function AreaDetectionManager:OnPlayerJoined(player)
    -- 初始化玩家數據
    self.playerAreas[player.UserId] = nil
    self.playerPositions[player.UserId] = nil
    
    -- 立即檢查玩家位置
    self:CheckPlayerPosition(player)
    
    print(string.format("👤 開始追蹤 %s 的位置", player.Name))
end

-- 玩家離開事件處理
function AreaDetectionManager:OnPlayerLeft(player)
    -- 如果玩家在某個區域，觸發離開事件
    local currentArea = self.playerAreas[player.UserId]
    if currentArea then
        self:TriggerAreaLeftEvent(player, currentArea)
    end
    
    -- 清理玩家數據
    self.playerAreas[player.UserId] = nil
    self.playerPositions[player.UserId] = nil
    
    print(string.format("👤 停止追蹤 %s 的位置", player.Name))
end

-- 開始位置追蹤
function AreaDetectionManager:StartPositionTracking()
    task.spawn(function()
        while self.isInitialized do
            self:UpdateAllPlayerPositions()
            task.wait(self.updateInterval)
        end
    end)
    
    print("🔄 位置追蹤循環已啟動")
end

-- 更新所有玩家位置
function AreaDetectionManager:UpdateAllPlayerPositions()
    local players = game.Players:GetPlayers()
    
    for _, player in ipairs(players) do
        if self.playerAreas[player.UserId] ~= nil then -- 玩家已初始化
            self:CheckPlayerPosition(player)
        end
    end
end

-- 檢查玩家位置
function AreaDetectionManager:CheckPlayerPosition(player)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local position = player.Character.HumanoidRootPart.Position
    local currentArea = self.playerAreas[player.UserId]
    local newArea = self:DeterminePlayerArea(position)
    
    -- 更新位置記錄
    self.playerPositions[player.UserId] = position
    
    -- 檢查區域變化
    if currentArea ~= newArea then
        self:HandleAreaChange(player, currentArea, newArea)
    end
end

-- 確定玩家所在區域
function AreaDetectionManager:DeterminePlayerArea(position)
    -- 首先檢查是否在安全區
    if WorldMapConfig.AREA_UTILS.isInSafeZone(position) then
        return {
            type = "SAFE_ZONE",
            name = "CASTLE",
            config = WorldMapConfig.MAIN_WORLD.safeZone
        }
    end
    
    -- 檢查野外區域
    local areaName, areaConfig = WorldMapConfig.AREA_UTILS.getWildArea(position)
    if areaName and areaConfig then
        return {
            type = "WILD_AREA",
            name = areaName,
            config = areaConfig
        }
    end
    
    -- 不在任何已知區域
    return {
        type = "UNKNOWN",
        name = "UNKNOWN",
        config = nil
    }
end

-- 處理區域變化
function AreaDetectionManager:HandleAreaChange(player, oldArea, newArea)
    -- 觸發離開舊區域事件
    if oldArea then
        self:TriggerAreaLeftEvent(player, oldArea)
    end
    
    -- 更新玩家當前區域
    self.playerAreas[player.UserId] = newArea
    
    -- 觸發進入新區域事件
    if newArea then
        self:TriggerAreaEnteredEvent(player, newArea)
    end
    
    -- 記錄區域變化
    local oldAreaName = oldArea and oldArea.name or "無"
    local newAreaName = newArea and newArea.name or "無"
    
    print(string.format("🗺️ %s 從 %s 移動到 %s", player.Name, oldAreaName, newAreaName))
end

-- 觸發進入區域事件
function AreaDetectionManager:TriggerAreaEnteredEvent(player, area)
    -- 通用區域進入事件
    EventManager:Fire(EventManager.EventTypes.PLAYER_ENTERED_AREA, {
        player = player,
        areaType = area.type,
        areaName = area.name,
        areaConfig = area.config
    })
    
    -- 特定類型的區域事件
    if area.type == "SAFE_ZONE" then
        EventManager:Fire(EventManager.EventTypes.PLAYER_ENTERED_SAFE_ZONE, {
            player = player,
            areaConfig = area.config
        })
        
        self:OnPlayerEnteredSafeZone(player, area)
        
    elseif area.type == "WILD_AREA" then
        EventManager:Fire(EventManager.EventTypes.PLAYER_ENTERED_WILD_AREA, {
            player = player,
            areaName = area.name,
            areaConfig = area.config
        })
        
        self:OnPlayerEnteredWildArea(player, area)
    end
end

-- 觸發離開區域事件
function AreaDetectionManager:TriggerAreaLeftEvent(player, area)
    -- 通用區域離開事件
    EventManager:Fire(EventManager.EventTypes.PLAYER_LEFT_AREA, {
        player = player,
        areaType = area.type,
        areaName = area.name,
        areaConfig = area.config
    })
    
    -- 特定類型的區域事件
    if area.type == "SAFE_ZONE" then
        EventManager:Fire(EventManager.EventTypes.PLAYER_LEFT_SAFE_ZONE, {
            player = player,
            areaConfig = area.config
        })
        
        self:OnPlayerLeftSafeZone(player, area)
        
    elseif area.type == "WILD_AREA" then
        EventManager:Fire(EventManager.EventTypes.PLAYER_LEFT_WILD_AREA, {
            player = player,
            areaName = area.name,
            areaConfig = area.config
        })
        
        self:OnPlayerLeftWildArea(player, area)
    end
end

-- 玩家進入安全區處理
function AreaDetectionManager:OnPlayerEnteredSafeZone(player, area)
    print(string.format("🏰 %s 進入了安全區", player.Name))
    
    -- 安全區特殊處理
    -- 1. 停止戰鬥
    EventManager:Fire(EventManager.EventTypes.COMBAT_ENDED, {
        player = player,
        reason = "entered_safe_zone"
    })
    
    -- 2. 開始快速回復
    self:StartSafeZoneRegeneration(player)
    
    -- 3. 顯示歡迎信息
    EventManager:Fire(EventManager.EventTypes.SHOW_NOTIFICATION, {
        player = player,
        message = "歡迎回到安全的城堡！",
        type = "info"
    })
end

-- 玩家離開安全區處理
function AreaDetectionManager:OnPlayerLeftSafeZone(player, area)
    print(string.format("🏰 %s 離開了安全區", player.Name))
    
    -- 停止安全區回復
    self:StopSafeZoneRegeneration(player)
    
    -- 顯示警告信息
    EventManager:Fire(EventManager.EventTypes.SHOW_NOTIFICATION, {
        player = player,
        message = "你已離開安全區，小心野外的怪物！",
        type = "warning"
    })
end

-- 玩家進入野外區域處理
function AreaDetectionManager:OnPlayerEnteredWildArea(player, area)
    local areaConfig = area.config
    print(string.format("🌲 %s 進入了 %s", player.Name, areaConfig.name))
    
    -- 檢查玩家等級是否適合
    local playerLevel = self:GetPlayerLevel(player)
    if not WorldMapConfig.AREA_UTILS.isLevelSuitableForArea(playerLevel, areaConfig) then
        local message = string.format("警告：此區域建議等級 %d-%d，你的等級是 %d", 
                                     areaConfig.recommendedLevel.min, 
                                     areaConfig.recommendedLevel.max, 
                                     playerLevel)
        
        EventManager:Fire(EventManager.EventTypes.SHOW_NOTIFICATION, {
            player = player,
            message = message,
            type = "warning"
        })
    end
    
    -- 顯示區域信息
    local message = string.format("進入 %s (%s)", areaConfig.name, areaConfig.difficulty)
    EventManager:Fire(EventManager.EventTypes.SHOW_NOTIFICATION, {
        player = player,
        message = message,
        type = "info"
    })
end

-- 玩家離開野外區域處理
function AreaDetectionManager:OnPlayerLeftWildArea(player, area)
    local areaConfig = area.config
    print(string.format("🌲 %s 離開了 %s", player.Name, areaConfig.name))
end

-- 開始安全區回復
function AreaDetectionManager:StartSafeZoneRegeneration(player)
    -- 這裡應該與英雄系統整合
    -- 暫時觸發事件讓其他系統處理
    EventManager:Fire(EventManager.EventTypes.START_SAFE_ZONE_REGEN, {
        player = player
    })
end

-- 停止安全區回復
function AreaDetectionManager:StopSafeZoneRegeneration(player)
    EventManager:Fire(EventManager.EventTypes.STOP_SAFE_ZONE_REGEN, {
        player = player
    })
end

-- 獲取玩家等級 (模擬)
function AreaDetectionManager:GetPlayerLevel(player)
    -- 這裡應該從英雄系統獲取真實等級
    -- 暫時返回隨機等級用於測試
    return math.random(1, 50)
end

-- 獲取玩家當前區域
function AreaDetectionManager:GetPlayerArea(player)
    return self.playerAreas[player.UserId]
end

-- 獲取玩家當前位置
function AreaDetectionManager:GetPlayerPosition(player)
    return self.playerPositions[player.UserId]
end

-- 獲取區域內的玩家
function AreaDetectionManager:GetPlayersInArea(areaType, areaName)
    local playersInArea = {}
    
    for playerId, area in pairs(self.playerAreas) do
        if area and area.type == areaType and area.name == areaName then
            local player = game.Players:GetPlayerByUserId(playerId)
            if player then
                table.insert(playersInArea, player)
            end
        end
    end
    
    return playersInArea
end

-- 獲取安全區內的玩家
function AreaDetectionManager:GetPlayersInSafeZone()
    return self:GetPlayersInArea("SAFE_ZONE", "CASTLE")
end

-- 獲取野外區域內的玩家
function AreaDetectionManager:GetPlayersInWildArea(areaName)
    return self:GetPlayersInArea("WILD_AREA", areaName)
end

-- 強制檢查所有玩家位置
function AreaDetectionManager:ForceUpdateAllPlayers()
    print("🔄 強制更新所有玩家位置...")
    self:UpdateAllPlayerPositions()
end

-- 獲取統計信息
function AreaDetectionManager:GetStats()
    local stats = {
        totalPlayers = 0,
        playersInSafeZone = 0,
        playersInWildAreas = {},
        playersInUnknownAreas = 0
    }
    
    for playerId, area in pairs(self.playerAreas) do
        stats.totalPlayers = stats.totalPlayers + 1
        
        if area.type == "SAFE_ZONE" then
            stats.playersInSafeZone = stats.playersInSafeZone + 1
        elseif area.type == "WILD_AREA" then
            stats.playersInWildAreas[area.name] = (stats.playersInWildAreas[area.name] or 0) + 1
        else
            stats.playersInUnknownAreas = stats.playersInUnknownAreas + 1
        end
    end
    
    return stats
end

-- 調試信息
function AreaDetectionManager:Debug()
    print("🗺️ 區域檢測系統調試信息:")
    print("  初始化狀態:", self.isInitialized)
    print("  更新間隔:", self.updateInterval, "秒")
    
    local stats = self:GetStats()
    print("  總玩家數:", stats.totalPlayers)
    print("  安全區玩家:", stats.playersInSafeZone)
    print("  未知區域玩家:", stats.playersInUnknownAreas)
    
    print("  野外區域玩家分佈:")
    for areaName, count in pairs(stats.playersInWildAreas) do
        print(string.format("    %s: %d", areaName, count))
    end
    
    print("  玩家詳細位置:")
    for playerId, area in pairs(self.playerAreas) do
        local player = game.Players:GetPlayerByUserId(playerId)
        if player then
            print(string.format("    %s: %s (%s)", player.Name, area.name, area.type))
        end
    end
end

-- 停止系統
function AreaDetectionManager:Shutdown()
    if not self.isInitialized then
        return
    end
    
    print("🛑 停止區域檢測系統...")
    
    -- 清理數據
    self.playerAreas = {}
    self.playerPositions = {}
    self.isInitialized = false
    
    print("✅ 區域檢測系統已停止")
end

return AreaDetectionManager
