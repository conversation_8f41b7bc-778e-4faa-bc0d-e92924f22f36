-- 怪物-寵物統一系統 - 主入口
-- 97隻生物既是地圖怪物也是收集寵物的統一管理系統

local MonsterPetSystem = {}

-- 依賴
local MonsterConfig = require(game.ReplicatedStorage.Configuration.MonsterConfig)
local MonsterManager = require(game.ReplicatedStorage.Shared.MonsterManager)
local GachaSystem = require(game.ReplicatedStorage.Shared.GachaSystem)
local VisualEffectsManager = require(game.ReplicatedStorage.Shared.VisualEffectsManager)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)
-- 移除 Utils 依賴，直接實現需要的功能

-- 系統狀態
MonsterPetSystem.isInitialized = false
MonsterPetSystem.stats = {
    totalMonsters = 0,
    monstersPerZone = {},
    monstersPerRarity = {},
    totalGachaRolls = 0
}

-- 初始化系統
function MonsterPetSystem:Initialize()
    if self.isInitialized then
        warn("⚠️ 怪物-寵物系統已經初始化")
        return true
    end
    
    print("🐾 初始化怪物-寵物統一系統...")
    
    -- 初始化各個子系統
    local success = true
    
    -- 初始化怪物管理器
    local managerSuccess = pcall(function()
        MonsterManager:Initialize()
    end)
    if not managerSuccess then
        warn("❌ 怪物管理器初始化失敗")
        success = false
    end
    
    -- 初始化扭蛋系統
    local gachaSuccess = pcall(function()
        GachaSystem:Initialize()
    end)
    if not gachaSuccess then
        warn("❌ 扭蛋系統初始化失敗")
        success = false
    end
    
    -- 初始化視覺效果管理器
    local visualSuccess = pcall(function()
        VisualEffectsManager:Initialize()
    end)
    if not visualSuccess then
        warn("❌ 視覺效果管理器初始化失敗")
        success = false
    end
    
    if success then
        self.isInitialized = true
        self:UpdateStats()
        self:SetupEventHandlers()
        
        print("✅ 怪物-寵物統一系統初始化完成")
        print("📊 系統統計:")
        print("  總怪物數:", self.stats.totalMonsters)
        print("  森林區域:", self.stats.monstersPerZone.FOREST or 0, "隻")
        print("  冰雪區域:", self.stats.monstersPerZone.ICE or 0, "隻")
        print("  熔岩區域:", self.stats.monstersPerZone.LAVA or 0, "隻")
        
        -- 觸發系統初始化事件
        EventManager:Fire(EventManager.EventTypes.SYSTEM_INITIALIZED, {
            system = "MonsterPetSystem",
            totalMonsters = self.stats.totalMonsters
        })
    else
        warn("❌ 怪物-寵物系統初始化失敗")
    end
    
    return success
end

-- 更新統計信息
function MonsterPetSystem:UpdateStats()
    self.stats.totalMonsters = MonsterManager:GetTotalMonsterCount()
    
    -- 統計各區域怪物數量
    for zone in pairs(MonsterConfig.ZONES) do
        local monsters = MonsterManager:GetMonstersByZone(zone)
        self.stats.monstersPerZone[zone] = #monsters
    end
    
    -- 統計各稀有度怪物數量
    for rarity in pairs(MonsterConfig.RARITY) do
        local monsters = MonsterManager:GetMonstersByRarity(rarity)
        self.stats.monstersPerRarity[rarity] = #monsters
    end
    
    -- 獲取扭蛋統計
    local success, gachaStats = pcall(function()
        return GachaSystem:GetStats()
    end)

    if success and gachaStats then
        self.stats.totalGachaRolls = gachaStats.totalRolls or 0
    else
        self.stats.totalGachaRolls = 0
    end
end

-- 設置事件處理器
function MonsterPetSystem:SetupEventHandlers()
    -- 監聽寵物獲得事件
    EventManager:Connect(EventManager.EventTypes.PET_OBTAINED, function(data)
        self:OnPetObtained(data)
    end)
    
    -- 監聽區域進入事件
    EventManager:Connect(EventManager.EventTypes.ZONE_ENTERED, function(data)
        self:OnZoneEntered(data)
    end)
    
    -- 監聽戰鬥開始事件
    EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
        self:OnCombatStarted(data)
    end)
end

-- 寵物獲得事件處理
function MonsterPetSystem:OnPetObtained(data)
    local monsterData = MonsterManager:GetMonsterData(data.petName)
    if monsterData then
        print(string.format("🎉 %s 獲得了 %s (%s級)", 
              data.player.Name, data.petName, monsterData.rarity))
        
        -- 應用視覺效果
        if data.petModel then
            VisualEffectsManager:ApplyRarityEffects(data.petModel, monsterData.rarity)
        end
    end
end

-- 區域進入事件處理
function MonsterPetSystem:OnZoneEntered(data)
    local zoneConfig = MonsterConfig.ZONES[data.zoneId]
    if zoneConfig then
        print(string.format("🗺️ %s 進入了 %s", data.player.Name, zoneConfig.name))
        
        -- 可以在這裡觸發怪物生成
        self:SpawnMonstersInZone(data.zoneId, data.player)
    end
end

-- 戰鬥開始事件處理
function MonsterPetSystem:OnCombatStarted(data)
    print(string.format("⚔️ 戰鬥開始: %s vs %s", data.attacker, data.target))
    
    -- 可以在這裡應用戰鬥視覺效果
end

-- 在區域生成怪物
function MonsterPetSystem:SpawnMonstersInZone(zoneId, player)
    local randomMonster = MonsterManager:GetRandomMonster(zoneId)
    if randomMonster then
        local monsterData = MonsterManager:GetMonsterData(randomMonster)
        
        print(string.format("👾 在 %s 生成了 %s (%s級)", 
              zoneId, randomMonster, monsterData.rarity))
        
        -- 觸發野生寵物生成事件
        EventManager:Fire(EventManager.EventTypes.WILD_PET_SPAWNED, {
            petName = randomMonster,
            zone = zoneId,
            rarity = monsterData.rarity,
            player = player
        })
    end
end

-- 執行扭蛋
function MonsterPetSystem:PerformGacha(player, gachaType)
    if not self.isInitialized then
        return {
            success = false,
            error = "系統未初始化"
        }
    end
    
    local result = GachaSystem:Roll(gachaType, player)
    
    if result and result.success then
        -- 觸發寵物獲得事件
        EventManager:FirePetEvent(EventManager.EventTypes.PET_OBTAINED, player, {
            petName = result.name,
            rarity = result.rarity,
            source = "gacha",
            gachaType = gachaType
        })
        
        -- 更新統計
        self:UpdateStats()
    end
    
    return result
end

-- 批量扭蛋
function MonsterPetSystem:PerformMultipleGacha(player, gachaType, count)
    if not self.isInitialized then
        return {
            success = false,
            error = "系統未初始化"
        }
    end
    
    local result = GachaSystem:RollMultiple(gachaType, count, player)
    
    if result and result.success then
        -- 為每個結果觸發事件
        for _, petResult in ipairs(result.results) do
            EventManager:FirePetEvent(EventManager.EventTypes.PET_OBTAINED, player, {
                petName = petResult.name,
                rarity = petResult.rarity,
                source = "gacha",
                gachaType = gachaType
            })
        end
        
        -- 更新統計
        self:UpdateStats()
    end
    
    return result
end

-- 獲取區域怪物列表
function MonsterPetSystem:GetZoneMonsters(zoneId)
    if not self.isInitialized then
        return {}
    end
    
    return MonsterManager:GetMonstersByZone(zoneId)
end

-- 獲取稀有度怪物列表
function MonsterPetSystem:GetRarityMonsters(rarity)
    if not self.isInitialized then
        return {}
    end
    
    return MonsterManager:GetMonstersByRarity(rarity)
end

-- 獲取怪物詳細信息
function MonsterPetSystem:GetMonsterInfo(monsterName)
    if not self.isInitialized then
        return nil
    end
    
    local data = MonsterManager:GetMonsterData(monsterName)
    if data then
        return {
            name = monsterName,
            rarity = data.rarity,
            zone = data.zone,
            stats = data.stats,
            complexity = data.complexity,
            partCount = data.partCount,
            hasSpecialEffects = data.hasSpecialEffects
        }
    end
    
    return nil
end

-- 應用視覺效果
function MonsterPetSystem:ApplyVisualEffects(model, rarity)
    if not self.isInitialized then
        return false
    end
    
    return VisualEffectsManager:ApplyRarityEffects(model, rarity)
end

-- 獲取扭蛋機率信息
function MonsterPetSystem:GetGachaRates(gachaType)
    if not self.isInitialized then
        return nil
    end
    
    return GachaSystem:GetRateDisplay(gachaType)
end

-- 獲取系統統計
function MonsterPetSystem:GetSystemStats()
    if not self.isInitialized then
        return {}
    end

    self:UpdateStats()

    -- 返回統計數據的副本
    return {
        totalMonsters = self.stats.totalMonsters,
        monstersPerZone = {
            FOREST = self.stats.monstersPerZone.FOREST or 0,
            ICE = self.stats.monstersPerZone.ICE or 0,
            LAVA = self.stats.monstersPerZone.LAVA or 0
        },
        monstersPerRarity = {
            COMMON = self.stats.monstersPerRarity.COMMON or 0,
            UNCOMMON = self.stats.monstersPerRarity.UNCOMMON or 0,
            RARE = self.stats.monstersPerRarity.RARE or 0,
            EPIC = self.stats.monstersPerRarity.EPIC or 0,
            LEGENDARY = self.stats.monstersPerRarity.LEGENDARY or 0
        },
        totalGachaRolls = self.stats.totalGachaRolls or 0
    }
end

-- 驗證系統完整性
function MonsterPetSystem:ValidateSystem()
    local issues = {}
    
    -- 檢查怪物總數
    if self.stats.totalMonsters ~= 97 then
        table.insert(issues, string.format("怪物總數不正確: %d (應為97)", self.stats.totalMonsters))
    end
    
    -- 檢查區域分配
    local totalInZones = 0
    for zone, count in pairs(self.stats.monstersPerZone) do
        totalInZones = totalInZones + count
    end
    
    if totalInZones ~= self.stats.totalMonsters then
        table.insert(issues, "區域分配總數與怪物總數不符")
    end
    
    -- 檢查稀有度分配
    local totalInRarities = 0
    for rarity, count in pairs(self.stats.monstersPerRarity) do
        totalInRarities = totalInRarities + count
    end
    
    if totalInRarities ~= self.stats.totalMonsters then
        table.insert(issues, "稀有度分配總數與怪物總數不符")
    end
    
    return #issues == 0, issues
end

-- 調試信息
function MonsterPetSystem:Debug()
    print("🐾 怪物-寵物統一系統調試信息:")
    print("  初始化狀態:", self.isInitialized)
    print("  總怪物數:", self.stats.totalMonsters)
    
    print("  區域分配:")
    for zone, count in pairs(self.stats.monstersPerZone) do
        print(string.format("    %s: %d 隻", zone, count))
    end
    
    print("  稀有度分配:")
    for rarity, count in pairs(self.stats.monstersPerRarity) do
        print(string.format("    %s: %d 隻", rarity, count))
    end
    
    print("  總扭蛋次數:", self.stats.totalGachaRolls)
    
    -- 驗證系統
    local isValid, issues = self:ValidateSystem()
    if isValid then
        print("✅ 系統驗證通過")
    else
        print("❌ 系統驗證失敗:")
        for _, issue in ipairs(issues) do
            print("    " .. issue)
        end
    end
end

return MonsterPetSystem
