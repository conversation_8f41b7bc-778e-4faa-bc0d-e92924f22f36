-- 寵物系統UI組件
-- 顯示扭蛋、寵物背包、圖鑑等功能

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Knit = require(ReplicatedStorage.Packages.Knit)
local Fusion = require(ReplicatedStorage.Packages.Fusion)

-- Fusion 0.2 組件
local New = Fusion.New
local State = Fusion.State
local Computed = Fusion.Computed
local Children = Fusion.Children

local PetSystemUI = {}

-- 本地狀態
local showPetSystem = State(false)
local currentTab = State("gacha") -- gacha, pets, pokedex
local monsterController = nil

function PetSystemUI.new()
    -- 獲取控制器
    monsterController = Knit.GetController("MonsterController")
    
    return New "Frame" {
        Name = "PetSystemFrame",
        Size = UDim2.new(0.8, 0, 0.8, 0),
        Position = UDim2.new(0.1, 0, 0.1, 0),
        BackgroundColor3 = Color3.fromRGB(40, 40, 40),
        BackgroundTransparency = 0.1,
        BorderSizePixel = 2,
        BorderColor3 = Color3.fromRGB(255, 255, 255),
        Visible = Computed(function()
            return showPetSystem:get()
        end),
        ZIndex = 100,
        
        [Children] = {
            New "UICorner" {
                CornerRadius = UDim.new(0, 12)
            },
            
            -- 標題欄
            New "Frame" {
                Name = "TitleBar",
                Size = UDim2.new(1, 0, 0, 60),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundColor3 = Color3.fromRGB(30, 30, 30),
                BorderSizePixel = 0,
                
                [Children] = {
                    New "UICorner" {
                        CornerRadius = UDim.new(0, 12)
                    },
                    
                    New "TextLabel" {
                        Name = "Title",
                        Size = UDim2.new(0.8, 0, 1, 0),
                        Position = UDim2.new(0, 15, 0, 0),
                        BackgroundTransparency = 1,
                        Text = "🐾 寵物系統",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextXAlignment = Enum.TextXAlignment.Left,
                        TextScaled = true,
                        Font = Enum.Font.GothamBold
                    },
                    
                    -- 關閉按鈕
                    New "TextButton" {
                        Name = "CloseButton",
                        Size = UDim2.new(0, 50, 0, 50),
                        Position = UDim2.new(1, -55, 0, 5),
                        BackgroundColor3 = Color3.fromRGB(255, 0, 0),
                        BorderSizePixel = 0,
                        Text = "✕",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        
                        [Fusion.OnEvent("Activated")] = function()
                            showPetSystem:set(false)
                        end,
                        
                        [Children] = {
                            New "UICorner" {
                                CornerRadius = UDim.new(0, 8)
                            }
                        }
                    }
                }
            },
            
            -- 標籤欄
            New "Frame" {
                Name = "TabBar",
                Size = UDim2.new(1, -20, 0, 50),
                Position = UDim2.new(0, 10, 0, 70),
                BackgroundTransparency = 1,
                
                [Children] = {
                    New "UIListLayout" {
                        SortOrder = Enum.SortOrder.LayoutOrder,
                        FillDirection = Enum.FillDirection.Horizontal,
                        Padding = UDim.new(0, 10),
                        HorizontalAlignment = Enum.HorizontalAlignment.Center
                    },
                    
                    -- 扭蛋標籤
                    New "TextButton" {
                        Name = "GachaTab",
                        Size = UDim2.new(0, 120, 1, 0),
                        BackgroundColor3 = Computed(function()
                            return currentTab:get() == "gacha" and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(70, 70, 70)
                        end),
                        BorderSizePixel = 0,
                        Text = "🎰 扭蛋",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        LayoutOrder = 1,
                        
                        [Fusion.OnEvent("Activated")] = function()
                            currentTab:set("gacha")
                        end,
                        
                        [Children] = {
                            New "UICorner" {
                                CornerRadius = UDim.new(0, 8)
                            }
                        }
                    },
                    
                    -- 寵物標籤
                    New "TextButton" {
                        Name = "PetsTab",
                        Size = UDim2.new(0, 120, 1, 0),
                        BackgroundColor3 = Computed(function()
                            return currentTab:get() == "pets" and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(70, 70, 70)
                        end),
                        BorderSizePixel = 0,
                        Text = "🐕 寵物",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        LayoutOrder = 2,
                        
                        [Fusion.OnEvent("Activated")] = function()
                            currentTab:set("pets")
                        end,
                        
                        [Children] = {
                            New "UICorner" {
                                CornerRadius = UDim.new(0, 8)
                            }
                        }
                    },
                    
                    -- 圖鑑標籤
                    New "TextButton" {
                        Name = "PokedexTab",
                        Size = UDim2.new(0, 120, 1, 0),
                        BackgroundColor3 = Computed(function()
                            return currentTab:get() == "pokedex" and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(70, 70, 70)
                        end),
                        BorderSizePixel = 0,
                        Text = "📖 圖鑑",
                        TextColor3 = Color3.fromRGB(255, 255, 255),
                        TextScaled = true,
                        Font = Enum.Font.GothamBold,
                        LayoutOrder = 3,
                        
                        [Fusion.OnEvent("Activated")] = function()
                            currentTab:set("pokedex")
                        end,
                        
                        [Children] = {
                            New "UICorner" {
                                CornerRadius = UDim.new(0, 8)
                            }
                        }
                    }
                }
            },
            
            -- 內容區域
            New "Frame" {
                Name = "ContentArea",
                Size = UDim2.new(1, -20, 1, -140),
                Position = UDim2.new(0, 10, 0, 130),
                BackgroundColor3 = Color3.fromRGB(50, 50, 50),
                BorderSizePixel = 1,
                BorderColor3 = Color3.fromRGB(100, 100, 100),
                
                [Children] = {
                    New "UICorner" {
                        CornerRadius = UDim.new(0, 8)
                    },
                    
                    -- 扭蛋內容
                    New "Frame" {
                        Name = "GachaContent",
                        Size = UDim2.new(1, 0, 1, 0),
                        BackgroundTransparency = 1,
                        Visible = Computed(function()
                            return currentTab:get() == "gacha"
                        end),
                        
                        [Children] = {
                            PetSystemUI.createGachaContent()
                        }
                    },
                    
                    -- 寵物內容
                    New "Frame" {
                        Name = "PetsContent",
                        Size = UDim2.new(1, 0, 1, 0),
                        BackgroundTransparency = 1,
                        Visible = Computed(function()
                            return currentTab:get() == "pets"
                        end),
                        
                        [Children] = {
                            PetSystemUI.createPetsContent()
                        }
                    },
                    
                    -- 圖鑑內容
                    New "Frame" {
                        Name = "PokedexContent",
                        Size = UDim2.new(1, 0, 1, 0),
                        BackgroundTransparency = 1,
                        Visible = Computed(function()
                            return currentTab:get() == "pokedex"
                        end),
                        
                        [Children] = {
                            PetSystemUI.createPokedexContent()
                        }
                    }
                }
            }
        }
    }
end

-- 創建扭蛋內容
function PetSystemUI.createGachaContent()
    return New "ScrollingFrame" {
        Size = UDim2.new(1, -20, 1, -20),
        Position = UDim2.new(0, 10, 0, 10),
        BackgroundTransparency = 1,
        ScrollBarThickness = 8,
        
        [Children] = {
            New "UIListLayout" {
                SortOrder = Enum.SortOrder.LayoutOrder,
                Padding = UDim.new(0, 15),
                HorizontalAlignment = Enum.HorizontalAlignment.Center
            },
            
            -- 金幣扭蛋
            PetSystemUI.createGachaButton("COIN", "💰 金幣扭蛋", "1000 金幣", Color3.fromRGB(255, 215, 0), 1),
            
            -- 寶石扭蛋
            PetSystemUI.createGachaButton("GEM", "💎 寶石扭蛋", "100 寶石", Color3.fromRGB(0, 255, 255), 2),
            
            -- R幣扭蛋
            PetSystemUI.createGachaButton("ROBUX", "🔥 R幣扭蛋", "50 R幣", Color3.fromRGB(0, 255, 0), 3)
        }
    }
end

-- 創建扭蛋按鈕
function PetSystemUI.createGachaButton(gachaType, title, cost, color, layoutOrder)
    return New "Frame" {
        Name = gachaType .. "Gacha",
        Size = UDim2.new(0.8, 0, 0, 120),
        BackgroundColor3 = color,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 2,
        BorderColor3 = Color3.fromRGB(255, 255, 255),
        LayoutOrder = layoutOrder,
        
        [Children] = {
            New "UICorner" {
                CornerRadius = UDim.new(0, 10)
            },
            
            New "TextLabel" {
                Name = "Title",
                Size = UDim2.new(0.6, 0, 0.5, 0),
                Position = UDim2.new(0, 15, 0, 10),
                BackgroundTransparency = 1,
                Text = title,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextXAlignment = Enum.TextXAlignment.Left,
                TextScaled = true,
                Font = Enum.Font.GothamBold
            },
            
            New "TextLabel" {
                Name = "Cost",
                Size = UDim2.new(0.6, 0, 0.3, 0),
                Position = UDim2.new(0, 15, 0.5, 0),
                BackgroundTransparency = 1,
                Text = cost,
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextXAlignment = Enum.TextXAlignment.Left,
                TextScaled = true,
                Font = Enum.Font.Gotham
            },
            
            -- 單抽按鈕
            New "TextButton" {
                Name = "SingleRoll",
                Size = UDim2.new(0, 80, 0, 40),
                Position = UDim2.new(1, -170, 0, 20),
                BackgroundColor3 = Color3.fromRGB(100, 200, 100),
                BorderSizePixel = 0,
                Text = "單抽",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [Fusion.OnEvent("Activated")] = function()
                    PetSystemUI.rollGacha(gachaType, 1)
                end,
                
                [Children] = {
                    New "UICorner" {
                        CornerRadius = UDim.new(0, 6)
                    }
                }
            },
            
            -- 十連按鈕
            New "TextButton" {
                Name = "TenRoll",
                Size = UDim2.new(0, 80, 0, 40),
                Position = UDim2.new(1, -85, 0, 20),
                BackgroundColor3 = Color3.fromRGB(200, 100, 100),
                BorderSizePixel = 0,
                Text = "十連",
                TextColor3 = Color3.fromRGB(255, 255, 255),
                TextScaled = true,
                Font = Enum.Font.GothamBold,
                
                [Fusion.OnEvent("Activated")] = function()
                    PetSystemUI.rollGacha(gachaType, 10)
                end,
                
                [Children] = {
                    New "UICorner" {
                        CornerRadius = UDim.new(0, 6)
                    }
                }
            }
        }
    }
end

-- 創建寵物內容
function PetSystemUI.createPetsContent()
    return New "TextLabel" {
        Size = UDim2.new(1, 0, 1, 0),
        BackgroundTransparency = 1,
        Text = "🐕 寵物管理\n\n這裡將顯示:\n• 攜帶的5隻寵物\n• 倉庫中的所有寵物\n• 當前跟隨的寵物\n• 寵物裝備/卸下功能",
        TextColor3 = Color3.fromRGB(255, 255, 255),
        TextSize = 18,
        Font = Enum.Font.Gotham,
        TextXAlignment = Enum.TextXAlignment.Center,
        TextYAlignment = Enum.TextYAlignment.Center
    }
end

-- 創建圖鑑內容
function PetSystemUI.createPokedexContent()
    return New "TextLabel" {
        Size = UDim2.new(1, 0, 1, 0),
        BackgroundTransparency = 1,
        Text = Computed(function()
            if monsterController then
                local stats = monsterController:getPokedexStatsState():get()
                return string.format("📖 寵物圖鑑\n\n已發現: %d/97\n已擁有: %d/97\n完成度: %.1f%%\n\n總遭遇: %d 次\n總擊敗: %d 次", 
                    stats.discovered, stats.owned, stats.completionRate * 100, 
                    stats.totalEncounters, stats.totalDefeats)
            else
                return "📖 寵物圖鑑\n\n載入中..."
            end
        end),
        TextColor3 = Color3.fromRGB(255, 255, 255),
        TextSize = 18,
        Font = Enum.Font.Gotham,
        TextXAlignment = Enum.TextXAlignment.Center,
        TextYAlignment = Enum.TextYAlignment.Center
    }
end

-- 扭蛋方法
function PetSystemUI.rollGacha(gachaType, count)
    if not monsterController then return end
    
    if count == 1 then
        monsterController:rollGacha(gachaType):andThen(function(result)
            print(string.format("🎁 獲得: %s (%s)", result.speciesId, result.rarity))
        end):catch(function(err)
            warn("扭蛋失敗:", err)
        end)
    else
        monsterController:rollMultipleGacha(gachaType, count):andThen(function(data)
            print(string.format("🎁 十連完成: %d 隻寵物", #data.results))
        end):catch(function(err)
            warn("十連扭蛋失敗:", err)
        end)
    end
end

-- 顯示寵物系統
function PetSystemUI.show()
    showPetSystem:set(true)
end

-- 隱藏寵物系統
function PetSystemUI.hide()
    showPetSystem:set(false)
end

-- 切換寵物系統顯示
function PetSystemUI.toggle()
    showPetSystem:set(not showPetSystem:get())
end

return PetSystemUI
