-- 服務端主啟動腳本
-- 初始化 Matter ECS 世界和 Knit 服務

print("🚀 PetSim99Lua 服務端啟動中...")

-- 引入依賴
local Matter = require(game.ReplicatedStorage.Packages.Matter)
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local ProfileService = require(game.ReplicatedStorage.Packages.ProfileService)

-- 引入配置
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Components = require(game.ReplicatedStorage.Components)

-- 創建全局 ECS 世界
local world = Matter.World.new()

-- 存儲世界引用供服務使用
_G.ECSWorld = world

print("✅ ECS 世界已創建")

-- 添加所有 Knit 服務
local servicesFolder = script.Services
for _, serviceModule in pairs(servicesFolder:GetChildren()) do
    if serviceModule:IsA("ModuleScript") then
        require(serviceModule)
        print("📦 已載入服務:", serviceModule.Name)
    end
end

-- 添加所有 ECS 系統
local systemsFolder = script.Systems
local systems = {}

for _, systemModule in pairs(systemsFolder:GetChildren()) do
    if systemModule:IsA("ModuleScript") then
        local system = require(systemModule)
        table.insert(systems, system)
        print("⚙️ 已載入系統:", systemModule.Name)
    end
end

-- 啟動 Knit
Knit.Start():andThen(function()
    print("🌐 Knit 服務端已啟動")
    
    -- 啟動 ECS 系統循環
    local RunService = game:GetService("RunService")
    
    RunService.Heartbeat:Connect(function(deltaTime)
        -- 運行所有 ECS 系統
        for _, system in ipairs(systems) do
            if system.step then
                local success, err = pcall(system.step, system, world, {
                    deltaTime = deltaTime,
                    currentTime = tick()
                })
                
                if not success then
                    warn("系統錯誤 [" .. tostring(system) .. "]:", err)
                end
            end
        end
        
        -- 更新 Matter 世界
        Matter.debugger(world)
    end)
    
    print("⚙️ ECS 系統循環已啟動")
    
end):catch(function(err)
    error("❌ Knit 啟動失敗: " .. tostring(err))
end)

-- 玩家連接處理
local Players = game:GetService("Players")

Players.PlayerAdded:Connect(function(player)
    print("👤 玩家加入:", player.Name)
    
    -- 創建玩家實體
    local playerEntity = world:spawn(
        Components.PlayerComponent({
            userId = player.UserId,
            displayName = player.DisplayName,
            joinTime = tick(),
            isOnline = true
        }),
        Components.HeroComponent({
            level = 1,
            experience = 0,
            maxHealth = GameConfig.HERO.BASE_HEALTH,
            currentHealth = GameConfig.HERO.BASE_HEALTH,
            attack = GameConfig.HERO.BASE_ATTACK,
            defense = GameConfig.HERO.BASE_DEFENSE,
            critRate = GameConfig.HERO.BASE_CRIT_RATE,
            critDamage = GameConfig.HERO.BASE_CRIT_DAMAGE
        }),
        Components.CurrencyComponent({
            coins = GameConfig.ECONOMY.STARTING_COINS,
            gems = GameConfig.ECONOMY.STARTING_GEMS,
            robux = GameConfig.ECONOMY.STARTING_ROBUX,
            experience = 0
        }),
        Components.ZoneComponent({
            currentZone = "castle",
            lastZoneChange = tick(),
            allowedZones = {"castle"}
        }),
        Components.VIPComponent({
            level = 0,
            totalSpent = 0,
            premiumExpiry = 0,
            premiumActive = false,
            lastRewardClaim = 0
        }),
        Components.PokedexComponent({
            encounteredSpecies = {},
            ownedSpecies = {},
            completionRate = 0
        })
    )
    
    -- 存儲玩家實體映射
    player:SetAttribute("EntityId", playerEntity)
    
    print("✅ 玩家實體已創建:", playerEntity)
end)

Players.PlayerRemoving:Connect(function(player)
    print("👋 玩家離開:", player.Name)
    
    -- 清理玩家實體
    local entityId = player:GetAttribute("EntityId")
    if entityId and world:contains(entityId) then
        world:despawn(entityId)
        print("🗑️ 玩家實體已清理:", entityId)
    end
end)

-- 錯誤處理
local function handleError(err)
    warn("🚨 服務端錯誤:", err)
    warn(debug.traceback())
end

-- 全局錯誤捕獲
local success, err = pcall(function()
    -- 主要初始化邏輯已在上面完成
end)

if not success then
    handleError(err)
end

print("🎮 PetSim99Lua 服務端啟動完成！")
print("📊 遊戲版本:", GameConfig.VERSION)
print("👥 最大玩家數:", GameConfig.MAX_PLAYERS)
