-- 英雄管理系統
local HeroManager = {}

-- 依賴
local HeroConfig = require(game.ReplicatedStorage.Configuration.HeroConfig)
local EventManager = require(game.ReplicatedStorage.Shared.EventManager)

-- 英雄數據存儲
HeroManager.heroes = {} -- playerId -> heroData
HeroManager.activeEffects = {} -- playerId -> {effectId -> effectData}

-- 初始化英雄管理系統
function HeroManager:Initialize()
    print("🦸 初始化英雄管理系統...")
    
    -- 重置數據
    self.heroes = {}
    self.activeEffects = {}
    
    -- 設置事件處理器
    self:SetupEventHandlers()
    
    print("✅ 英雄管理系統初始化完成")
end

-- 設置事件處理器
function HeroManager:SetupEventHandlers()
    -- 監聽玩家加入事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_JOINED, function(data)
        self:OnPlayerJoined(data.player)
    end)
    
    -- 監聽玩家離開事件
    EventManager:Connect(EventManager.EventTypes.PLAYER_LEFT, function(data)
        self:OnPlayerLeft(data.player)
    end)
    
    -- 監聽戰鬥事件
    EventManager:Connect(EventManager.EventTypes.COMBAT_STARTED, function(data)
        self:OnCombatStarted(data)
    end)
    
    EventManager:Connect(EventManager.EventTypes.COMBAT_ENDED, function(data)
        self:OnCombatEnded(data)
    end)
end

-- 玩家加入事件處理
function HeroManager:OnPlayerJoined(player)
    -- 載入或創建英雄數據
    local heroData = self:LoadHeroData(player)
    if not heroData then
        heroData = self:CreateDefaultHero(player)
    end
    
    self.heroes[player.UserId] = heroData
    self.activeEffects[player.UserId] = {}
    
    print(string.format("🦸 %s 的英雄已載入 (等級 %d %s)", 
          player.Name, heroData.level, heroData.class))
end

-- 玩家離開事件處理
function HeroManager:OnPlayerLeft(player)
    -- 保存英雄數據
    self:SaveHeroData(player)
    
    -- 清理數據
    self.heroes[player.UserId] = nil
    self.activeEffects[player.UserId] = nil
    
    print(string.format("👋 %s 的英雄數據已保存", player.Name))
end

-- 戰鬥開始事件處理
function HeroManager:OnCombatStarted(data)
    local heroData = self.heroes[data.player.UserId]
    if heroData then
        heroData.combatState = HeroConfig.STATUS_SYSTEM.combatStates.FIGHTING
        heroData.lastCombatTime = tick()
    end
end

-- 戰鬥結束事件處理
function HeroManager:OnCombatEnded(data)
    local heroData = self.heroes[data.player.UserId]
    if heroData then
        heroData.combatState = HeroConfig.STATUS_SYSTEM.combatStates.IDLE
        heroData.lastCombatEndTime = tick()
    end
end

-- 創建默認英雄
function HeroManager:CreateDefaultHero(player, heroClass)
    heroClass = heroClass or "WARRIOR" -- 默認戰士
    
    local classConfig = HeroConfig.CLASSES[heroClass]
    if not classConfig then
        warn("無效的英雄職業:", heroClass)
        classConfig = HeroConfig.CLASSES.WARRIOR
        heroClass = "WARRIOR"
    end
    
    local heroData = {
        -- 基本信息
        playerId = player.UserId,
        playerName = player.Name,
        class = heroClass,
        level = 1,
        experience = 0,
        
        -- 基礎屬性
        baseStats = self:DeepCopy(classConfig.baseStats),
        
        -- 當前狀態
        currentHealth = classConfig.baseStats.health,
        currentMana = classConfig.baseStats.mana,
        
        -- 狀態
        baseState = HeroConfig.STATUS_SYSTEM.baseStates.ALIVE,
        combatState = HeroConfig.STATUS_SYSTEM.combatStates.IDLE,
        
        -- 時間戳
        createdTime = tick(),
        lastLoginTime = tick(),
        lastCombatTime = 0,
        lastCombatEndTime = 0,
        
        -- 技能
        skills = self:DeepCopy(classConfig.skills),
        skillLevels = {},
        
        -- 裝備
        equipment = {},
        
        -- 統計
        stats = {
            totalDamageDealt = 0,
            totalDamageTaken = 0,
            totalHealingDone = 0,
            totalHealingReceived = 0,
            monstersKilled = 0,
            deaths = 0,
            playtime = 0
        }
    }
    
    -- 初始化技能等級
    for _, skill in ipairs(heroData.skills) do
        heroData.skillLevels[skill] = 1
    end
    
    -- 觸發英雄創建事件
    EventManager:Fire(EventManager.EventTypes.HERO_CREATED, {
        player = player,
        heroData = heroData
    })
    
    print(string.format("🦸 為 %s 創建了 %s 英雄", player.Name, classConfig.name))
    
    return heroData
end

-- 獲取英雄數據
function HeroManager:GetHeroData(player)
    return self.heroes[player.UserId]
end

-- 計算當前屬性 (包含裝備和效果加成)
function HeroManager:GetCurrentStats(player)
    local heroData = self.heroes[player.UserId]
    if not heroData then
        return nil
    end
    
    local classConfig = HeroConfig.CLASSES[heroData.class]
    local stats = self:DeepCopy(heroData.baseStats)
    
    -- 等級加成
    local level = heroData.level
    for statName, growth in pairs(classConfig.statGrowth) do
        if stats[statName] then
            stats[statName] = stats[statName] + (growth * (level - 1))
        end
    end
    
    -- 裝備加成 (TODO: 實現裝備系統後添加)
    
    -- 效果加成
    local effects = self.activeEffects[player.UserId] or {}
    for effectId, effectData in pairs(effects) do
        self:ApplyEffectToStats(stats, effectData)
    end
    
    -- 計算衍生屬性
    local derivedStats = {
        maxHealth = HeroConfig.STAT_FORMULAS.maxHealth(
            stats.health, stats.vitality, level, classConfig.statGrowth.health
        ),
        maxMana = HeroConfig.STAT_FORMULAS.maxMana(
            stats.mana, stats.intelligence, level, classConfig.statGrowth.mana
        ),
        physicalAttack = HeroConfig.STAT_FORMULAS.physicalAttack(
            stats.strength, stats.agility
        ),
        magicalAttack = HeroConfig.STAT_FORMULAS.magicalAttack(
            stats.intelligence
        ),
        physicalDefense = HeroConfig.STAT_FORMULAS.physicalDefense(
            stats.defense, stats.strength
        ),
        magicalDefense = HeroConfig.STAT_FORMULAS.magicalDefense(
            stats.magicResist, stats.intelligence
        ),
        moveSpeed = HeroConfig.STAT_FORMULAS.moveSpeed(
            16, stats.agility -- 基礎移動速度16
        ),
        attackSpeed = HeroConfig.STAT_FORMULAS.attackSpeed(
            1.0, stats.agility -- 基礎攻擊速度1.0
        )
    }
    
    -- 合併基礎屬性和衍生屬性
    for key, value in pairs(derivedStats) do
        stats[key] = value
    end
    
    return stats
end

-- 獲得經驗值
function HeroManager:GainExperience(player, expAmount)
    local heroData = self.heroes[player.UserId]
    if not heroData then
        return false
    end
    
    local oldLevel = heroData.level
    heroData.experience = heroData.experience + expAmount
    
    -- 檢查升級
    local newLevel = self:CalculateLevel(heroData.experience)
    if newLevel > oldLevel then
        self:LevelUp(player, newLevel)
    end
    
    -- 觸發經驗獲得事件
    EventManager:Fire(EventManager.EventTypes.HERO_GAINED_EXP, {
        player = player,
        expGained = expAmount,
        totalExp = heroData.experience,
        level = heroData.level
    })
    
    return true
end

-- 升級
function HeroManager:LevelUp(player, newLevel)
    local heroData = self.heroes[player.UserId]
    if not heroData then
        return false
    end
    
    local oldLevel = heroData.level
    heroData.level = newLevel
    
    -- 恢復血量和魔力
    local currentStats = self:GetCurrentStats(player)
    heroData.currentHealth = currentStats.maxHealth
    heroData.currentMana = currentStats.maxMana
    
    -- 觸發升級事件
    EventManager:Fire(EventManager.EventTypes.HERO_LEVEL_UP, {
        player = player,
        oldLevel = oldLevel,
        newLevel = newLevel,
        heroData = heroData
    })
    
    print(string.format("🎉 %s 升級到 %d 級！", player.Name, newLevel))
    
    return true
end

-- 計算等級
function HeroManager:CalculateLevel(totalExp)
    for level = 1, HeroConfig.LEVEL_SYSTEM.maxLevel do
        local requiredExp = HeroConfig.LEVEL_SYSTEM.getTotalExpForLevel(level + 1)
        if totalExp < requiredExp then
            return level
        end
    end
    return HeroConfig.LEVEL_SYSTEM.maxLevel
end

-- 造成傷害
function HeroManager:DealDamage(attacker, target, damage, damageType)
    local attackerData = self.heroes[attacker.UserId]
    local targetData = self.heroes[target.UserId]
    
    if not attackerData or not targetData then
        return false
    end
    
    -- 檢查目標是否存活
    if targetData.baseState ~= HeroConfig.STATUS_SYSTEM.baseStates.ALIVE then
        return false
    end
    
    damageType = damageType or HeroConfig.DAMAGE_SYSTEM.damageTypes.PHYSICAL
    
    -- 計算實際傷害
    local actualDamage = self:CalculateDamage(attacker, target, damage, damageType)
    
    -- 應用傷害
    targetData.currentHealth = math.max(0, targetData.currentHealth - actualDamage)
    
    -- 更新統計
    attackerData.stats.totalDamageDealt = attackerData.stats.totalDamageDealt + actualDamage
    targetData.stats.totalDamageTaken = targetData.stats.totalDamageTaken + actualDamage
    
    -- 檢查死亡
    if targetData.currentHealth <= 0 then
        self:HandleDeath(target)
    end
    
    -- 觸發傷害事件
    EventManager:Fire(EventManager.EventTypes.HERO_DAMAGED, {
        attacker = attacker,
        target = target,
        damage = actualDamage,
        damageType = damageType,
        targetHealth = targetData.currentHealth
    })
    
    return true
end

-- 計算傷害
function HeroManager:CalculateDamage(attacker, target, baseDamage, damageType)
    local attackerStats = self:GetCurrentStats(attacker)
    local targetStats = self:GetCurrentStats(target)
    
    if not attackerStats or not targetStats then
        return baseDamage
    end
    
    local damage = baseDamage
    
    -- 根據傷害類型計算
    if damageType == HeroConfig.DAMAGE_SYSTEM.damageTypes.PHYSICAL then
        damage = HeroConfig.DAMAGE_SYSTEM.damageFormulas.physical(
            damage + attackerStats.physicalAttack,
            targetStats.physicalDefense
        )
    elseif damageType == HeroConfig.DAMAGE_SYSTEM.damageTypes.MAGICAL then
        damage = HeroConfig.DAMAGE_SYSTEM.damageFormulas.magical(
            damage + attackerStats.magicalAttack,
            targetStats.magicalDefense
        )
    else -- TRUE damage
        damage = HeroConfig.DAMAGE_SYSTEM.damageFormulas.true(damage)
    end
    
    -- 檢查暴擊
    local critRate = HeroConfig.DAMAGE_SYSTEM.criticalHit.baseCritRate + 
                    (attackerStats.agility * HeroConfig.DAMAGE_SYSTEM.criticalHit.agilityCritBonus)
    
    if math.random() < critRate then
        local critDamage = HeroConfig.DAMAGE_SYSTEM.criticalHit.baseCritDamage +
                          (attackerStats.agility * HeroConfig.DAMAGE_SYSTEM.criticalHit.critDamageBonus)
        damage = math.floor(damage * critDamage)
        
        -- 觸發暴擊事件
        EventManager:Fire(EventManager.EventTypes.CRITICAL_HIT, {
            attacker = attacker,
            target = target,
            damage = damage
        })
    end
    
    return math.max(1, math.floor(damage))
end

-- 治療
function HeroManager:Heal(healer, target, healAmount, healType)
    local healerData = self.heroes[healer.UserId]
    local targetData = self.heroes[target.UserId]
    
    if not healerData or not targetData then
        return false
    end
    
    -- 檢查目標是否存活
    if targetData.baseState ~= HeroConfig.STATUS_SYSTEM.baseStates.ALIVE then
        return false
    end
    
    healType = healType or HeroConfig.HEALING_SYSTEM.healingTypes.INSTANT
    
    -- 計算實際治療量
    local actualHeal = self:CalculateHealing(healer, target, healAmount, healType)
    
    -- 應用治療
    local targetStats = self:GetCurrentStats(target)
    targetData.currentHealth = math.min(targetStats.maxHealth, targetData.currentHealth + actualHeal)
    
    -- 更新統計
    healerData.stats.totalHealingDone = healerData.stats.totalHealingDone + actualHeal
    targetData.stats.totalHealingReceived = targetData.stats.totalHealingReceived + actualHeal
    
    -- 觸發治療事件
    EventManager:Fire(EventManager.EventTypes.HERO_HEALED, {
        healer = healer,
        target = target,
        healAmount = actualHeal,
        healType = healType,
        targetHealth = targetData.currentHealth
    })
    
    return true
end

-- 計算治療量
function HeroManager:CalculateHealing(healer, target, baseHeal, healType)
    local healerStats = self:GetCurrentStats(healer)
    
    if not healerStats then
        return baseHeal
    end
    
    local heal = baseHeal
    
    -- 智力加成治療效果
    heal = heal + (healerStats.intelligence * 0.5)
    
    return math.max(1, math.floor(heal))
end

-- 處理死亡
function HeroManager:HandleDeath(player)
    local heroData = self.heroes[player.UserId]
    if not heroData then
        return false
    end
    
    -- 更新狀態
    heroData.baseState = HeroConfig.STATUS_SYSTEM.baseStates.DEAD
    heroData.combatState = HeroConfig.STATUS_SYSTEM.combatStates.IDLE
    heroData.currentHealth = 0
    
    -- 更新統計
    heroData.stats.deaths = heroData.stats.deaths + 1
    
    -- 應用死亡懲罰
    local penalty = HeroConfig.STATUS_SYSTEM.resurrection.deathPenalty
    local expLoss = math.floor(heroData.experience * penalty.expLoss)
    heroData.experience = math.max(0, heroData.experience - expLoss)
    
    -- 觸發死亡事件
    EventManager:Fire(EventManager.EventTypes.HERO_DIED, {
        player = player,
        heroData = heroData,
        expLoss = expLoss
    })
    
    print(string.format("💀 %s 死亡了，失去 %d 經驗值", player.Name, expLoss))
    
    -- 設置自動復活
    task.spawn(function()
        task.wait(HeroConfig.STATUS_SYSTEM.resurrection.methods.AUTO_REVIVE.delay)
        self:Resurrect(player, "AUTO_REVIVE")
    end)
    
    return true
end

-- 復活
function HeroManager:Resurrect(player, method)
    local heroData = self.heroes[player.UserId]
    if not heroData then
        return false
    end
    
    if heroData.baseState ~= HeroConfig.STATUS_SYSTEM.baseStates.DEAD then
        return false
    end
    
    method = method or "AUTO_REVIVE"
    local reviveConfig = HeroConfig.STATUS_SYSTEM.resurrection.methods[method]
    
    if not reviveConfig then
        return false
    end
    
    -- 復活
    heroData.baseState = HeroConfig.STATUS_SYSTEM.baseStates.ALIVE
    
    -- 恢復血量
    local currentStats = self:GetCurrentStats(player)
    heroData.currentHealth = math.floor(currentStats.maxHealth * reviveConfig.reviveHealthPercent)
    
    -- 觸發復活事件
    EventManager:Fire(EventManager.EventTypes.HERO_RESURRECTED, {
        player = player,
        method = method,
        heroData = heroData
    })
    
    print(string.format("🌟 %s 復活了 (方式: %s)", player.Name, reviveConfig.name))
    
    return true
end

-- 載入英雄數據 (模擬)
function HeroManager:LoadHeroData(player)
    -- 這裡應該從數據庫載入
    -- 現在返回 nil 表示新玩家
    return nil
end

-- 保存英雄數據 (模擬)
function HeroManager:SaveHeroData(player)
    local heroData = self.heroes[player.UserId]
    if heroData then
        -- 這裡應該保存到數據庫
        print(string.format("💾 保存 %s 的英雄數據", player.Name))
    end
end

-- 應用效果到屬性
function HeroManager:ApplyEffectToStats(stats, effectData)
    if effectData.effects then
        for statName, modifier in pairs(effectData.effects) do
            if stats[statName] and type(modifier) == "number" then
                if modifier > 1 then
                    -- 乘法修正 (如 1.1 = +10%)
                    stats[statName] = stats[statName] * modifier
                else
                    -- 加法修正
                    stats[statName] = stats[statName] + modifier
                end
            end
        end
    end
end

-- 深拷貝函數
function HeroManager:DeepCopy(original)
    if type(original) ~= "table" then
        return original
    end
    
    local copy = {}
    for key, value in pairs(original) do
        copy[key] = self:DeepCopy(value)
    end
    return copy
end

-- 調試信息
function HeroManager:Debug()
    print("🦸 英雄管理系統調試信息:")
    print("  活躍英雄數:", self:GetActiveHeroCount())
    
    for playerId, heroData in pairs(self.heroes) do
        print(string.format("  %s: 等級 %d %s (血量 %d/%d)", 
              heroData.playerName, heroData.level, heroData.class,
              heroData.currentHealth, self:GetCurrentStats({UserId = playerId}).maxHealth or 0))
    end
end

-- 獲取活躍英雄數量
function HeroManager:GetActiveHeroCount()
    local count = 0
    for _ in pairs(self.heroes) do
        count = count + 1
    end
    return count
end

return HeroManager
