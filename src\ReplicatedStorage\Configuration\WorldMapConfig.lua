-- 正確的世界地圖配置系統
local WorldMapConfig = {}

-- 地圖類型
WorldMapConfig.MAP_TYPES = {
    SAFE_ZONE = "safe_zone",      -- 安全區
    WILD_AREA = "wild_area",      -- 野外練功區
    DUNGEON = "dungeon"           -- 副本
}

-- 主世界地圖配置
WorldMapConfig.MAIN_WORLD = {
    name = "主世界",
    description = "冒險者的主要活動區域",
    
    -- 城堡安全區 (中心)
    safeZone = {
        name = "魔法城堡",
        description = "完全安全的區域，所有服務和功能的中心",
        type = WorldMapConfig.MAP_TYPES.SAFE_ZONE,
        center = Vector3.new(0, 10, 0),
        radius = 100, -- 安全區半徑
        
        -- 功能區域
        areas = {
            SPAWN = {
                name = "出生點",
                position = Vector3.new(0, 10, 0),
                description = "新玩家的出生地點"
            },
            SHOP = {
                name = "商店區",
                position = Vector3.new(30, 10, 0),
                description = "購買裝備和道具"
            },
            GACHA = {
                name = "扭蛋區",
                position = Vector3.new(-30, 10, 0),
                description = "寵物扭蛋機"
            },
            TEAM_FORMATION = {
                name = "組隊區",
                position = Vector3.new(0, 10, 40),
                description = "組建隊伍準備冒險"
            },
            DUNGEON_PORTALS = {
                name = "副本傳送門",
                position = Vector3.new(0, 10, -40),
                description = "進入各個副本的傳送門"
            },
            PET_MANAGEMENT = {
                name = "寵物管理",
                position = Vector3.new(40, 10, 0),
                description = "管理和培養寵物"
            },
            BANK = {
                name = "銀行",
                position = Vector3.new(-40, 10, 0),
                description = "存儲物品和金錢"
            },
            AUCTION_HOUSE = {
                name = "拍賣行",
                position = Vector3.new(0, 10, 30),
                description = "玩家間交易"
            }
        }
    },
    
    -- 野外練功區 (3個同心圓)
    wildAreas = {
        -- 內圈：森林區域
        FOREST_WILD = {
            name = "翡翠森林野外",
            description = "適合新手冒險者的森林區域",
            type = WorldMapConfig.MAP_TYPES.WILD_AREA,
            difficulty = "初級",
            recommendedLevel = {min = 1, max = 20},
            
            -- 區域範圍 (環形)
            area = {
                center = Vector3.new(0, 10, 0),
                innerRadius = 100, -- 從安全區邊界開始
                outerRadius = 300  -- 到中圈邊界
            },
            
            -- 怪物配置
            monsters = {
                spawnRate = 0.7, -- 70%生成率
                maxCount = 50,   -- 最大怪物數量
                respawnTime = 30, -- 重生時間(秒)
                types = {
                    -- 普通怪物 (80%)
                    {
                        rarity = "COMMON",
                        weight = 80,
                        models = {"Cat", "Fox", "Dog", "Bear", "Bunny", "Mouse", "Bee", "Cow", "Pig", "Duck"},
                        level = {min = 1, max = 15},
                        experience = {min = 10, max = 25}
                    },
                    -- 稀有怪物 (15%)
                    {
                        rarity = "UNCOMMON", 
                        weight = 15,
                        models = {"Golden Cat", "LuaBear", "Skeleton deer", "Bat", "Shark"},
                        level = {min = 10, max = 20},
                        experience = {min = 30, max = 50}
                    },
                    -- 精英怪物 (5%)
                    {
                        rarity = "RARE",
                        weight = 5,
                        models = {"Cyborg", "lil' creature", "Octopuslord"},
                        level = {min = 15, max = 25},
                        experience = {min = 60, max = 100}
                    }
                }
            },
            
            -- 環境設置
            environment = {
                lighting = "Natural",
                fogColor = Color3.fromRGB(100, 150, 100),
                fogStart = 100,
                fogEnd = 500,
                ambientSound = "ForestAmbient"
            }
        },
        
        -- 中圈：冰雪區域
        ICE_WILD = {
            name = "極地冰原野外",
            description = "寒冷危險的冰雪區域",
            type = WorldMapConfig.MAP_TYPES.WILD_AREA,
            difficulty = "中級",
            recommendedLevel = {min = 20, max = 50},
            
            -- 區域範圍 (環形)
            area = {
                center = Vector3.new(0, 10, 0),
                innerRadius = 300, -- 從森林區邊界開始
                outerRadius = 600  -- 到外圈邊界
            },
            
            -- 怪物配置
            monsters = {
                spawnRate = 0.6, -- 60%生成率
                maxCount = 40,   -- 最大怪物數量
                respawnTime = 45, -- 重生時間(秒)
                types = {
                    -- 普通怪物 (60%)
                    {
                        rarity = "COMMON",
                        weight = 60,
                        models = {"Snowman", "Nice cloud", "Ice golem"},
                        level = {min = 20, max = 40},
                        experience = {min = 40, max = 80}
                    },
                    -- 稀有怪物 (25%)
                    {
                        rarity = "UNCOMMON",
                        weight = 25,
                        models = {"Robot Overlord", "Bacterial Virus", "Angelfly"},
                        level = {min = 30, max = 50},
                        experience = {min = 100, max = 150}
                    },
                    -- 精英怪物 (15%)
                    {
                        rarity = "RARE",
                        weight = 15,
                        models = {"Gigantic Lord", "Kraken", "Aqua Dragon"},
                        level = {min = 40, max = 60},
                        experience = {min = 200, max = 300}
                    }
                }
            },
            
            -- 環境設置
            environment = {
                lighting = "Arctic",
                fogColor = Color3.fromRGB(200, 220, 255),
                fogStart = 50,
                fogEnd = 400,
                ambientSound = "IceAmbient"
            }
        },
        
        -- 外圈：熔岩區域
        LAVA_WILD = {
            name = "地獄火山野外",
            description = "極度危險的熔岩區域",
            type = WorldMapConfig.MAP_TYPES.WILD_AREA,
            difficulty = "高級",
            recommendedLevel = {min = 50, max = 80},
            
            -- 區域範圍 (環形)
            area = {
                center = Vector3.new(0, 10, 0),
                innerRadius = 600, -- 從冰雪區邊界開始
                outerRadius = 1000 -- 世界邊界
            },
            
            -- 怪物配置
            monsters = {
                spawnRate = 0.5, -- 50%生成率
                maxCount = 30,   -- 最大怪物數量
                respawnTime = 60, -- 重生時間(秒)
                types = {
                    -- 普通怪物 (40%)
                    {
                        rarity = "UNCOMMON",
                        weight = 40,
                        models = {"Lava Beast", "Demon bee", "Hellish Golem"},
                        level = {min = 50, max = 70},
                        experience = {min = 150, max = 250}
                    },
                    -- 稀有怪物 (35%)
                    {
                        rarity = "RARE",
                        weight = 35,
                        models = {"Demon", "lil' demon", "Mythical Demon"},
                        level = {min = 60, max = 80},
                        experience = {min = 300, max = 500}
                    },
                    -- 精英怪物 (20%)
                    {
                        rarity = "EPIC",
                        weight = 20,
                        models = {"Lava Lord", "Light Demon", "Demonic Destroyer"},
                        level = {min = 70, max = 90},
                        experience = {min = 600, max = 1000}
                    },
                    -- 傳說怪物 (5%)
                    {
                        rarity = "LEGENDARY",
                        weight = 5,
                        models = {"Demonic Dominus", "Demonic Spider"},
                        level = {min = 80, max = 100},
                        experience = {min = 1200, max = 2000}
                    }
                }
            },
            
            -- 環境設置
            environment = {
                lighting = "Volcanic",
                fogColor = Color3.fromRGB(255, 100, 50),
                fogStart = 30,
                fogEnd = 300,
                ambientSound = "LavaAmbient"
            }
        }
    }
}

-- 副本地圖配置 (獨立地圖)
WorldMapConfig.DUNGEONS = {
    FOREST_DUNGEON = {
        name = "翡翠森林副本",
        description = "森林守護者的領域，需要團隊合作",
        type = WorldMapConfig.MAP_TYPES.DUNGEON,
        difficulty = "初級",
        requiredLevel = 15,
        teamSize = {min = 3, max = 5},
        timeLimit = 1800, -- 30分鐘
        
        -- 地圖位置
        mapLocation = "Workspace.MAP.Forest",
        
        -- BOSS配置
        bosses = {
            {
                name = "狼王",
                model = "Wolf",
                type = "mini_boss",
                level = 20,
                health = 3000,
                position = Vector3.new(50, 5, 50)
            },
            {
                name = "熊王",
                model = "Bear",
                type = "mini_boss",
                level = 25,
                health = 5000,
                position = Vector3.new(-50, 5, -50)
            },
            {
                name = "森林守護者",
                model = "LuaBear",
                type = "main_boss",
                level = 30,
                health = 12000,
                position = Vector3.new(0, 10, 0)
            }
        },
        
        -- 獎勵配置
        rewards = {
            experience = {min = 500, max = 1000},
            coins = {min = 200, max = 500},
            gems = {min = 5, max = 15},
            petDropChance = {
                COMMON = 0.3,
                UNCOMMON = 0.4,
                RARE = 0.25,
                EPIC = 0.05
            }
        }
    },
    
    ICE_DUNGEON = {
        name = "極地冰窟副本",
        description = "冰霜巨龍的巢穴，極度危險",
        type = WorldMapConfig.MAP_TYPES.DUNGEON,
        difficulty = "中級",
        requiredLevel = 35,
        teamSize = {min = 4, max = 6},
        timeLimit = 2400, -- 40分鐘
        
        -- 地圖位置
        mapLocation = "Workspace.MAP.Ice",
        
        -- BOSS配置
        bosses = {
            {
                name = "冰魔法師",
                model = "Nice cloud",
                type = "mini_boss",
                level = 40,
                health = 8000,
                position = Vector3.new(60, 10, 30)
            },
            {
                name = "雪怪王",
                model = "Snowman",
                type = "mini_boss",
                level = 45,
                health = 12000,
                position = Vector3.new(-60, 10, -30)
            },
            {
                name = "冰霜巨龍",
                model = "Ice golem",
                type = "main_boss",
                level = 50,
                health = 25000,
                position = Vector3.new(0, 20, 0)
            }
        },
        
        -- 獎勵配置
        rewards = {
            experience = {min = 1000, max = 2000},
            coins = {min = 500, max = 1000},
            gems = {min = 15, max = 30},
            petDropChance = {
                UNCOMMON = 0.2,
                RARE = 0.4,
                EPIC = 0.3,
                LEGENDARY = 0.1
            }
        }
    },
    
    LAVA_DUNGEON = {
        name = "地獄熔爐副本",
        description = "惡魔王的王座，終極挑戰",
        type = WorldMapConfig.MAP_TYPES.DUNGEON,
        difficulty = "高級",
        requiredLevel = 60,
        teamSize = {min = 5, max = 8},
        timeLimit = 3600, -- 60分鐘
        
        -- 地圖位置
        mapLocation = "Workspace.MAP.Lava",
        
        -- BOSS配置
        bosses = {
            {
                name = "火焰巨人",
                model = "Lava Beast",
                type = "mini_boss",
                level = 65,
                health = 15000,
                position = Vector3.new(80, 15, 40)
            },
            {
                name = "岩漿元素",
                model = "Hellish Golem",
                type = "mini_boss",
                level = 70,
                health = 20000,
                position = Vector3.new(-80, 15, -40)
            },
            {
                name = "地獄犬王",
                model = "Demon bee",
                type = "mini_boss",
                level = 75,
                health = 25000,
                position = Vector3.new(0, 15, 80)
            },
            {
                name = "熔岩惡魔王",
                model = "Demonic Destroyer",
                type = "main_boss",
                level = 80,
                health = 50000,
                position = Vector3.new(0, 25, 0)
            }
        },
        
        -- 獎勵配置
        rewards = {
            experience = {min = 2000, max = 4000},
            coins = {min = 1000, max = 2000},
            gems = {min = 30, max = 60},
            petDropChance = {
                RARE = 0.2,
                EPIC = 0.4,
                LEGENDARY = 0.35,
                MYTHIC = 0.05
            }
        }
    }
}

-- 區域檢測工具
WorldMapConfig.AREA_UTILS = {
    -- 檢查位置是否在安全區
    isInSafeZone = function(position)
        local safeZone = WorldMapConfig.MAIN_WORLD.safeZone
        local distance = (position - safeZone.center).Magnitude
        return distance <= safeZone.radius
    end,
    
    -- 獲取位置所在的野外區域
    getWildArea = function(position)
        local center = WorldMapConfig.MAIN_WORLD.safeZone.center
        local distance = (position - center).Magnitude
        
        for areaName, area in pairs(WorldMapConfig.MAIN_WORLD.wildAreas) do
            if distance >= area.area.innerRadius and distance <= area.area.outerRadius then
                return areaName, area
            end
        end
        
        return nil, nil
    end,
    
    -- 檢查玩家等級是否適合區域
    isLevelSuitableForArea = function(playerLevel, areaConfig)
        return playerLevel >= areaConfig.recommendedLevel.min and 
               playerLevel <= areaConfig.recommendedLevel.max
    end
}

return WorldMapConfig
