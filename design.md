# 英雄寵物冒險遊戲 - 系統設計文檔

## 1. 系統架構設計

### 1.1 整體架構
```
Client (StarterPlayerScripts)
├── UI Controllers (原生Roblox GUI)
├── Game Controllers (Lua腳本)
└── Local Services (Signal通訊)

Server (ServerScriptService)
├── Game Services (Lua服務)
├── Data Management (ProfileService)
└── Combat & Economy Systems (Promise處理)

Shared (ReplicatedStorage)
├── Packages (第三方依賴)
│   ├── Promise.lua
│   ├── Signal.lua
│   ├── ProfileService.lua
│   └── TableUtil.lua
├── Modules (共享模組)
├── Events (RemoteEvents)
└── Configuration Files
```

### 1.2 核心技術棧
- **Lua**: 主要開發語言 (Roblox 原生)
- **Promise**: 非同步處理，取代傳統 callback
- **Signal**: 本地模組間通訊，效能比 BindableEvent 更好
- **ProfileService**: 玩家數據安全儲存，防止數據丟失
- **TableUtil**: 高效處理表格操作，合併、過濾等
- **原生Roblox服務**: 使用Roblox內建服務架構

## 2. 數據結構設計

### 2.1 玩家數據結構
```lua
-- 玩家數據結構 (ProfileService 模板)
local PlayerData = {
    -- 英雄屬性
    hero = {
        level = 1,               -- 1-100
        experience = 0,          -- 當前經驗值
        health = 150,            -- 當前血量
        maxHealth = 150,         -- 最大血量
        attack = 15,             -- 攻擊力
        defense = 8,             -- 防禦力
        critRate = 0.05,         -- 暴擊率
        critDamage = 1.5,        -- 暴擊傷害
    },

    -- 貨幣系統
    currencies = {
        coins = 1000,            -- 金幣
        gems = 20,               -- 寶石 (稀缺)
        robux = 0,               -- R幣 (僅記錄，不可贈送)
        experience = 0,          -- 經驗值貨幣
    },

    -- 裝備系統
    equipment = {
        weapon = nil,            -- 武器
        armor = nil,             -- 防具
        accessory = nil,         -- 飾品
    },

    -- 寵物系統
    pets = {
        active = {},             -- 當前攜帶寵物 (最多5隻)
        collection = {},         -- 寵物圖鑑記錄
    },

    -- 進度數據
    progress = {
        unlockedAreas = {"starter_town"}, -- 已解鎖區域
        completedQuests = {},    -- 已完成任務
        achievements = {},       -- 已獲得成就
    },

    -- 任務系統
    quests = {
        daily = {},              -- 每日任務
        weekly = {},             -- 週任務
        monthly = {},            -- 月任務
        dailyResetTime = 0,      -- 每日重置時間
        weeklyResetTime = 0,     -- 週重置時間
        monthlyResetTime = 0,    -- 月重置時間
        activePoints = 0,        -- 每日活躍度積分
    },

    -- VIP系統
    vip = {
        level = 0,               -- VIP等級 (0-10)
        totalSpent = 0,          -- 累積充值金額
        premiumExpiry = 0,       -- Premium會員到期時間
        premiumActive = false,   -- Premium會員狀態
    },

    -- 登入獎勵
    loginRewards = {
        currentDay = 1,          -- 當前登入天數 (1-30)
        lastClaimTime = 0,       -- 上次領取時間
        claimedDays = {},        -- 已領取天數記錄 (30天)
        missedDays = {},         -- 遺漏天數 (可補簽)
    },

    -- 新手系統
    newbie = {
        hasClaimedWelcomePet = false,   -- 是否已領取歡迎寵物
        welcomePetOptions = {},         -- 3選1寵物選項
        tutorialCompleted = false,      -- 教學是否完成
        daysSinceStart = 0,             -- 註冊天數
    },

    -- 社交數據
    social = {
        friends = {},            -- 好友列表
        guildId = nil,           -- 公會ID
        lastLogin = 0,           -- 上次登入時間
        loginStreak = 0,         -- 連續登入天數
    },
}
```

### 2.2 寵物數據結構
```lua
-- 寵物數據結構
local Pet = {
    id = "",                     -- 唯一ID
    speciesId = "",             -- 寵物種類ID
    name = "",                  -- 寵物名稱
    rarity = "common",          -- 稀有度
    level = 1,                  -- 等級 (1-100)
    experience = 0,             -- 經驗值

    -- 屬性
    stats = {
        health = 75,            -- 血量
        attack = 15,            -- 攻擊力
        defense = 7,            -- 防禦力
        speed = 21,             -- 速度
    },

    -- 技能
    skills = {},                -- 寵物技能列表

    -- 其他
    obtainedAt = 0,             -- 獲得時間
    isShiny = false,            -- 是否為閃光
}

-- 寵物稀有度枚舉
local PetRarity = {
    Common = "common",          -- 普通
    Rare = "rare",             -- 稀有
    Epic = "epic",             -- 史詩
    Mythic = "mythic",         -- 神話
    Legendary = "legendary"     -- 傳說
}

-- 任務系統數據結構
local DailyQuest = {
    id = "",                    -- 任務ID
    type = "",                  -- 任務類型
    target = 0,                 -- 目標數量
    progress = 0,               -- 當前進度
    completed = false,          -- 是否完成
    claimed = false,            -- 是否已領取獎勵
    rewards = {},               -- 獎勵列表
}

local WeeklyQuest = {
    id = "",
    type = "",
    target = 0,
    progress = 0,
    completed = false,
    claimed = false,
    rewards = {},
    weekNumber = 0,             -- 週數標識
}

local MonthlyQuest = {
    id = "",
    type = "",
    target = 0,
    progress = 0,
    completed = false,
    claimed = false,
    rewards = {},
    month = 0,                  -- 月份標識
    year = 0,                   -- 年份標識
}

local QuestType = {
    DefeatMonsters = "defeat_monsters",
    CollectPets = "collect_pets",
    UpgradePets = "upgrade_pets",
    CompleteAreas = "complete_areas",
    SpendCurrency = "spend_currency",
    LoginDays = "login_days",
    SocialInteraction = "social_interaction"
}

local QuestReward = {
    type = "currency",          -- "currency" | "item" | "pet" | "experience"
    id = nil,                   -- 物品或寵物ID
    amount = 0,                 -- 數量
}

-- VIP系統數據結構
local VIPBenefit = {
    loginRewardMultiplier = 1.0,    -- 登入獎勵倍數
    gachaDiscount = 0,              -- 扭蛋折扣
    expBonus = 0,                   -- 經驗加成
    dailyGems = 0,                  -- 每日寶石
    exclusiveShop = false,          -- 專屬商店
    tradeFeeDicount = 0,            -- 交易手續費折扣
}
```

### 2.3 裝備數據結構
```lua
-- 裝備數據結構
local Equipment = {
    id = "",                    -- 唯一ID
    itemId = "",               -- 裝備種類ID
    name = "",                 -- 裝備名稱
    type = "",                 -- 裝備類型
    rarity = "common",         -- 品質等級
    level = 1,                 -- 強化等級

    -- 屬性加成
    stats = {
        attack = 0,            -- 攻擊力加成
        defense = 0,           -- 防禦力加成
        health = 0,            -- 血量加成
        critRate = 0,          -- 暴擊率加成
    },

    -- 特殊效果
    effects = {},              -- 特殊效果列表
}
```

## 3. 系統模組設計

### 3.1 英雄系統 (HeroService)
```lua
-- 英雄系統服務
local HeroService = {}

-- 等級計算 (韓式慢速成長)
function HeroService:calculateLevelFromExp(experience)
    -- 根據經驗值計算等級
end

function HeroService:calculateExpForLevel(level)
    -- 指數級增長的經驗需求
end

-- 屬性計算
function HeroService:calculateMaxHealth(level)
    return 100 + (level * 50)
end

function HeroService:calculateBaseStats(level)
    -- 計算基礎屬性
end

-- 升級處理
function HeroService:levelUp(player)
    -- 處理升級邏輯
end

function HeroService:gainExperience(player, amount)
    -- 獲得經驗值
end

-- 韓式升級體驗
function HeroService:getExpMultiplierByLevel(level)
    -- 等級越高經驗需求越多
end

function HeroService:isLevelUpPossible(player)
    -- 檢查是否可以升級
end
```

### 3.2 寵物系統 (PetService) - 怪物收集系統
```lua
-- 寵物系統服務 (整合怪物和寵物)
local PetService = {}

-- 怪物遭遇系統
function PetService:encounterMonster(player, monsterId, zoneId)
    -- 玩家遭遇地圖怪物，解鎖圖鑑
    -- 記錄遭遇歷史，激發收集慾望
end

function PetService:getZoneMonsters(zoneId)
    -- 獲取指定區域的怪物列表
    -- Forest: 動物系, Ice: 冰系, Lava: 火系
end

-- 純扭蛋獲得系統
function PetService:rollGoldEgg(player)
    -- 金幣扭蛋：1000金幣，普通70%+稀有25%+史詩5%
end

function PetService:rollGemEgg(player)
    -- 寶石扭蛋：100寶石，稀有40%+史詩35%+神話20%+傳說5%
end

function PetService:rollRobuxEgg(player)
    -- R幣扭蛋：50R幣，保證稀有以上，傳說5%
end

function PetService:rollZoneEgg(player, zoneId)
    -- 區域限定扭蛋，只包含該區域怪物
end

-- 寵物管理
function PetService:addPetToPlayer(player, pet)
    -- 添加寵物到玩家收藏
end

function PetService:getPlayerActivePets(player)
    -- 獲取玩家當前攜帶的5隻寵物
end

function PetService:releasePet(player, petId)
    -- 釋放寵物獲得金幣和材料
end

-- 寵物升級
function PetService:gainPetExperience(player, petId, amount)
    -- 寵物獲得經驗值
end

function PetService:levelUpPet(player, petId)
    -- 寵物升級
end

-- 新手歡迎系統
function PetService:generateWelcomePetOptions()
    -- 生成3隻普通寵物選項 (從常見怪物中選擇)
end

function PetService:claimWelcomePet(player, petId)
    -- 領取歡迎寵物
end
```

### 3.3 戰鬥系統 (CombatService) - 怪物遭遇戰鬥
```lua
-- 戰鬥系統服務 (整合怪物生成和戰鬥)
local CombatService = {}

-- 怪物生成系統
function CombatService:spawnMonstersInZone(zoneId)
    -- 在指定區域生成怪物
    -- Forest: 動物系怪物, Ice: 冰系怪物, Lava: 火系怪物
end

function CombatService:getRandomMonsterByRarity(zoneId, rarity)
    -- 根據稀有度和區域獲取隨機怪物
    -- 普通70%, 稀有20%, 史詩8%, 神話2%, 傳說<1%
end

function CombatService:respawnMonster(monsterId, zoneId)
    -- 怪物被擊敗後重新生成
end

-- 戰鬥計算
function CombatService:calculateDamage(attacker, defender)
    -- 計算傷害值
end

function CombatService:processCombatTurn(battle)
    -- 處理戰鬥回合
end

-- 戰鬥管理
function CombatService:startBattle(player, monster)
    -- 開始與地圖怪物戰鬥
    -- 首次遭遇時觸發圖鑑解鎖
end

function CombatService:endBattle(battle)
    -- 結束戰鬥並給予獎勵
    -- 金幣、經驗值、材料獎勵
    -- 記錄怪物遭遇歷史
end

-- 特殊戰鬥
function CombatService:createBossEncounter(bossId, zoneId)
    -- 創建區域BOSS遭遇 (神話/傳說級怪物)
end

function CombatService:joinBossRaid(player, raidId)
    -- 加入BOSS戰 (多人協作)
end
```

### 3.4 經濟系統 (EconomyService)
```lua
-- 經濟系統服務
local EconomyService = {}

-- 貨幣操作
function EconomyService:addCurrency(player, currencyType, amount)
    -- 添加貨幣
end

function EconomyService:removeCurrency(player, currencyType, amount)
    -- 扣除貨幣
end

-- R幣系統 (特殊處理)
function EconomyService:canSpendRobux(player, amount)
    -- 檢查R幣餘額
end

function EconomyService:spendRobux(player, amount)
    -- 消費R幣
end

-- 交易系統
function EconomyService:createTrade(seller, buyer, items)
    -- 創建交易
end

function EconomyService:completeTrade(tradeId)
    -- 完成交易
end

-- 拍賣系統
function EconomyService:createAuction(seller, item, startPrice)
    -- 創建拍賣
end

function EconomyService:placeBid(player, auctionId, amount)
    -- 出價
end

-- 寶石稀缺性控制
function EconomyService:getGemRewardAmount(source)
    -- 控制寶石獲得量
end
```

### 3.5 任務系統 (QuestService)
```typescript
export class QuestService {
    // 每日任務
    generateDailyQuests(player: Player): DailyQuest[];
    updateQuestProgress(player: Player, questType: QuestType, amount: number): void;
    claimQuestReward(player: Player, questId: string): boolean;
    refreshDailyQuests(player: Player): void;

    // 週任務
    generateWeeklyQuests(player: Player): WeeklyQuest[];
    resetWeeklyQuests(player: Player): void;

    // 月任務
    generateMonthlyQuests(player: Player): MonthlyQuest[];
    resetMonthlyQuests(player: Player): void;

    // 活躍度系統
    addActivePoints(player: Player, points: number): void;
    claimActiveReward(player: Player, tier: number): boolean;
}
```

### 3.6 VIP系統 (VIPService)
```typescript
export class VIPService {
    // VIP等級管理
    updateVIPLevel(player: Player, robuxSpent: number): void; // 使用R幣計算
    getVIPBenefits(vipLevel: number): VIPBenefit;

    // Premium會員
    activatePremium(player: Player, duration: number): void;
    checkPremiumStatus(player: Player): boolean;
    getPremiumBenefits(): PremiumBenefit[];

    // VIP特權
    applyVIPMultiplier(player: Player, baseAmount: number, type: string): number;
    canAccessVIPShop(player: Player): boolean;
    getVIPDailyRewards(player: Player): Reward[];
}
```

### 3.7 登入獎勵系統 (LoginRewardService)
```typescript
export class LoginRewardService {
    // 30天登入獎勵
    checkLoginReward(player: Player): LoginRewardInfo;
    claimLoginReward(player: Player, day: number): boolean;

    // 補簽功能 (使用寶石)
    canMakeUpDay(player: Player, day: number): boolean;
    makeUpMissedDay(player: Player, day: number): boolean;
    getMakeUpCost(day: number): number; // 寶石成本

    // 獎勵計算 (不包含R幣)
    calculateDailyReward(day: number, vipLevel: number): Reward[];
    getMonthlySpecialRewards(): Reward[];
}
```

### 3.8 區域系統 (AreaService)
```typescript
export class AreaService {
    // 區域管理
    unlockArea(player: Player, areaId: string): boolean;
    checkUnlockRequirements(player: Player, areaId: string): boolean;

    // 怪物生成
    spawnMonsters(areaId: string): Monster[];
    spawnBoss(areaId: string): Boss;

    // 資源管理
    getAreaResources(areaId: string): Resource[];
    collectResource(player: Player, resourceId: string): boolean;
}
```

## 4. UI系統設計

### 4.1 主界面布局
```
┌─────────────────────────────────────┐
│ 頂部狀態欄 (血量、等級、貨幣)        │
├─────────────────────────────────────┤
│                                     │
│         主遊戲區域                   │
│     (3D場景或戰鬥畫面)               │
│                                     │
├─────────────────────────────────────┤
│ 底部功能欄                          │
│ [背包][寵物][商店][任務][社交]       │
└─────────────────────────────────────┘
```

### 4.2 Roblox GUI 架構
```lua
-- 主UI控制器
local GameUI = {}

function GameUI:init()
    -- 初始化UI組件
    self:createStatusBar()
    self:createGameView()
    self:createNavigationBar()
    self:createModalManager()
end

-- 狀態欄組件
function GameUI:createStatusBar()
    local statusBar = Instance.new("Frame")
    statusBar.Name = "StatusBar"
    statusBar.Size = UDim2.new(1, 0, 0, 60)
    statusBar.Position = UDim2.new(0, 0, 0, 0)

    -- 血量條
    self:createHealthBar(statusBar)
    -- 等級顯示
    self:createLevelDisplay(statusBar)
    -- 貨幣顯示
    self:createCurrencyDisplay(statusBar)
end
```

### 4.3 狀態管理 (Signal + Promise)
```lua
-- 玩家狀態管理
local PlayerState = {}
local Signal = require(game.ReplicatedStorage.Packages.Signal)

-- 狀態變化信號
PlayerState.dataChanged = Signal.new()
PlayerState.heroStatsChanged = Signal.new()
PlayerState.currencyChanged = Signal.new()

-- 當前狀態
PlayerState.current = {
    data = nil,
    isLoading = false,
    error = nil
}

-- 設置玩家數據
function PlayerState:setPlayerData(data)
    self.current.data = data
    self.current.isLoading = false
    self.dataChanged:Fire(data)
end

-- 更新英雄屬性
function PlayerState:updateHeroStats(newStats)
    if self.current.data then
        for key, value in pairs(newStats) do
            self.current.data.hero[key] = value
        end
        self.heroStatsChanged:Fire(self.current.data.hero)
    end
end

-- 添加貨幣
function PlayerState:addCurrency(currencyType, amount)
    if self.current.data then
        self.current.data.currencies[currencyType] =
            self.current.data.currencies[currencyType] + amount
        self.currencyChanged:Fire(self.current.data.currencies)
    end
end
```

## 5. 網路通信設計

### 5.1 RemoteEvent 定義
```lua
-- 遊戲事件定義 (RemoteEvents)
local GameEvents = {
    -- 英雄系統
    HeroGainExperience = "HeroGainExperience",
    HeroLevelUp = "HeroLevelUp",

    -- 寵物系統
    PetRollEgg = "PetRollEgg",
    PetLevelUp = "PetLevelUp",
    PetEquip = "PetEquip",

    -- 戰鬥系統
    CombatAttackMonster = "CombatAttackMonster",
    CombatJoinBossRaid = "CombatJoinBossRaid",

    -- 經濟系統
    EconomyPurchaseItem = "EconomyPurchaseItem",
    EconomyCreateTrade = "EconomyCreateTrade",

    -- 任務系統
    QuestClaimDailyReward = "QuestClaimDailyReward",
    QuestClaimWeeklyReward = "QuestClaimWeeklyReward",
    QuestClaimMonthlyReward = "QuestClaimMonthlyReward",
    QuestRefreshDaily = "QuestRefreshDaily",
    QuestClaimActiveReward = "QuestClaimActiveReward",

    -- 登入獎勵系統
    LoginClaimDailyReward = "LoginClaimDailyReward",
    LoginMakeUpMissedDay = "LoginMakeUpMissedDay",

    -- VIP系統
    VIPPurchasePackage = "VIPPurchasePackage",
    VIPActivatePremium = "VIPActivatePremium",
    VIPClaimReward = "VIPClaimReward",

    -- 社交系統
    SocialAddFriend = "SocialAddFriend",
    SocialSendMessage = "SocialSendMessage",
}
```

### 5.2 數據同步策略
- **即時同步**: 戰鬥結果、貨幣變化、等級提升
- **定期同步**: 寵物狀態、裝備信息、進度數據
- **按需同步**: 好友列表、公會信息、排行榜

## 6. 配置系統設計

### 6.1 遊戲配置文件
```lua
-- 寵物配置
local PET_CONFIG = {
    rarityChances = {
        basic = {
            common = 0.7,
            rare = 0.25,
            epic = 0.05,
        },
        premium = {
            rare = 0.4,
            epic = 0.35,
            mythic = 0.2,
            legendary = 0.05,
        },
    },

    statMultipliers = {
        common = 1.0,
        rare = 1.5,
        epic = 2.0,
        mythic = 3.0,
        legendary = 5.0,
    },
}

-- 經濟配置
local ECONOMY_CONFIG = {
    eggPrices = {
        basic = { coins = 1000 },
        premium = { gems = 100 },
        limited = { robux = 50 },
    },

    areaUnlockCosts = {
        forest = { coins = 5000, level = 10 },
        desert = { coins = 15000, level = 25 },
        mountain = { coins = 35000, level = 50 },
    },
}
```

## 7. 安全性設計

### 7.1 防作弊機制
- **服務器驗證**: 所有重要操作在服務器端驗證
- **數據校驗**: 定期檢查玩家數據的合理性
- **行為監控**: 檢測異常的遊戲行為模式
- **限制機制**: 操作頻率和數量限制

### 7.2 數據保護
- **加密傳輸**: 敏感數據加密傳輸
- **備份機制**: 定期自動備份玩家數據
- **版本控制**: 數據結構版本管理和遷移
- **錯誤恢復**: 數據損壞的檢測和修復

## 8. 性能優化設計

### 8.1 客戶端優化
- **資源管理**: 按需載入和釋放資源
- **UI優化**: 虛擬化長列表，減少DOM操作
- **動畫優化**: 使用高效的動畫庫和技術

### 8.2 服務器優化
- **數據庫優化**: 合理的數據結構和查詢優化
- **緩存策略**: 熱點數據緩存機制
- **負載均衡**: 分散服務器負載

### 8.3 網路優化
- **數據壓縮**: 減少網路傳輸數據量
- **批量操作**: 合併多個小操作為批量操作
- **連接管理**: 優化網路連接的建立和維護

## 9. 遊戲平衡設計

### 9.1 經濟平衡公式
```lua
-- 英雄等級經驗值需求 (韓式慢速成長)
local function getExpForLevel(level)
    if level <= 10 then
        -- 1-10級：快速升級期
        return math.floor(100 * math.pow(1.2, level - 1))
    elseif level <= 30 then
        -- 11-30級：開始變慢
        return math.floor(1000 * math.pow(1.4, level - 10))
    elseif level <= 60 then
        -- 31-60級：明顯變慢
        return math.floor(50000 * math.pow(1.6, level - 30))
    else
        -- 61-100級：極慢升級 (天堂式體驗)
        return math.floor(5000000 * math.pow(1.8, level - 60))
    end
end

-- 寵物屬性計算
local function calculatePetStats(level, rarity)
    local baseStats = {
        health = 50 + (level * 25),
        attack = 10 + (level * 5),
        defense = 5 + (level * 2),
        speed = 20 + (level * 1)
    }

    local multiplier = PET_CONFIG.statMultipliers[rarity]
    return {
        health = math.floor(baseStats.health * multiplier),
        attack = math.floor(baseStats.attack * multiplier),
        defense = math.floor(baseStats.defense * multiplier),
        speed = math.floor(baseStats.speed * multiplier)
    }
end

-- 戰鬥傷害計算
local function calculateDamage(attacker, defender)
    local baseDamage = attacker.attack
    local defense = defender.defense
    local critChance = attacker.critRate or 0.05
    local critMultiplier = attacker.critDamage or 1.5

    local damage = math.max(1, baseDamage - defense * 0.5)

    -- 暴擊計算
    if math.random() < critChance then
        damage = damage * critMultiplier
    end

    -- 隨機浮動 ±10%
    local variance = 0.9 + math.random() * 0.2
    return math.floor(damage * variance)
end
```

### 9.2 扭蛋機率設計
```lua
local GACHA_RATES = {
    starter = {
        common = 1.0,               -- 100% 普通
    },
    basic = {
        common = 0.70,              -- 70% 普通
        rare = 0.25,                -- 25% 稀有
        epic = 0.05,                -- 5% 史詩
    },
    premium = {
        rare = 0.40,                -- 40% 稀有
        epic = 0.35,                -- 35% 史詩
        mythic = 0.20,              -- 20% 神話
        legendary = 0.05,           -- 5% 傳說
    },
    limited = {
        epic = 0.30,                -- 30% 史詩
        mythic = 0.40,              -- 40% 神話
        legendary = 0.30,           -- 30% 傳說
    }
}

-- 保底機制
local PITY_SYSTEM = {
    epic = { threshold = 20, guarantee = "epic" },
    mythic = { threshold = 50, guarantee = "mythic" },
    legendary = { threshold = 100, guarantee = "legendary" }
}
```

### 9.3 區域解鎖成本設計
```lua
local AREA_UNLOCK_COSTS = {
    starter_town = { level = 1, coins = 0 },
    green_forest = { level = 5, coins = 2000 },
    dark_cave = { level = 15, coins = 8000 },
    burning_desert = { level = 25, coins = 20000 },
    frozen_peaks = { level = 40, coins = 50000 },
    shadow_realm = { level = 60, coins = 100000 },
    celestial_tower = { level = 80, coins = 200000 },
    void_dimension = { level = 95, coins = 500000 }
}
```

## 10. 數據持久化設計

### 10.1 ProfileService 整合
```lua
-- ProfileService 數據模板
local ProfileService = require(game.ReplicatedStorage.Packages.ProfileService)

local ProfileTemplate = {
    -- 版本控制
    dataVersion = 1,

    -- 英雄數據
    hero = {
        level = 1,
        experience = 0,
        health = 150,
        maxHealth = 150,
        stats = {
            attack = 15,
            defense = 8,
            critRate = 0.05,
            critDamage = 1.5
        }
    },

    -- 貨幣數據
    currencies = {
        coins = 1000,
        gems = 20,              -- 減少初始寶石
        robux = 0,              -- R幣初始為0
        experience = 0
    },

    -- 寵物數據
    pets = {
        active = {},
        collection = {},
        nextId = 1
    },

    -- 裝備數據
    equipment = {
        weapon = nil,
        armor = nil,
        accessory = nil,
        inventory = {}
    },

    -- 任務系統數據
    quests = {
        daily = {},
        weekly = {},
        monthly = {},
        dailyResetTime = 0,
        weeklyResetTime = 0,
        monthlyResetTime = 0,
        activePoints = 0,
        activeRewardsClaimed = {}
    },

    -- VIP系統數據
    vip = {
        level = 0,
        totalSpent = 0,
        premiumExpiry = 0,
        premiumActive = false,
        lastVIPRewardClaim = 0
    },

    -- 登入獎勵數據
    loginRewards = {
        currentDay = 1,
        lastClaimTime = 0,
        claimedDays = {},
        missedDays = {}
    },

    // 新手系統數據
    newbie: {
        hasClaimedWelcomePet: false,
        welcomePetOptions: [],
        tutorialCompleted: false,
        daysSinceStart: 0
    },

    // 進度數據
    progress: {
        unlockedAreas: ["starter_town"],
        completedQuests: [],
        achievements: [],
        gachaHistory: [],
        combatStats: {
            monstersDefeated: 0,
            bossesDefeated: 0,
            totalDamageDealt: 0
        },
        newPlayerProgress: {
            tutorialCompleted: false,
            firstPurchaseOffered: false,
            daysSinceStart: 0
        }
    },

    // 社交數據
    social: {
        friends: [],
        guildId: null,
        lastLogin: 0,
        loginStreak: 0,
        dailyRewardsClaimed: []
    },

    // 設置數據
    settings: {
        soundEnabled: true,
        musicEnabled: true,
        notificationsEnabled: true,
        language: "zh-TW"
    }
};
```

### 10.2 數據遷移策略
```lua
-- 數據遷移工具
local DataMigration = {}

function DataMigration.migrateToVersion2(data)
    -- 添加新的貨幣類型
    if not data.currencies.shopCoins then
        data.currencies.shopCoins = 0
    end

    -- 添加新的統計數據
    if not data.progress.combatStats then
        data.progress.combatStats = {
            monstersDefeated = 0,
            bossesDefeated = 0,
            totalDamageDealt = 0
        }
    end

    data.dataVersion = 2
    return data
end

function DataMigration.migrateToVersion3(data)
    -- 重構寵物數據結構
    if data.pets and not data.pets.collection then
        data.pets.collection = data.pets.owned or {}
        data.pets.active = data.pets.equipped or {}
        data.pets.owned = nil
        data.pets.equipped = nil
    end

    data.dataVersion = 3
    return data
end
```

## 11. 錯誤處理和日誌系統

### 11.1 錯誤分類和處理
```lua
-- 錯誤類型枚舉
local ErrorType = {
    NetworkError = "NETWORK_ERROR",
    DataError = "DATA_ERROR",
    ValidationError = "VALIDATION_ERROR",
    GameLogicError = "GAME_LOGIC_ERROR",
    UIError = "UI_ERROR"
}

-- 遊戲錯誤結構
local GameError = {
    type = "",
    message = "",
    context = nil,
    timestamp = 0,
    playerId = nil
}

-- 錯誤處理器
local ErrorHandler = {}

function ErrorHandler.handleError(error)
    -- 記錄錯誤
    ErrorHandler.logError(error)

    -- 根據錯誤類型決定處理方式
    if error.type == ErrorType.NetworkError then
        ErrorHandler.handleNetworkError(error)
    elseif error.type == ErrorType.DataError then
        ErrorHandler.handleDataError(error)
    elseif error.type == ErrorType.ValidationError then
        ErrorHandler.handleValidationError(error)
    else
        ErrorHandler.handleGenericError(error)
    end
end

function ErrorHandler.handleNetworkError(error)
    -- 顯示重試提示
    -- 自動重試機制
    -- 離線模式切換
end

function ErrorHandler.handleDataError(error)
    -- 數據恢復嘗試
    -- 備份數據載入
    -- 用戶通知
end
```

### 11.2 性能監控
```lua
-- 性能監控工具
local PerformanceMonitor = {}
local metrics = {}

function PerformanceMonitor.startTimer(operation)
    local startTime = tick()

    return function()
        local endTime = tick()
        local duration = endTime - startTime

        if not metrics[operation] then
            metrics[operation] = {}
        end

        table.insert(metrics[operation], duration)

        -- 如果操作時間過長，記錄警告
        if duration > 0.1 then -- 100ms
            warn("Slow operation detected: " .. operation .. " took " .. duration .. "s")
        end
    end
end

function PerformanceMonitor.getAverageTime(operation)
    local times = metrics[operation]
    if not times or #times == 0 then
        return 0
    end

    local sum = 0
    for _, time in ipairs(times) do
        sum = sum + time
    end

    return sum / #times
end

## 12. 任務系統配置

### 12.1 每日任務配置
```lua
-- 每日任務配置
local DAILY_QUEST_CONFIG = {
    -- 任務池配置
    questPool = {
        {
            id = "defeat_monsters_easy",
            type = QuestType.DefeatMonsters,
            target = 10,
            weight = 30,
            rewards = {
                { type = "currency", id = "coins", amount = 500 },
                { type = "experience", amount = 100 }
            }
        },
        {
            id = "collect_pets",
            type = QuestType.CollectPets,
            target = 1,
            weight = 15,
            rewards = {
                { type = "currency", id = "gems", amount = 5 }, -- 減少寶石獎勵
                { type = "currency", id = "coins", amount = 300 }
            }
        },
        {
            id = "upgrade_pets",
            type = QuestType.UpgradePets,
            target = 3,
            weight = 20,
            rewards = {
                { type = "currency", id = "coins", amount = 800 },
                { type = "experience", amount = 150 }
            }
        }
    },

    -- 活躍度獎勵配置
    activeRewards = {
        { points = 20, rewards = {{ type = "currency", id = "coins", amount = 1000 }} },
        { points = 40, rewards = {{ type = "currency", id = "gems", amount = 10 }} }, -- 減少寶石
        { points = 60, rewards = {{ type = "item", id = "basic_egg", amount = 1 }} },
        { points = 80, rewards = {{ type = "currency", id = "coins", amount = 2000 }} },
        { points = 100, rewards = {{ type = "item", id = "premium_egg", amount = 1 }} }
    }
}
```

### 12.2 VIP系統配置
```typescript
export const VIP_CONFIG = {
    // VIP等級門檻 (R幣)
    levelThresholds: [
        { level: 0, requiredSpent: 0 },
        { level: 1, requiredSpent: 80 },      // 約$1
        { level: 2, requiredSpent: 240 },     // 約$3
        { level: 3, requiredSpent: 480 },     // 約$6
        { level: 4, requiredSpent: 800 },     // 約$10
        { level: 5, requiredSpent: 1600 },    // 約$20
        { level: 6, requiredSpent: 3200 },    // 約$40
        { level: 7, requiredSpent: 6400 },    // 約$80
        { level: 8, requiredSpent: 12000 },   // 約$150
        { level: 9, requiredSpent: 20000 },   // 約$250
        { level: 10, requiredSpent: 40000 }   // 約$500
    ],

    // VIP特權配置
    benefits: {
        0: { loginRewardMultiplier: 1.0, gachaDiscount: 0, expBonus: 0, dailyGems: 0 },
        1: { loginRewardMultiplier: 1.2, gachaDiscount: 0.05, expBonus: 0.1, dailyGems: 10 },
        2: { loginRewardMultiplier: 1.4, gachaDiscount: 0.08, expBonus: 0.15, dailyGems: 15 },
        3: { loginRewardMultiplier: 1.6, gachaDiscount: 0.12, expBonus: 0.2, dailyGems: 20 },
        4: { loginRewardMultiplier: 1.8, gachaDiscount: 0.15, expBonus: 0.3, dailyGems: 25 },
        5: { loginRewardMultiplier: 2.0, gachaDiscount: 0.18, expBonus: 0.4, dailyGems: 35 },
        6: { loginRewardMultiplier: 2.2, gachaDiscount: 0.22, expBonus: 0.5, dailyGems: 45 },
        7: { loginRewardMultiplier: 2.5, gachaDiscount: 0.25, expBonus: 0.6, dailyGems: 60 },
        8: { loginRewardMultiplier: 2.8, gachaDiscount: 0.28, expBonus: 0.8, dailyGems: 80 },
        9: { loginRewardMultiplier: 3.0, gachaDiscount: 0.30, expBonus: 1.0, dailyGems: 100 },
        10: { loginRewardMultiplier: 3.5, gachaDiscount: 0.35, expBonus: 1.5, dailyGems: 150 }
    },

    // Premium會員配置
    premium: {
        monthlyPrice: 400, // 400 R幣 (約$5)
        benefits: {
            dailyGems: 20,     // 減少每日寶石
            exclusiveQuests: true,
            monthlyEgg: true,
            tradeFeeDicount: 0.5,
            prioritySupport: true
        }
    }
};
```

### 12.3 登入獎勵配置
```typescript
export const LOGIN_REWARD_CONFIG = {
    // 30天獎勵配置
    rewards: [
        // 第1-7天
        { day: 1, rewards: [{ type: "currency", id: "coins", amount: 1000 }] },
        { day: 2, rewards: [{ type: "currency", id: "gems", amount: 5 }] },  // 減少寶石
        { day: 3, rewards: [{ type: "currency", id: "coins", amount: 1500 }] },
        { day: 4, rewards: [{ type: "currency", id: "coins", amount: 2000 }] },
        { day: 5, rewards: [{ type: "currency", id: "gems", amount: 8 }] },   // 減少寶石
        { day: 6, rewards: [{ type: "currency", id: "coins", amount: 2500 }] },
        { day: 7, rewards: [{ type: "item", id: "rare_pet_egg", amount: 1 }] }, // 特殊獎勵

        // 第8-14天
        { day: 8, rewards: [{ type: "currency", id: "coins", amount: 2500 }] },
        { day: 9, rewards: [{ type: "currency", id: "gems", amount: 40 }] },
        { day: 10, rewards: [{ type: "currency", id: "shopCoins", amount: 15 }] },
        { day: 11, rewards: [{ type: "currency", id: "coins", amount: 3000 }] },
        { day: 12, rewards: [{ type: "currency", id: "gems", amount: 50 }] },
        { day: 13, rewards: [{ type: "currency", id: "coins", amount: 3500 }] },
        { day: 14, rewards: [{ type: "item", id: "epic_pet_egg", amount: 1 }] }, // 特殊獎勵

        // 第15-21天
        { day: 15, rewards: [{ type: "currency", id: "coins", amount: 4000 }] },
        { day: 16, rewards: [{ type: "currency", id: "gems", amount: 60 }] },
        { day: 17, rewards: [{ type: "currency", id: "shopCoins", amount: 20 }] },
        { day: 18, rewards: [{ type: "currency", id: "coins", amount: 4500 }] },
        { day: 19, rewards: [{ type: "currency", id: "gems", amount: 70 }] },
        { day: 20, rewards: [{ type: "currency", id: "coins", amount: 5000 }] },
        { day: 21, rewards: [{ type: "item", id: "mythic_pet_egg", amount: 1 }] }, // 特殊獎勵

        // 第22-30天
        { day: 22, rewards: [{ type: "currency", id: "coins", amount: 6000 }] },
        { day: 23, rewards: [{ type: "currency", id: "gems", amount: 80 }] },
        { day: 24, rewards: [{ type: "currency", id: "shopCoins", amount: 25 }] },
        { day: 25, rewards: [{ type: "currency", id: "coins", amount: 7000 }] },
        { day: 26, rewards: [{ type: "currency", id: "gems", amount: 90 }] },
        { day: 27, rewards: [{ type: "currency", id: "coins", amount: 8000 }] },
        { day: 28, rewards: [{ type: "currency", id: "gems", amount: 100 }] },
        { day: 29, rewards: [{ type: "currency", id: "shopCoins", amount: 50 }] },
        { day: 30, rewards: [{ type: "pet", id: "legendary_login_pet", amount: 1 }] } // 終極獎勵
    ],

    // 補簽價格配置
    makeUpCosts: [
        { day: 1, cost: 10 },   // 第1天補簽10寶石
        { day: 2, cost: 15 },   // 第2天補簽15寶石
        { day: 3, cost: 20 },   // 依此類推...
        // ... 最多可補簽7天
    ]
};
```

### 12.4 新手引導付費配置
```typescript
export const NEWBIE_MONETIZATION_CONFIG = {
    // 首購優惠
    firstPurchase: {
        discountPercent: 50,
        timeLimit: 24 * 60 * 60, // 24小時
        packages: [
            {
                id: "newbie_starter",
                originalPrice: 0.99,
                discountPrice: 0.49,
                contents: [
                    { type: "currency", id: "gems", amount: 200 },
                    { type: "pet", id: "rare_starter_pet", amount: 1 },
                    { type: "currency", id: "coins", amount: 5000 }
                ]
            }
        ]
    },

    // 進度卡點觸發
    progressTriggers: [
        {
            condition: "hero_level_10",
            package: "power_boost_pack",
            message: "需要更強的力量來面對挑戰？"
        },
        {
            condition: "pet_inventory_full",
            package: "inventory_expansion",
            message: "寵物背包已滿，擴展空間繼續收集！"
        },
        {
            condition: "failed_boss_3_times",
            package: "boss_helper_pack",
            message: "BOSS太強了？試試這個助戰包！"
        }
    ],

    // 挫折救援系統
    frustrationDetection: {
        triggers: [
            { event: "combat_loss_streak", threshold: 5 },
            { event: "gacha_bad_luck", threshold: 10 },
            { event: "stuck_same_level", threshold: 3 }
        ],
        rescuePackages: [
            {
                id: "power_boost",
                price: 160, // 160 R幣 (約$2)
                contents: [
                    { type: "item", id: "exp_potion", amount: 5 },
                    { type: "item", id: "strength_boost", amount: 3 }
                ]
            }
        ]
    }
};
```

### 12.5 新手歡迎系統配置
```typescript
export const NEWBIE_WELCOME_CONFIG = {
    // 新手歡迎扭蛋 (3選1)
    welcomeGacha: {
        petOptions: [
            {
                id: "starter_cat",
                name: "新手小貓",
                rarity: PetRarity.Common,
                description: "忠實的夥伴，攻擊力較高"
            },
            {
                id: "starter_dog",
                name: "新手小狗",
                rarity: PetRarity.Common,
                description: "勇敢的守護者，血量較高"
            },
            {
                id: "starter_bird",
                name: "新手小鳥",
                rarity: PetRarity.Common,
                description: "敏捷的偵察兵，速度較快"
            }
        ]
    },

    // 新手教學流程
    tutorial: {
        steps: [
            { id: "welcome", action: "show_welcome_message" },
            { id: "choose_pet", action: "show_pet_selection" },
            { id: "first_battle", action: "guide_first_combat" },
            { id: "level_up", action: "explain_leveling" },
            { id: "daily_quest", action: "introduce_quests" }
        ]
    }
};
```
```
