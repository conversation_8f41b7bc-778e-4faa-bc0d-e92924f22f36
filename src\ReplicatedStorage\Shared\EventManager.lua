-- 事件管理器 - 統一管理遊戲中的所有事件
local Signal = require(game.ReplicatedStorage.Packages.Signal)

local EventManager = {}

-- 事件類型定義
EventManager.EventTypes = {
    -- 玩家事件
    PLAYER_JOINED = "PlayerJoined",
    PLAYER_LEFT = "PlayerLeft",
    PLAYER_LEVEL_UP = "PlayerLevelUp",
    PLAYER_DIED = "PlayerDied",
    PLAYER_RESPAWNED = "PlayerRespawned",
    
    -- 戰鬥事件
    COMBAT_STARTED = "CombatStarted",
    COMBAT_ENDED = "CombatEnded",
    DAMAGE_DEALT = "DamageDealt",
    DAMAGE_RECEIVED = "DamageReceived",
    MONSTER_DEFEATED = "MonsterDefeated",
    PLAYER_DEFEATED = "PlayerDefeated",
    
    -- 寵物事件
    PET_OBTAINED = "PetObtained",
    PET_LEVEL_UP = "PetLevelUp",
    PET_EVOLVED = "PetEvolved",
    PET_RELEASED = "PetReleased",
    PET_RENAMED = "PetRenamed",
    WILD_PET_SPAWNED = "WildPetSpawned",
    WILD_PET_CAUGHT = "WildPetCaught",
    WILD_PET_ESCAPED = "WildPetEscaped",
    
    -- 區域事件
    ZONE_ENTERED = "ZoneEntered",
    ZONE_EXITED = "ZoneExited",
    ZONE_UNLOCKED = "ZoneUnlocked",
    TELEPORT_COMPLETED = "TeleportCompleted",
    
    -- 經濟事件
    CURRENCY_CHANGED = "CurrencyChanged",
    PURCHASE_COMPLETED = "PurchaseCompleted",
    PURCHASE_FAILED = "PurchaseFailed",
    ITEM_BOUGHT = "ItemBought",
    ITEM_SOLD = "ItemSold",
    
    -- 任務事件
    QUEST_STARTED = "QuestStarted",
    QUEST_COMPLETED = "QuestCompleted",
    QUEST_PROGRESS_UPDATED = "QuestProgressUpdated",
    QUEST_REWARD_CLAIMED = "QuestRewardClaimed",
    QUEST_FAILED = "QuestFailed",
    
    -- PvP 事件
    PVP_MATCH_STARTED = "PvPMatchStarted",
    PVP_MATCH_ENDED = "PvPMatchEnded",
    PVP_PLAYER_JOINED = "PvPPlayerJoined",
    PVP_PLAYER_LEFT = "PvPPlayerLeft",
    PVP_ROUND_STARTED = "PvPRoundStarted",
    PVP_ROUND_ENDED = "PvPRoundEnded",
    
    -- 系統事件
    SYSTEM_ERROR = "SystemError",
    SYSTEM_WARNING = "SystemWarning",
    DATA_SAVED = "DataSaved",
    DATA_LOADED = "DataLoaded",
    SERVER_SHUTDOWN = "ServerShutdown",
    
    -- UI 事件
    UI_OPENED = "UIOpened",
    UI_CLOSED = "UIClosed",
    NOTIFICATION_SHOWN = "NotificationShown",
    BUTTON_CLICKED = "ButtonClicked",
    
    -- 特殊事件
    ACHIEVEMENT_UNLOCKED = "AchievementUnlocked",
    MILESTONE_REACHED = "MilestoneReached",
    EVENT_STARTED = "EventStarted",
    EVENT_ENDED = "EventEnded"
}

-- 事件信號存儲
EventManager._signals = {}

-- 事件監聽器存儲
EventManager._listeners = {}

-- 事件統計
EventManager._stats = {
    totalEvents = 0,
    eventCounts = {},
    lastEventTime = 0
}

-- 初始化事件管理器
function EventManager:Initialize()
    -- 為每個事件類型創建信號
    for eventName, eventType in pairs(self.EventTypes) do
        self._signals[eventType] = Signal.new()
        self._stats.eventCounts[eventType] = 0
    end
    
    print("📡 EventManager 已初始化，共", self:GetEventTypeCount(), "種事件類型")
end

-- 觸發事件
function EventManager:Fire(eventType, eventData)
    if not self._signals[eventType] then
        warn("❌ 未知的事件類型:", eventType)
        return false
    end
    
    -- 添加時間戳
    local enrichedData = eventData or {}
    enrichedData.timestamp = tick()
    enrichedData.eventType = eventType
    
    -- 觸發信號
    self._signals[eventType]:Fire(enrichedData)
    
    -- 更新統計
    self._stats.totalEvents = self._stats.totalEvents + 1
    self._stats.eventCounts[eventType] = self._stats.eventCounts[eventType] + 1
    self._stats.lastEventTime = tick()
    
    -- 調試輸出
    if game:GetService("RunService"):IsStudio() then
        print("📡 事件觸發:", eventType, "數據:", enrichedData)
    end
    
    return true
end

-- 監聽事件
function EventManager:Connect(eventType, callback)
    if not self._signals[eventType] then
        warn("❌ 未知的事件類型:", eventType)
        return nil
    end
    
    local connection = self._signals[eventType]:Connect(callback)
    
    -- 記錄監聽器
    if not self._listeners[eventType] then
        self._listeners[eventType] = {}
    end
    table.insert(self._listeners[eventType], connection)
    
    return connection
end

-- 一次性監聽事件
function EventManager:Once(eventType, callback)
    if not self._signals[eventType] then
        warn("❌ 未知的事件類型:", eventType)
        return nil
    end
    
    local connection
    connection = self._signals[eventType]:Connect(function(...)
        callback(...)
        connection:Disconnect()
    end)
    
    return connection
end

-- 斷開事件監聽
function EventManager:Disconnect(eventType, connection)
    if connection then
        connection:Disconnect()
        
        -- 從監聽器列表中移除
        if self._listeners[eventType] then
            for i, conn in ipairs(self._listeners[eventType]) do
                if conn == connection then
                    table.remove(self._listeners[eventType], i)
                    break
                end
            end
        end
    end
end

-- 斷開所有監聽器
function EventManager:DisconnectAll(eventType)
    if self._listeners[eventType] then
        for _, connection in ipairs(self._listeners[eventType]) do
            connection:Disconnect()
        end
        self._listeners[eventType] = {}
    end
end

-- 等待事件
function EventManager:Wait(eventType, timeout)
    if not self._signals[eventType] then
        warn("❌ 未知的事件類型:", eventType)
        return nil
    end
    
    if timeout then
        local startTime = tick()
        local connection
        local result = nil
        
        connection = self._signals[eventType]:Connect(function(data)
            result = data
            connection:Disconnect()
        end)
        
        while not result and (tick() - startTime) < timeout do
            wait(0.1)
        end
        
        if connection then
            connection:Disconnect()
        end
        
        return result
    else
        return self._signals[eventType]:Wait()
    end
end

-- 批量觸發事件
function EventManager:FireBatch(events)
    for _, event in ipairs(events) do
        self:Fire(event.type, event.data)
    end
end

-- 延遲觸發事件
function EventManager:FireDelayed(eventType, eventData, delay)
    task.spawn(function()
        task.wait(delay)
        self:Fire(eventType, eventData)
    end)
end

-- 條件觸發事件
function EventManager:FireIf(condition, eventType, eventData)
    if condition then
        return self:Fire(eventType, eventData)
    end
    return false
end

-- 獲取事件統計
function EventManager:GetStats()
    return {
        totalEvents = self._stats.totalEvents,
        eventCounts = self._stats.eventCounts,
        lastEventTime = self._stats.lastEventTime,
        uptime = tick() - (self._stats.lastEventTime or tick())
    }
end

-- 獲取事件類型數量
function EventManager:GetEventTypeCount()
    local count = 0
    for _ in pairs(self.EventTypes) do
        count = count + 1
    end
    return count
end

-- 獲取監聽器數量
function EventManager:GetListenerCount(eventType)
    if eventType then
        return self._listeners[eventType] and #self._listeners[eventType] or 0
    else
        local total = 0
        for _, listeners in pairs(self._listeners) do
            total = total + #listeners
        end
        return total
    end
end

-- 清理所有事件
function EventManager:Cleanup()
    for eventType, listeners in pairs(self._listeners) do
        for _, connection in ipairs(listeners) do
            connection:Disconnect()
        end
    end
    
    self._listeners = {}
    self._signals = {}
    self._stats = {
        totalEvents = 0,
        eventCounts = {},
        lastEventTime = 0
    }
    
    print("🧹 EventManager 已清理")
end

-- 調試信息
function EventManager:Debug()
    print("📊 EventManager 調試信息:")
    print("  總事件數:", self._stats.totalEvents)
    print("  事件類型數:", self:GetEventTypeCount())
    print("  總監聽器數:", self:GetListenerCount())
    print("  最後事件時間:", self._stats.lastEventTime)
    
    print("  各事件觸發次數:")
    for eventType, count in pairs(self._stats.eventCounts) do
        if count > 0 then
            print("    " .. eventType .. ":", count)
        end
    end
    
    print("  各事件監聽器數:")
    for eventType, listeners in pairs(self._listeners) do
        if #listeners > 0 then
            print("    " .. eventType .. ":", #listeners)
        end
    end
end

-- 便捷方法：玩家事件
function EventManager:FirePlayerEvent(eventType, player, data)
    local eventData = data or {}
    eventData.player = player
    eventData.userId = player.UserId
    eventData.playerName = player.Name
    return self:Fire(eventType, eventData)
end

-- 便捷方法：戰鬥事件
function EventManager:FireCombatEvent(eventType, attacker, target, data)
    local eventData = data or {}
    eventData.attacker = attacker
    eventData.target = target
    return self:Fire(eventType, eventData)
end

-- 便捷方法：寵物事件
function EventManager:FirePetEvent(eventType, player, petData, data)
    local eventData = data or {}
    eventData.player = player
    eventData.petData = petData
    return self:Fire(eventType, eventData)
end

-- 便捷方法：區域事件
function EventManager:FireZoneEvent(eventType, player, zoneId, data)
    local eventData = data or {}
    eventData.player = player
    eventData.zoneId = zoneId
    return self:Fire(eventType, eventData)
end

-- 便捷方法：經濟事件
function EventManager:FireEconomyEvent(eventType, player, currencyType, amount, data)
    local eventData = data or {}
    eventData.player = player
    eventData.currencyType = currencyType
    eventData.amount = amount
    return self:Fire(eventType, eventData)
end

return EventManager
