# PetSim99Lua 開發指南

## 📋 目錄
- [Rojo 使用說明](#rojo-使用說明)
- [開發工具介紹](#開發工具介紹)
- [注意事項](#注意事項)
- [最佳實踐](#最佳實踐)
- [常見問題](#常見問題)

## 🔧 Rojo 使用說明

### 啟動 Rojo 服務器
```bash
rojo serve
```
- 服務器會在 `http://localhost:34872/` 啟動
- 保持終端開啟，不要關閉

### 在 Roblox Studio 中連接
1. 安裝 Rojo 插件（從 Roblox 插件商店）
2. 點擊 Rojo 插件中的 "Connect" 按鈕
3. 輸入服務器地址：`localhost:34872`
4. 點擊 "Connect" 連接

### 同步注意事項
- ⚠️ **首次同步會覆蓋 Studio 中的現有內容**
- 建議在空白 Place 中進行首次同步
- 同步後，代碼更改會自動反映到 Studio 中

## 🛠️ 開發工具介紹

### 1. Promise - 非同步處理
**用途：** 處理非同步邏輯，取代傳統 callback

**引用方式：**
```lua
local Promise = require(game.ReplicatedStorage.Packages.Promise)
```

**基本用法：**
```lua
-- 創建 Promise
local promise = Promise.new(function(resolve, reject)
    wait(1)
    resolve("完成！")
end)

-- 處理結果
promise:andThen(function(result)
    print(result) -- 輸出：完成！
end):catch(function(error)
    warn("錯誤：", error)
end)
```

**注意事項：**
- 避免在 Promise 中使用 `wait()`，使用 `Promise.delay()` 代替
- 總是處理錯誤情況，使用 `:catch()`
- 不要在 Promise 中直接操作 UI，使用 `:andThen()` 回調

### 2. Signal - 事件通訊
**用途：** 本地模組間通訊，效能比 BindableEvent 更好

**引用方式：**
```lua
local Signal = require(game.ReplicatedStorage.Packages.Signal)
```

**基本用法：**
```lua
-- 創建信號
local mySignal = Signal.new()

-- 連接監聽器
local connection = mySignal:Connect(function(data)
    print("收到數據：", data)
end)

-- 發送信號
mySignal:Fire("Hello World!")

-- 斷開連接
connection:Disconnect()
```

**注意事項：**
- 記得在不需要時斷開連接，避免內存洩漏
- 不要在信號回調中進行耗時操作
- 使用 `:Wait()` 時要小心死鎖

### 3. ProfileService - 玩家數據管理
**用途：** 安全的玩家數據儲存，防止數據丟失和多開問題

**引用方式：**
```lua
local ProfileService = require(game.ReplicatedStorage.Packages.ProfileService)
```

**基本用法：**
```lua
-- 創建 ProfileStore
local ProfileStore = ProfileService.GetProfileStore(
    "PlayerData",
    {
        Cash = 0,
        Level = 1,
        Pets = {}
    }
)

-- 玩家加入時載入數據
local function PlayerAdded(player)
    local profile = ProfileStore:LoadProfileAsync("Player_" .. player.UserId)
    
    if profile ~= nil then
        profile:AddUserId(player.UserId)
        profile:Reconcile()
        
        if player.Parent == Players then
            -- 數據載入成功
            player.Data = profile.Data
        else
            profile:Release()
        end
    else
        -- 數據載入失敗，踢出玩家
        player:Kick("數據載入失敗，請重新加入")
    end
end
```

**重要注意事項：**
- ⚠️ **永遠不要直接修改 Profile.Data**，使用 ProfileService 提供的方法
- 玩家離開時必須調用 `profile:Release()`
- 使用 `:Reconcile()` 確保數據結構完整
- 定期保存數據，不要只依賴自動保存

### 4. TableUtil - 表格工具
**用途：** 高效處理表格操作，合併、過濾、映射等

**引用方式：**
```lua
local TableUtil = require(game.ReplicatedStorage.Packages.TableUtil)
```

**常用方法：**
```lua
-- 深拷貝
local copy = TableUtil.Copy(originalTable, true)

-- 合併表格
local merged = TableUtil.Assign({}, table1, table2)

-- 過濾
local filtered = TableUtil.Filter(myTable, function(value, key)
    return value > 10
end)

-- 映射
local mapped = TableUtil.Map(myTable, function(value)
    return value * 2
end)
```

## ⚠️ 注意事項

### Rojo 相關
1. **不要在 Studio 中直接編輯同步的腳本**
   - 所有更改都應該在外部編輯器中進行
   - Studio 中的更改會被 Rojo 覆蓋

2. **文件結構**
   - 保持 `src/` 目錄結構整潔
   - 不要移動 `Packages/` 目錄
   - 遵循 Roblox 服務命名規範

3. **同步問題**
   - 如果同步出現問題，重啟 Rojo 服務器
   - 檢查文件路徑是否正確
   - 確保沒有文件被其他程序鎖定

### 開發工具相關
1. **Promise**
   - 不要嵌套過多 Promise，使用 `Promise.all()` 或 `Promise.race()`
   - 避免在 Promise 中使用 `pcall()`，使用 Promise 的錯誤處理

2. **Signal**
   - 在模組被銷毀時清理所有 Signal 連接
   - 不要在 Signal 回調中創建新的 Signal

3. **ProfileService**
   - 測試環境和正式環境使用不同的 ProfileStore 名稱
   - 定期備份重要數據
   - 監控數據載入失敗率

4. **TableUtil**
   - 大型表格操作時注意性能
   - 深拷貝會消耗較多內存，謹慎使用

## 🎯 最佳實踐

### 代碼組織
```
src/
├── ReplicatedStorage/
│   ├── Modules/          # 共享模組
│   ├── Events/           # RemoteEvents
│   └── Packages/         # 第三方依賴
├── ServerScriptService/
│   ├── Services/         # 服務器服務
│   └── Modules/          # 服務器模組
├── StarterGui/
│   └── Client/           # 客戶端腳本
└── StarterPlayer/
    └── PlayerScripts/    # 玩家腳本
```

### 模組引用
```lua
-- 好的做法
local Promise = require(game.ReplicatedStorage.Packages.Promise)
local Signal = require(game.ReplicatedStorage.Packages.Signal)

-- 避免的做法
local Promise = require(script.Parent.Parent.Packages.Promise)
```

### 錯誤處理
```lua
-- 使用 Promise 處理異步操作
local function LoadPlayerData(player)
    return Promise.new(function(resolve, reject)
        local success, result = pcall(function()
            return ProfileStore:LoadProfileAsync("Player_" .. player.UserId)
        end)
        
        if success and result then
            resolve(result)
        else
            reject("數據載入失敗")
        end
    end)
end
```

## ❓ 常見問題

### Q: Rojo 連接失敗怎麼辦？
A: 
1. 檢查服務器是否正在運行
2. 確認端口號正確（通常是 34872）
3. 重啟 Rojo 服務器和 Studio

### Q: 為什麼我的更改沒有同步到 Studio？
A:
1. 檢查文件是否保存
2. 確認文件在正確的目錄中
3. 查看 Rojo 終端是否有錯誤信息

### Q: ProfileService 數據丟失怎麼辦？
A:
1. 檢查是否正確調用了 `profile:Release()`
2. 確認網絡連接穩定
3. 查看 Studio 輸出是否有錯誤信息

### Q: 如何調試 Promise？
A:
```lua
promise:andThen(function(result)
    print("成功：", result)
    return result
end):catch(function(error)
    warn("錯誤：", error)
    warn(debug.traceback())
end)
```

---

**最後更新：** 2025-07-28
**版本：** 1.0.0
