-- 區域控制器 - 處理客戶端區域相關邏輯
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local ZoneConfig = require(game.ReplicatedStorage.Configuration.ZoneConfig)

local ZoneController = Knit.CreateController({
    Name = "ZoneController"
})

function ZoneController:KnitStart()
    print("🗺️ ZoneController 啟動")
    
    -- 連接到區域服務
    self:_connectToZoneService()
    
    -- 設置區域 UI 事件
    self:_setupZoneUI()
    
    -- 初始化區域狀態
    self:_initializeZoneState()
end

-- 連接到區域服務
function ZoneController:_connectToZoneService()
    local ZoneService = Knit.GetService("ZoneService")
    if not ZoneService then
        warn("❌ 無法連接到 ZoneService")
        return
    end
    
    -- 監聽區域事件
    ZoneService.OnZoneEntered:Connect(function(data)
        self:_onZoneEntered(data)
    end)
    
    ZoneService.OnZoneExited:Connect(function(data)
        self:_onZoneExited(data)
    end)
    
    ZoneService.OnZoneChanged:Connect(function(data)
        self:_onZoneChanged(data)
    end)
    
    ZoneService.OnTeleportCompleted:Connect(function(data)
        self:_onTeleportCompleted(data)
    end)
    
    ZoneService.OnZoneUnlocked:Connect(function(data)
        self:_onZoneUnlocked(data)
    end)
    
    self._zoneService = ZoneService
    print("✅ 已連接到 ZoneService")
end

-- 設置區域 UI 事件
function ZoneController:_setupZoneUI()
    -- 這裡可以設置區域相關的 UI 事件
    -- 例如：地圖界面、傳送界面等
end

-- 初始化區域狀態
function ZoneController:_initializeZoneState()
    self._currentZone = nil
    self._availableZones = {}
    self._zoneHistory = {}
    
    -- 獲取當前區域
    if self._zoneService then
        self._zoneService:GetCurrentZone():andThen(function(result)
            if result.success then
                self._currentZone = result.zoneId
                print("📍 當前區域:", result.zoneConfig.name)
            end
        end):catch(function(err)
            warn("❌ 獲取當前區域失敗:", err)
        end)
        
        -- 獲取可用區域
        self._zoneService:GetAvailableZones():andThen(function(result)
            if result.success then
                self._availableZones = result.availableZones
                print("🗺️ 可用區域數量:", #result.availableZones)
            end
        end):catch(function(err)
            warn("❌ 獲取可用區域失敗:", err)
        end)
    end
end

-- 玩家進入區域事件
function ZoneController:_onZoneEntered(data)
    self._currentZone = data.zoneId
    table.insert(self._zoneHistory, {
        zoneId = data.zoneId,
        enterTime = data.timestamp,
        action = "enter"
    })
    
    print("🚪 進入區域:", data.zoneName)
    
    -- 更新音樂
    self:_updateZoneMusic(data.zoneId)
    
    -- 更新 UI
    self:_updateZoneUI(data)
    
    -- 顯示區域通知
    self:_showZoneNotification("進入 " .. data.zoneName, "info")
    
    -- 觸發區域特定邏輯
    self:_handleZoneSpecificLogic(data.zoneId, "enter")
end

-- 玩家離開區域事件
function ZoneController:_onZoneExited(data)
    table.insert(self._zoneHistory, {
        zoneId = data.zoneId,
        exitTime = data.timestamp,
        action = "exit"
    })
    
    print("🚪 離開區域:", data.zoneName)
    
    -- 清理區域特定 UI
    self:_cleanupZoneUI(data.zoneId)
end

-- 區域變更事件
function ZoneController:_onZoneChanged(data)
    print("🔄 區域變更:", data.fromZone, "->", data.toZone)
end

-- 傳送完成事件
function ZoneController:_onTeleportCompleted(data)
    print("✈️ 傳送完成到:", data.zoneName)
    
    -- 顯示傳送成功通知
    self:_showZoneNotification("傳送到 " .. data.zoneName .. " 完成", "success")
    
    -- 如果有傳送費用，顯示費用信息
    if data.cost and data.cost.coins > 0 then
        self:_showZoneNotification("花費 " .. data.cost.coins .. " 金幣", "info")
    end
end

-- 區域解鎖事件
function ZoneController:_onZoneUnlocked(data)
    print("🔓 區域解鎖:", data.zoneName)
    
    -- 顯示解鎖通知
    self:_showZoneNotification("新區域解鎖：" .. data.zoneName, "success")
    
    -- 更新可用區域列表
    self:_refreshAvailableZones()
end

-- 更新區域音樂
function ZoneController:_updateZoneMusic(zoneId)
    local AudioController = Knit.GetController("AudioController")
    if AudioController then
        AudioController:changeZoneMusic(zoneId)
    end
end

-- 更新區域 UI
function ZoneController:_updateZoneUI(zoneData)
    -- 更新狀態管理器
    local success, StateManager = pcall(function()
        return require(script.Parent.Parent.UI.StateManager)
    end)
    
    if success and StateManager then
        StateManager:UpdateUI({
            currentZone = zoneData.zoneId,
            zoneName = zoneData.zoneName,
            zoneType = zoneData.zoneType
        })
    end
    
    -- 更新 UI 控制器
    local UIController = Knit.GetController("UIController")
    if UIController then
        -- 可以在這裡更新 UI 相關狀態
    end
end

-- 清理區域 UI
function ZoneController:_cleanupZoneUI(zoneId)
    -- 清理區域特定的 UI 元素
end

-- 顯示區域通知
function ZoneController:_showZoneNotification(message, type)
    local UIController = Knit.GetController("UIController")
    if UIController then
        UIController:ShowNotification(message, type)
    end
end

-- 處理區域特定邏輯
function ZoneController:_handleZoneSpecificLogic(zoneId, action)
    local zoneConfig = ZoneConfig:GetZone(zoneId)
    if not zoneConfig then
        return
    end
    
    if action == "enter" then
        -- 進入區域時的客戶端邏輯
        if zoneConfig.type == ZoneConfig.ZoneTypes.SAFE then
            self:_handleSafeZoneEntry(zoneId)
        elseif zoneConfig.type == ZoneConfig.ZoneTypes.HUNTING then
            self:_handleHuntingZoneEntry(zoneId)
        elseif zoneConfig.type == ZoneConfig.ZoneTypes.PVP then
            self:_handlePvPZoneEntry(zoneId)
        elseif zoneConfig.type == ZoneConfig.ZoneTypes.EVENT then
            self:_handleEventZoneEntry(zoneId)
        end
    end
end

-- 處理安全區域進入
function ZoneController:_handleSafeZoneEntry(zoneId)
    print("🛡️ 進入安全區域")
    
    -- 顯示安全區域提示
    self:_showZoneNotification("您現在處於安全區域", "info")
    
    -- 可以在這裡添加安全區域特定的 UI
end

-- 處理狩獵區域進入
function ZoneController:_handleHuntingZoneEntry(zoneId)
    print("🏹 進入狩獵區域")
    
    -- 顯示狩獵區域提示
    self:_showZoneNotification("小心！這裡有怪物出沒", "warning")
    
    -- 可以在這裡添加狩獵相關的 UI
end

-- 處理 PvP 區域進入
function ZoneController:_handlePvPZoneEntry(zoneId)
    print("⚔️ 進入 PvP 區域")
    
    -- 顯示 PvP 警告
    self:_showZoneNotification("警告：您已進入 PvP 區域！", "warning")
    
    -- 可以在這裡添加 PvP 相關的 UI
end

-- 處理事件區域進入
function ZoneController:_handleEventZoneEntry(zoneId)
    print("🎉 進入事件區域")
    
    -- 顯示事件信息
    self:_showZoneNotification("歡迎來到活動廣場！", "success")
    
    -- 可以在這裡添加事件相關的 UI
end

-- 刷新可用區域列表
function ZoneController:_refreshAvailableZones()
    if self._zoneService then
        self._zoneService:GetAvailableZones():andThen(function(result)
            if result.success then
                self._availableZones = result.availableZones
                print("🔄 已刷新可用區域列表")
            end
        end):catch(function(err)
            warn("❌ 刷新可用區域失敗:", err)
        end)
    end
end

-- 請求傳送到區域
function ZoneController:teleportToZone(zoneId)
    if not self._zoneService then
        warn("❌ ZoneService 未連接")
        return
    end
    
    self._zoneService:TeleportToZone(zoneId):andThen(function(result)
        if result.success then
            print("✈️ 傳送請求成功")
        else
            warn("❌ 傳送失敗:", result.error)
            self:_showZoneNotification("傳送失敗：" .. result.error, "error")
        end
    end):catch(function(err)
        warn("❌ 傳送請求錯誤:", err)
        self:_showZoneNotification("傳送請求失敗", "error")
    end)
end

-- 獲取區域信息
function ZoneController:getZoneInfo(zoneId)
    if not self._zoneService then
        return nil
    end
    
    return self._zoneService:GetZoneInfo(zoneId)
end

-- 獲取當前區域
function ZoneController:getCurrentZone()
    return self._currentZone
end

-- 獲取可用區域
function ZoneController:getAvailableZones()
    return self._availableZones
end

-- 獲取區域歷史
function ZoneController:getZoneHistory()
    return self._zoneHistory
end

-- 檢查是否可以傳送到區域
function ZoneController:canTeleportToZone(zoneId)
    for _, zone in ipairs(self._availableZones) do
        if zone.zoneId == zoneId then
            return true, zone.teleportCost
        end
    end
    return false, "區域未解鎖或不可用"
end

-- 獲取區域配置
function ZoneController:getZoneConfig(zoneId)
    return ZoneConfig:GetZone(zoneId)
end

-- 清理控制器
function ZoneController:cleanup()
    self._currentZone = nil
    self._availableZones = {}
    self._zoneHistory = {}
    self._zoneService = nil
end

return ZoneController
