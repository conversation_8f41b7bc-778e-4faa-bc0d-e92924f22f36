-- 服務端主啟動腳本
-- 初始化 Matter ECS 世界和 Knit 服務

print("🚀 PetSim99Lua 服務端啟動中...")

-- 引入依賴
local Knit = require(game.ReplicatedStorage.Packages.Knit)

-- 引入配置
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)

-- 創建世界管理器
local WorldManager = require(script.WorldManager)
local worldManager = WorldManager.new()

-- 存儲世界管理器引用供服務使用
_G.WorldManager = worldManager
_G.ECSWorld = worldManager:getWorld()

print("✅ ECS 世界管理器已創建")

-- 添加所有 Knit 服務
local servicesFolder = script.Services
for _, serviceModule in pairs(servicesFolder:GetChildren()) do
    if serviceModule:IsA("ModuleScript") then
        require(serviceModule)
        print("📦 已載入服務:", serviceModule.Name)
    end
end

-- 啟動 Knit
Knit.Start():andThen(function()
    print("🌐 Knit 服務端已啟動")

    -- 啟動 ECS 世界管理器
    worldManager:start()
    print("⚙️ ECS 世界管理器已啟動")

end):catch(function(err)
    error("❌ Knit 啟動失敗: " .. tostring(err))
end)

-- 玩家連接處理現在由 WorldManager 負責

-- 錯誤處理
local function handleError(err)
    warn("🚨 服務端錯誤:", err)
    warn(debug.traceback())
end

-- 全局錯誤捕獲
local success, err = pcall(function()
    -- 主要初始化邏輯已在上面完成
end)

if not success then
    handleError(err)
end

print("🎮 PetSim99Lua 服務端啟動完成！")
print("📊 遊戲版本:", GameConfig.VERSION)
print("👥 最大玩家數:", GameConfig.MAX_PLAYERS)
