-- 扭蛋系統 - 純扭蛋獲得機制
local GachaSystem = {}

-- 依賴
local MonsterConfig = require(game.ReplicatedStorage.Configuration.MonsterConfig)
local MonsterManager = require(game.ReplicatedStorage.Shared.MonsterManager)
-- 移除 Utils 依賴

-- 扭蛋統計
GachaSystem.Stats = {
    totalRolls = 0,
    rollsByType = {},
    rollsByRarity = {},
    totalSpent = {
        coins = 0,
        gems = 0,
        robux = 0
    }
}

-- 初始化扭蛋系統
function GachaSystem:Initialize()
    print("🎰 初始化扭蛋系統...")
    
    -- 重置統計
    self:ResetStats()
    
    print("✅ 扭蛋系統初始化完成")
end

-- 執行扭蛋
function GachaSystem:Roll(gachaType, player)
    local gachaConfig = MonsterConfig.GACHA[gachaType]
    if not gachaConfig then
        warn("❌ 未知的扭蛋類型:", gachaType)
        return nil
    end
    
    -- 檢查玩家貨幣 (這裡假設有玩家數據系統)
    local canAfford = self:CheckPlayerCurrency(player, gachaConfig.currency, gachaConfig.cost)
    if not canAfford then
        return {
            success = false,
            error = "貨幣不足",
            required = gachaConfig.cost,
            currency = gachaConfig.currency
        }
    end
    
    -- 執行扭蛋
    local result = self:PerformRoll(gachaType, gachaConfig)
    
    if result then
        -- 扣除貨幣
        self:DeductCurrency(player, gachaConfig.currency, gachaConfig.cost)
        
        -- 更新統計
        self:UpdateStats(gachaType, result.rarity, gachaConfig.currency, gachaConfig.cost)
        
        -- 添加成功標記
        result.success = true
        result.timestamp = tick()
        result.player = player.Name
        
        print(string.format("🎰 %s 扭蛋獲得: %s (%s級)", 
              player.Name, result.name, result.rarity))
    end
    
    return result
end

-- 執行扭蛋邏輯
function GachaSystem:PerformRoll(gachaType, gachaConfig)
    -- 根據機率選擇稀有度
    local selectedRarity = self:RollRarity(gachaConfig.rates)
    
    -- 從該稀有度中隨機選擇怪物
    local monsters = MonsterManager:GetMonstersByRarity(selectedRarity)
    if #monsters == 0 then
        -- 如果該稀有度沒有怪物，降級到普通
        monsters = MonsterManager:GetMonstersByRarity("COMMON")
        selectedRarity = "COMMON"
    end
    
    if #monsters == 0 then
        warn("❌ 沒有可用的怪物")
        return nil
    end
    
    local selectedMonster = monsters[math.random(#monsters)]
    local monsterData = MonsterManager:GetMonsterData(selectedMonster)
    
    return {
        name = selectedMonster,
        rarity = selectedRarity,
        data = monsterData,
        gachaType = gachaType,
        cost = gachaConfig.cost,
        currency = gachaConfig.currency,
        stats = monsterData.stats,
        zone = monsterData.zone
    }
end

-- 根據機率選擇稀有度
function GachaSystem:RollRarity(rates)
    local roll = math.random(100)
    local cumulative = 0
    
    -- 按照稀有度順序檢查
    local rarityOrder = {"COMMON", "UNCOMMON", "RARE", "EPIC", "LEGENDARY"}
    
    for _, rarity in ipairs(rarityOrder) do
        local rate = rates[rarity] or 0
        cumulative = cumulative + rate
        if roll <= cumulative then
            return rarity
        end
    end
    
    -- 默認返回普通
    return "COMMON"
end

-- 批量扭蛋
function GachaSystem:RollMultiple(gachaType, count, player)
    local results = {}
    local totalCost = 0
    local gachaConfig = MonsterConfig.GACHA[gachaType]
    
    if not gachaConfig then
        return {
            success = false,
            error = "未知的扭蛋類型"
        }
    end
    
    totalCost = gachaConfig.cost * count
    
    -- 檢查總費用
    local canAfford = self:CheckPlayerCurrency(player, gachaConfig.currency, totalCost)
    if not canAfford then
        return {
            success = false,
            error = "貨幣不足",
            required = totalCost,
            currency = gachaConfig.currency
        }
    end
    
    -- 執行多次扭蛋
    for i = 1, count do
        local result = self:PerformRoll(gachaType, gachaConfig)
        if result then
            result.rollNumber = i
            table.insert(results, result)
        end
    end
    
    -- 扣除總費用
    self:DeductCurrency(player, gachaConfig.currency, totalCost)
    
    -- 批量更新統計
    for _, result in ipairs(results) do
        self:UpdateStats(gachaType, result.rarity, gachaConfig.currency, gachaConfig.cost)
    end
    
    return {
        success = true,
        results = results,
        count = #results,
        totalCost = totalCost,
        currency = gachaConfig.currency,
        timestamp = tick(),
        player = player.Name
    }
end

-- 檢查玩家貨幣 (模擬)
function GachaSystem:CheckPlayerCurrency(player, currency, amount)
    -- 這裡應該連接到實際的玩家數據系統
    -- 現在返回 true 用於測試
    return true
end

-- 扣除貨幣 (模擬)
function GachaSystem:DeductCurrency(player, currency, amount)
    -- 這裡應該連接到實際的玩家數據系統
    print(string.format("💰 扣除 %s %d %s", player.Name, amount, currency))
end

-- 更新統計
function GachaSystem:UpdateStats(gachaType, rarity, currency, cost)
    self.Stats.totalRolls = self.Stats.totalRolls + 1
    
    -- 按類型統計
    self.Stats.rollsByType[gachaType] = (self.Stats.rollsByType[gachaType] or 0) + 1
    
    -- 按稀有度統計
    self.Stats.rollsByRarity[rarity] = (self.Stats.rollsByRarity[rarity] or 0) + 1
    
    -- 花費統計
    self.Stats.totalSpent[currency] = (self.Stats.totalSpent[currency] or 0) + cost
end

-- 獲取扭蛋機率顯示
function GachaSystem:GetRateDisplay(gachaType)
    local gachaConfig = MonsterConfig.GACHA[gachaType]
    if not gachaConfig then
        return nil
    end
    
    local display = {
        type = gachaType,
        cost = gachaConfig.cost,
        currency = gachaConfig.currency,
        rates = {}
    }
    
    for rarity, rate in pairs(gachaConfig.rates) do
        table.insert(display.rates, {
            rarity = rarity,
            name = MonsterConfig.RARITY[rarity].name,
            rate = rate,
            color = MonsterConfig.RARITY[rarity].color
        })
    end
    
    -- 按機率排序
    table.sort(display.rates, function(a, b)
        return a.rate > b.rate
    end)
    
    return display
end

-- 獲取保底系統信息
function GachaSystem:GetPityInfo(player, gachaType)
    -- 這裡可以實現保底系統
    -- 例如：連續多少次沒有獲得高稀有度就保證下次獲得
    return {
        enabled = false, -- 暫時禁用保底
        currentCount = 0,
        maxCount = 100,
        guaranteedRarity = "EPIC"
    }
end

-- 獲取扭蛋歷史
function GachaSystem:GetRollHistory(player, limit)
    -- 這裡應該從數據庫獲取玩家的扭蛋歷史
    -- 現在返回空數組
    return {}
end

-- 重置統計
function GachaSystem:ResetStats()
    self.Stats = {
        totalRolls = 0,
        rollsByType = {},
        rollsByRarity = {},
        totalSpent = {
            coins = 0,
            gems = 0,
            robux = 0
        }
    }
end

-- 獲取統計信息
function GachaSystem:GetStats()
    -- 返回統計數據的副本
    return {
        totalRolls = self.Stats.totalRolls,
        rollsByType = self.Stats.rollsByType,
        rollsByRarity = self.Stats.rollsByRarity,
        totalSpent = self.Stats.totalSpent
    }
end

-- 計算稀有度機率
function GachaSystem:CalculateActualRates()
    local actualRates = {}
    
    for rarity, count in pairs(self.Stats.rollsByRarity) do
        if self.Stats.totalRolls > 0 then
            actualRates[rarity] = (count / self.Stats.totalRolls) * 100
        else
            actualRates[rarity] = 0
        end
    end
    
    return actualRates
end

-- 模擬扭蛋 (用於測試)
function GachaSystem:Simulate(gachaType, rollCount)
    print(string.format("🎲 模擬 %s 扭蛋 %d 次", gachaType, rollCount))
    
    local gachaConfig = MonsterConfig.GACHA[gachaType]
    if not gachaConfig then
        print("❌ 無效的扭蛋類型")
        return
    end
    
    local results = {}
    for i = 1, rollCount do
        local result = self:PerformRoll(gachaType, gachaConfig)
        if result then
            table.insert(results, result)
            self:UpdateStats(gachaType, result.rarity, gachaConfig.currency, gachaConfig.cost)
        end
    end
    
    -- 顯示結果
    print("📊 模擬結果:")
    local rarityCount = {}
    for _, result in ipairs(results) do
        rarityCount[result.rarity] = (rarityCount[result.rarity] or 0) + 1
    end
    
    for rarity, count in pairs(rarityCount) do
        local percentage = (count / rollCount) * 100
        print(string.format("  %s: %d 次 (%.1f%%)", 
              MonsterConfig.RARITY[rarity].name, count, percentage))
    end
    
    return results
end

-- 調試信息
function GachaSystem:Debug()
    print("🎰 扭蛋系統調試信息:")
    print("  總扭蛋次數:", self.Stats.totalRolls)
    
    print("  按類型統計:")
    for gachaType, count in pairs(self.Stats.rollsByType) do
        print(string.format("    %s: %d 次", gachaType, count))
    end
    
    print("  按稀有度統計:")
    for rarity, count in pairs(self.Stats.rollsByRarity) do
        local percentage = self.Stats.totalRolls > 0 and (count / self.Stats.totalRolls) * 100 or 0
        print(string.format("    %s: %d 次 (%.1f%%)", 
              MonsterConfig.RARITY[rarity].name, count, percentage))
    end
    
    print("  總花費:")
    for currency, amount in pairs(self.Stats.totalSpent) do
        print(string.format("    %s: %d", currency, amount))
    end
end

return GachaSystem
