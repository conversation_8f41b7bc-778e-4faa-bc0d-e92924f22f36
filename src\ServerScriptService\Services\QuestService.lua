-- 任務服務 - 處理每日、每週、每月任務系統
local Knit = require(game.ReplicatedStorage.Packages.Knit)
local Components = require(game.ReplicatedStorage.Components)
local GameConfig = require(game.ReplicatedStorage.Configuration.GameConfig)
local Utils = require(game.ReplicatedStorage.Shared.Utils)

local QuestService = Knit.CreateService({
    Name = "QuestService",
    Client = {
        -- 客戶端可調用的方法
        GetQuests = Knit.CreateSignal(),
        ClaimQuestReward = Knit.CreateSignal(),
        GetQuestProgress = Knit.CreateSignal(),
        RefreshDailyQuests = Knit.CreateSignal(),
        
        -- 客戶端事件
        OnQuestCompleted = Knit.CreateSignal(),
        OnQuestProgressUpdated = Knit.CreateSignal(),
        OnQuestRewardClaimed = Knit.CreateSignal(),
        OnQuestsRefreshed = Knit.CreateSignal()
    }
})

function QuestService:KnitInit()
    self._worldManager = _G.WorldManager
    self._world = _G.ECSWorld
    
    -- 任務模板
    self._questTemplates = {
        daily = {
            {
                id = "defeat_monsters",
                name = "擊敗怪物",
                description = "擊敗 {target} 隻怪物",
                type = "defeat",
                target = 10,
                rewards = { coins = 200, experience = 50 }
            },
            {
                id = "gain_experience",
                name = "獲得經驗",
                description = "獲得 {target} 點經驗值",
                type = "experience",
                target = 500,
                rewards = { coins = 150, gems = 2 }
            },
            {
                id = "roll_eggs",
                name = "扭蛋召喚",
                description = "進行 {target} 次扭蛋",
                type = "gacha",
                target = 5,
                rewards = { coins = 300, gems = 3 }
            },
            {
                id = "level_up_pet",
                name = "寵物升級",
                description = "讓寵物升級 {target} 次",
                type = "pet_level",
                target = 3,
                rewards = { coins = 250, gems = 5 }
            }
        },
        weekly = {
            {
                id = "weekly_defeat",
                name = "週挑戰",
                description = "本週擊敗 {target} 隻怪物",
                type = "defeat",
                target = 100,
                rewards = { coins = 2000, gems = 20, experience = 500 }
            },
            {
                id = "weekly_collection",
                name = "收集大師",
                description = "本週獲得 {target} 隻新寵物",
                type = "collect",
                target = 10,
                rewards = { coins = 1500, gems = 25 }
            }
        },
        monthly = {
            {
                id = "monthly_master",
                name = "月度大師",
                description = "本月完成 {target} 個每日任務",
                type = "daily_complete",
                target = 20,
                rewards = { coins = 10000, gems = 100, experience = 2000 }
            }
        }
    }
    
    print("📋 QuestService 初始化完成")
end

function QuestService:KnitStart()
    print("🎮 QuestService 啟動")
    
    -- 設置定時器檢查任務重置
    self:setupQuestResetTimers()
end

-- 設置任務重置定時器
function QuestService:setupQuestResetTimers()
    local RunService = game:GetService("RunService")
    
    -- 每分鐘檢查一次任務重置
    RunService.Heartbeat:Connect(function()
        if tick() % 60 < 1 then -- 大約每分鐘執行一次
            self:checkQuestResets()
        end
    end)
end

-- 檢查任務重置
function QuestService:checkQuestResets()
    local currentTime = tick()
    local currentDate = os.date("*t", currentTime)
    
    -- 檢查每日任務重置 (每天 00:00)
    if currentDate.hour == 0 and currentDate.min == 0 then
        self:resetDailyQuests()
    end
    
    -- 檢查每週任務重置 (每週一 00:00)
    if currentDate.wday == 2 and currentDate.hour == 0 and currentDate.min == 0 then
        self:resetWeeklyQuests()
    end
    
    -- 檢查每月任務重置 (每月1號 00:00)
    if currentDate.day == 1 and currentDate.hour == 0 and currentDate.min == 0 then
        self:resetMonthlyQuests()
    end
end

-- 客戶端方法：獲取任務
function QuestService.Client:GetQuests(player, questType)
    return self.Server:GetQuests(player, questType)
end

function QuestService:GetQuests(player, questType)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local questProgress = self._world:get(playerEntity, Components.QuestProgressComponent)
    if not questProgress then
        -- 初始化任務進度
        questProgress = self:initializePlayerQuests(playerEntity)
    end
    
    if questType then
        return {
            success = true,
            quests = questProgress[questType .. "Progress"] or {}
        }
    else
        return {
            success = true,
            quests = {
                daily = questProgress.dailyProgress or {},
                weekly = questProgress.weeklyProgress or {},
                monthly = questProgress.monthlyProgress or {}
            }
        }
    end
end

-- 初始化玩家任務
function QuestService:initializePlayerQuests(playerEntity)
    local questProgress = {
        activeQuests = {},
        completedQuests = {},
        dailyProgress = self:generateDailyQuests(),
        weeklyProgress = self:generateWeeklyQuests(),
        monthlyProgress = self:generateMonthlyQuests()
    }
    
    self._world:insert(playerEntity, Components.QuestProgressComponent(questProgress))
    
    return questProgress
end

-- 生成每日任務
function QuestService:generateDailyQuests()
    local dailyQuests = {}
    local templates = self._questTemplates.daily
    
    -- 隨機選擇3個每日任務
    local selectedTemplates = {}
    for i = 1, 3 do
        local template = Utils.randomChoice(templates)
        if template and not selectedTemplates[template.id] then
            selectedTemplates[template.id] = true
            
            local quest = Utils.deepCopy(template)
            quest.progress = 0
            quest.completed = false
            quest.claimed = false
            quest.startTime = tick()
            
            table.insert(dailyQuests, quest)
        end
    end
    
    return dailyQuests
end

-- 生成每週任務
function QuestService:generateWeeklyQuests()
    local weeklyQuests = {}
    local templates = self._questTemplates.weekly
    
    for _, template in ipairs(templates) do
        local quest = Utils.deepCopy(template)
        quest.progress = 0
        quest.completed = false
        quest.claimed = false
        quest.startTime = tick()
        
        table.insert(weeklyQuests, quest)
    end
    
    return weeklyQuests
end

-- 生成每月任務
function QuestService:generateMonthlyQuests()
    local monthlyQuests = {}
    local templates = self._questTemplates.monthly
    
    for _, template in ipairs(templates) do
        local quest = Utils.deepCopy(template)
        quest.progress = 0
        quest.completed = false
        quest.claimed = false
        quest.startTime = tick()
        
        table.insert(monthlyQuests, quest)
    end
    
    return monthlyQuests
end

-- 客戶端方法：領取任務獎勵
function QuestService.Client:ClaimQuestReward(player, questType, questId)
    return self.Server:ClaimQuestReward(player, questType, questId)
end

function QuestService:ClaimQuestReward(player, questType, questId)
    local playerEntity = self._worldManager:findPlayerEntity(player.UserId)
    if not playerEntity then
        return { success = false, error = "玩家實體未找到" }
    end
    
    local questProgress = self._world:get(playerEntity, Components.QuestProgressComponent)
    if not questProgress then
        return { success = false, error = "任務數據未找到" }
    end
    
    local questList = questProgress[questType .. "Progress"]
    if not questList then
        return { success = false, error = "無效的任務類型" }
    end
    
    -- 查找任務
    local quest = nil
    for _, q in ipairs(questList) do
        if q.id == questId then
            quest = q
            break
        end
    end
    
    if not quest then
        return { success = false, error = "任務不存在" }
    end
    
    if not quest.completed then
        return { success = false, error = "任務未完成" }
    end
    
    if quest.claimed then
        return { success = false, error = "獎勵已領取" }
    end
    
    -- 給予獎勵
    local rewardResult = self:giveQuestRewards(playerEntity, quest.rewards)
    if not rewardResult.success then
        return rewardResult
    end
    
    -- 標記為已領取
    quest.claimed = true
    
    -- 更新任務進度
    self._world:insert(playerEntity, Components.QuestProgressComponent(questProgress))
    
    -- 通知客戶端
    self.Client.OnQuestRewardClaimed:Fire(player, {
        questType = questType,
        questId = questId,
        questName = quest.name,
        rewards = quest.rewards,
        timestamp = tick()
    })
    
    print("🎁 任務獎勵領取:", player.Name, quest.name)
    
    return {
        success = true,
        rewards = quest.rewards
    }
end

-- 給予任務獎勵
function QuestService:giveQuestRewards(playerEntity, rewards)
    local currency = self._world:get(playerEntity, Components.CurrencyComponent)
    if not currency then
        return { success = false, error = "貨幣數據未找到" }
    end
    
    local newCurrency = Utils.deepCopy(currency)
    
    if rewards.coins then
        newCurrency.coins = newCurrency.coins + rewards.coins
    end
    
    if rewards.gems then
        newCurrency.gems = newCurrency.gems + rewards.gems
    end
    
    if rewards.experience then
        newCurrency.experience = newCurrency.experience + rewards.experience
        
        -- 給英雄增加經驗值
        local LevelingSystem = self._worldManager:getSystem("LevelingSystem")
        if LevelingSystem then
            LevelingSystem:giveHeroExperience(self._world, playerEntity, rewards.experience)
        end
    end
    
    self._world:insert(playerEntity, Components.CurrencyComponent(newCurrency))
    
    return { success = true }
end

-- 更新任務進度
function QuestService:updateQuestProgress(playerEntity, progressType, amount)
    local questProgress = self._world:get(playerEntity, Components.QuestProgressComponent)
    if not questProgress then
        return
    end
    
    local updated = false
    
    -- 更新每日任務
    for _, quest in ipairs(questProgress.dailyProgress) do
        if quest.type == progressType and not quest.completed then
            quest.progress = quest.progress + amount
            if quest.progress >= quest.target then
                quest.progress = quest.target
                quest.completed = true
                updated = true
                
                -- 通知任務完成
                local player = game.Players:GetPlayerByUserId(
                    self._world:get(playerEntity, Components.PlayerComponent).userId
                )
                if player then
                    self.Client.OnQuestCompleted:Fire(player, {
                        questType = "daily",
                        quest = quest,
                        timestamp = tick()
                    })
                end
            else
                updated = true
            end
        end
    end
    
    -- 更新每週任務
    for _, quest in ipairs(questProgress.weeklyProgress) do
        if quest.type == progressType and not quest.completed then
            quest.progress = quest.progress + amount
            if quest.progress >= quest.target then
                quest.progress = quest.target
                quest.completed = true
                updated = true
            else
                updated = true
            end
        end
    end
    
    -- 更新每月任務
    for _, quest in ipairs(questProgress.monthlyProgress) do
        if quest.type == progressType and not quest.completed then
            quest.progress = quest.progress + amount
            if quest.progress >= quest.target then
                quest.progress = quest.target
                quest.completed = true
                updated = true
            else
                updated = true
            end
        end
    end
    
    if updated then
        self._world:insert(playerEntity, Components.QuestProgressComponent(questProgress))
        
        -- 通知進度更新
        local player = game.Players:GetPlayerByUserId(
            self._world:get(playerEntity, Components.PlayerComponent).userId
        )
        if player then
            self.Client.OnQuestProgressUpdated:Fire(player, {
                progressType = progressType,
                amount = amount,
                timestamp = tick()
            })
        end
    end
end

-- 重置每日任務
function QuestService:resetDailyQuests()
    for entityId, questProgress in self._world:query(Components.QuestProgressComponent) do
        questProgress.dailyProgress = self:generateDailyQuests()
        self._world:insert(entityId, Components.QuestProgressComponent(questProgress))
    end
    
    print("🔄 每日任務已重置")
end

-- 重置每週任務
function QuestService:resetWeeklyQuests()
    for entityId, questProgress in self._world:query(Components.QuestProgressComponent) do
        questProgress.weeklyProgress = self:generateWeeklyQuests()
        self._world:insert(entityId, Components.QuestProgressComponent(questProgress))
    end
    
    print("🔄 每週任務已重置")
end

-- 重置每月任務
function QuestService:resetMonthlyQuests()
    for entityId, questProgress in self._world:query(Components.QuestProgressComponent) do
        questProgress.monthlyProgress = self:generateMonthlyQuests()
        self._world:insert(entityId, Components.QuestProgressComponent(questProgress))
    end
    
    print("🔄 每月任務已重置")
end

return QuestService
